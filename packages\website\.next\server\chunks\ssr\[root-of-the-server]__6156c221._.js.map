{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/webs/apiflow/packages/website/src/components/sections/HeroSection.tsx"], "sourcesContent": ["import Link from 'next/link';\nimport { WifiOff, Users, Shield, Download, Gift, Globe } from 'lucide-react';\nimport { useTranslations } from 'next-intl';\n\nexport default function HeroSection() {\n  const t = useTranslations();\n  const features = [\n    { icon: WifiOff, title: t('离线运行'), description: t('除协作外可以使用所有功能') },\n    { icon: Users, title: t('团队协作'), description: t('我们提供实时协作功能') },\n    { icon: Shield, title: t('本地部署'), description: t('所有功能支持Docker本地部署') },\n    { icon: Download, title: t('数据自由'), description: t('可随时导出数据到其他工具') },\n    { icon: Gift, title: t('完全免费'), description: t('所有核心功能永久免费使用') },\n    { icon: Globe, title: t('协议支持'), description: t('支持HTTP、WebSocket、GraphQL等协议') },\n  ];\n\n  return (\n    <section className=\"relative pt-20 pb-16 sm:pt-24 sm:pb-20 lg:pt-32 lg:pb-28\">\n      {/* 背景渐变 */}\n      <div className=\"absolute inset-0 bg-gradient-to-br from-blue-50 via-white to-purple-50\"></div>\n\n      <div className=\"relative mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\">\n          {/* 左侧 - 内容 */}\n          <div className=\"text-left\">\n            {/* 徽章 */}\n            <div className=\"inline-flex items-center rounded-full bg-blue-100 px-4 py-2 text-sm font-medium text-blue-800 mb-8\">\n              {t('🚀 新功能：高级 API 测试功能现已上线')}\n            </div>\n\n            {/* 主标题 */}\n            <h1 className=\"text-4xl font-bold tracking-tight text-gray-900 sm:text-5xl lg:text-6xl\">\n              <span>{t('Postman 和 Apifox 的')}{' '}</span>\n              <span className=\"gradient-text\">{t('免费开源替代品')}</span>\n            </h1>\n\n            {/* 副标题 */}\n            <p className=\"mt-6 max-w-2xl text-lg leading-8 text-gray-600 sm:text-xl\">\n              <span>{t('在 Vibe Coding 的支持下，个人开发者或小团队同样能够构建媲美企业级应用的产品，欢迎 Star，支持我们走得更远')}</span>\n            </p>\n\n            {/* CTA按钮 */}\n            <div className=\"mt-10 flex flex-col sm:flex-row items-start gap-4\">\n              <Link\n                href=\"https://github.com/trueleaf/apiflow/releases\"\n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n                className=\"inline-flex items-center rounded-lg bg-blue-600 px-8 py-3 text-base font-semi text-white shadow-lg hover:bg-blue-700\"\n              >\n                {t('下载客户端')}\n              </Link>\n            </div>\n          </div>\n\n          {/* 右侧 - 特色功能 */}\n          <div className=\"lg:pl-8\">\n            <div className=\"grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-2\">\n              {features.map((feature, index) => {\n                const Icon = feature.icon;\n                return (\n                  <div key={index} className=\"flex items-start p-3\">\n                    <div className=\"flex h-10 w-10 items-center justify-center rounded-lg bg-blue-100 mr-3 flex-shrink-0\">\n                      <Icon className=\"h-5 w-5 text-blue-600\" />\n                    </div>\n                    <div>\n                      <div className=\"text-base font-semibold text-gray-900 mb-1\">{feature.title}</div>\n                      <div className=\"text-sm text-gray-600 leading-relaxed\">{feature.description}</div>\n                    </div>\n                  </div>\n                );\n              })}\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;;;;;AAEe,SAAS;IACtB,MAAM,IAAI,IAAA,iQAAe;IACzB,MAAM,WAAW;QACf;YAAE,MAAM,uNAAO;YAAE,OAAO,EAAE;YAAS,aAAa,EAAE;QAAgB;QAClE;YAAE,MAAM,6MAAK;YAAE,OAAO,EAAE;YAAS,aAAa,EAAE;QAAc;QAC9D;YAAE,MAAM,gNAAM;YAAE,OAAO,EAAE;YAAS,aAAa,EAAE;QAAoB;QACrE;YAAE,MAAM,sNAAQ;YAAE,OAAO,EAAE;YAAS,aAAa,EAAE;QAAgB;QACnE;YAAE,MAAM,0MAAI;YAAE,OAAO,EAAE;YAAS,aAAa,EAAE;QAAgB;QAC/D;YAAE,MAAM,6MAAK;YAAE,OAAO,EAAE;YAAS,aAAa,EAAE;QAA+B;KAChF;IAED,qBACE,8OAAC;QAAQ,WAAU;;0BAEjB,8OAAC;gBAAI,WAAU;;;;;;0BAEf,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;8CACZ,EAAE;;;;;;8CAIL,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;;gDAAM,EAAE;gDAAuB;;;;;;;sDAChC,8OAAC;4CAAK,WAAU;sDAAiB,EAAE;;;;;;;;;;;;8CAIrC,8OAAC;oCAAE,WAAU;8CACX,cAAA,8OAAC;kDAAM,EAAE;;;;;;;;;;;8CAIX,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,uKAAI;wCACH,MAAK;wCACL,QAAO;wCACP,KAAI;wCACJ,WAAU;kDAET,EAAE;;;;;;;;;;;;;;;;;sCAMT,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACZ,SAAS,GAAG,CAAC,CAAC,SAAS;oCACtB,MAAM,OAAO,QAAQ,IAAI;oCACzB,qBACE,8OAAC;wCAAgB,WAAU;;0DACzB,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAK,WAAU;;;;;;;;;;;0DAElB,8OAAC;;kEACC,8OAAC;wDAAI,WAAU;kEAA8C,QAAQ,KAAK;;;;;;kEAC1E,8OAAC;wDAAI,WAAU;kEAAyC,QAAQ,WAAW;;;;;;;;;;;;;uCANrE;;;;;gCAUd;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOd", "debugId": null}}, {"offset": {"line": 251, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/webs/apiflow/packages/website/src/components/sections/ComparisonSection.tsx"], "sourcesContent": ["import React from 'react';\r\nimport { Check, X } from 'lucide-react';\r\nimport { useTranslations } from 'next-intl';\r\n\r\nexport default function ComparisonSection() {\r\n  const t = useTranslations();\r\n  const features = [\r\n    {\r\n      category: 'Core Features',\r\n      items: [\r\n        { feature: 'API Testing & Documentation', apiflow: true, postman: true, apifox: true },\r\n        { feature: 'Real-time Collaboration', apiflow: true, postman: 'limited', apifox: true },\r\n        { feature: 'Advanced Mock Servers', apiflow: true, postman: 'paid', apifox: true },\r\n        { feature: 'Automated Testing Workflows', apiflow: true, postman: 'paid', apifox: 'limited' },\r\n        { feature: 'GraphQL Support', apiflow: true, postman: true, apifox: true },\r\n      ]\r\n    },\r\n    {\r\n      category: 'Developer Experience',\r\n      items: [\r\n        { feature: 'Intuitive Interface', apiflow: true, postman: 'good', apifox: 'good' },\r\n        { feature: 'Fast Performance', apiflow: true, postman: 'slow', apifox: 'good' },\r\n        { feature: 'Offline Mode', apiflow: true, postman: true, apifox: 'limited' },\r\n        { feature: 'Custom Themes', apiflow: true, postman: false, apifox: 'limited' },\r\n        { feature: 'Plugin Ecosystem', apiflow: true, postman: true, apifox: 'limited' },\r\n      ]\r\n    },\r\n    {\r\n      category: 'Enterprise',\r\n      items: [\r\n        { feature: 'SSO Integration', apiflow: true, postman: 'paid', apifox: 'paid' },\r\n        { feature: 'Advanced Security', apiflow: true, postman: 'paid', apifox: 'limited' },\r\n        { feature: 'Custom Deployment', apiflow: true, postman: 'enterprise', apifox: false },\r\n        { feature: 'Priority Support', apiflow: true, postman: 'paid', apifox: 'paid' },\r\n        { feature: 'Compliance Ready', apiflow: true, postman: 'enterprise', apifox: 'limited' },\r\n      ]\r\n    }\r\n  ];\r\n\r\n  const renderFeatureValue = (value: boolean | string) => {\r\n    if (value === true) {\r\n      return <Check className=\"h-5 w-5 text-green-600\" />;\r\n    } else if (value === false) {\r\n      return <X className=\"h-5 w-5 text-red-500\" />;\r\n    } else {\r\n      return <span className=\"text-xs text-yellow-600 font-medium\">{value}</span>;\r\n    }\r\n  };\r\n\r\n\r\n\r\n  return (\r\n    <section id=\"features\" className=\"py-16 sm:py-20 lg:py-18 bg-gray-50\">\r\n      <div className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\r\n        {/* 版块标题 */}\r\n        <div className=\"text-center mb-16\">\r\n          <h2 className=\"text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl lg:text-5xl\">\r\n            {t('为什么选择 Apiflow？')}\r\n          </h2>\r\n          <p className=\"mx-auto mt-6 max-w-3xl text-lg text-gray-600\">\r\n            {t('其他工具有的功能我们也有，其他工具收费的功能我们免费')}\r\n          </p>\r\n          <p className=\"mx-auto mt-2 max-w-3xl text-lg text-gray-600\">\r\n            {t('如果发现我们还有不支持的功能，请稍等一下，过两天就有了')}\r\n          </p>\r\n        </div>\r\n\r\n\r\n\r\n        {/* 对比表格 */}\r\n        <div className=\"bg-white rounded-2xl shadow-xl overflow-hidden\">\r\n          <div className=\"px-6 py-8 bg-gradient-to-r from-blue-600 to-purple-600\">\r\n            <h3 className=\"text-2xl font-bold text-white text-center\">\r\n              {t('功能对比')}\r\n            </h3>\r\n            <p className=\"text-blue-100 text-center mt-2\">\r\n              {t('我们正在努力追赶与竞品之间的差距')}\r\n            </p>\r\n          </div>\r\n\r\n          <div className=\"overflow-x-auto\">\r\n            <table className=\"w-full\">\r\n              <thead className=\"bg-gray-50\">\r\n                <tr>\r\n                  <th className=\"px-6 py-4 text-left text-base font-semibold text-gray-900\">\r\n                    功能\r\n                  </th>\r\n                  <th className=\"px-6 py-4 text-center text-base font-semibold text-blue-600\">\r\n                    Apiflow\r\n                  </th>\r\n                  <th className=\"px-6 py-4 text-center text-base font-semibold text-gray-600\">\r\n                    Postman\r\n                  </th>\r\n                  <th className=\"px-6 py-4 text-center text-base font-semibold text-gray-600\">\r\n                    Hoppscotch\r\n                  </th>\r\n                  <th className=\"px-6 py-4 text-center text-base font-semibold text-gray-600\">\r\n                    Apifox\r\n                  </th>\r\n                </tr>\r\n              </thead>\r\n              <tbody className=\"divide-y divide-gray-200\">\r\n                {features.map((category, categoryIndex) => (\r\n                  <React.Fragment key={`category-group-${categoryIndex}`}>\r\n                    <tr key={`category-${categoryIndex}`} className=\"bg-gray-50\">\r\n                      <td colSpan={4} className=\"px-6 py-3 text-sm font-semibold text-gray-700\">\r\n                        {category.category}\r\n                      </td>\r\n                    </tr>\r\n                    {category.items.map((item, itemIndex) => (\r\n                      <tr key={`${categoryIndex}-${itemIndex}`} className=\"hover:bg-gray-50\">\r\n                        <td className=\"px-6 py-4 text-sm text-gray-900\">\r\n                          {item.feature}\r\n                        </td>\r\n                        <td className=\"px-6 py-4 text-center\">\r\n                          {renderFeatureValue(item.apiflow)}\r\n                        </td>\r\n                        <td className=\"px-6 py-4 text-center\">\r\n                          {renderFeatureValue(item.postman)}\r\n                        </td>\r\n                        <td className=\"px-6 py-4 text-center\">\r\n                          {renderFeatureValue(item.apifox)}\r\n                        </td>\r\n                      </tr>\r\n                    ))}\r\n                  </React.Fragment>\r\n                ))}\r\n              </tbody>\r\n            </table>\r\n          </div>\r\n\r\n          <div className=\"px-6 py-6 bg-blue-50 text-center\">\r\n            <p className=\"text-sm text-gray-600 mb-4\">\r\n              {t('准备体验不同之处？')}\r\n            </p>\r\n            <button className=\"inline-flex items-center rounded-lg bg-blue-600 px-6 py-3 text-base font-semibold text-white shadow-lg hover:bg-blue-700 transition-all duration-200\">\r\n              {t('开始免费试用')}\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AAAA;AACA;;;;;AAEe,SAAS;IACtB,MAAM,IAAI,IAAA,iQAAe;IACzB,MAAM,WAAW;QACf;YACE,UAAU;YACV,OAAO;gBACL;oBAAE,SAAS;oBAA+B,SAAS;oBAAM,SAAS;oBAAM,QAAQ;gBAAK;gBACrF;oBAAE,SAAS;oBAA2B,SAAS;oBAAM,SAAS;oBAAW,QAAQ;gBAAK;gBACtF;oBAAE,SAAS;oBAAyB,SAAS;oBAAM,SAAS;oBAAQ,QAAQ;gBAAK;gBACjF;oBAAE,SAAS;oBAA+B,SAAS;oBAAM,SAAS;oBAAQ,QAAQ;gBAAU;gBAC5F;oBAAE,SAAS;oBAAmB,SAAS;oBAAM,SAAS;oBAAM,QAAQ;gBAAK;aAC1E;QACH;QACA;YACE,UAAU;YACV,OAAO;gBACL;oBAAE,SAAS;oBAAuB,SAAS;oBAAM,SAAS;oBAAQ,QAAQ;gBAAO;gBACjF;oBAAE,SAAS;oBAAoB,SAAS;oBAAM,SAAS;oBAAQ,QAAQ;gBAAO;gBAC9E;oBAAE,SAAS;oBAAgB,SAAS;oBAAM,SAAS;oBAAM,QAAQ;gBAAU;gBAC3E;oBAAE,SAAS;oBAAiB,SAAS;oBAAM,SAAS;oBAAO,QAAQ;gBAAU;gBAC7E;oBAAE,SAAS;oBAAoB,SAAS;oBAAM,SAAS;oBAAM,QAAQ;gBAAU;aAChF;QACH;QACA;YACE,UAAU;YACV,OAAO;gBACL;oBAAE,SAAS;oBAAmB,SAAS;oBAAM,SAAS;oBAAQ,QAAQ;gBAAO;gBAC7E;oBAAE,SAAS;oBAAqB,SAAS;oBAAM,SAAS;oBAAQ,QAAQ;gBAAU;gBAClF;oBAAE,SAAS;oBAAqB,SAAS;oBAAM,SAAS;oBAAc,QAAQ;gBAAM;gBACpF;oBAAE,SAAS;oBAAoB,SAAS;oBAAM,SAAS;oBAAQ,QAAQ;gBAAO;gBAC9E;oBAAE,SAAS;oBAAoB,SAAS;oBAAM,SAAS;oBAAc,QAAQ;gBAAU;aACxF;QACH;KACD;IAED,MAAM,qBAAqB,CAAC;QAC1B,IAAI,UAAU,MAAM;YAClB,qBAAO,8OAAC,6MAAK;gBAAC,WAAU;;;;;;QAC1B,OAAO,IAAI,UAAU,OAAO;YAC1B,qBAAO,8OAAC,iMAAC;gBAAC,WAAU;;;;;;QACtB,OAAO;YACL,qBAAO,8OAAC;gBAAK,WAAU;0BAAuC;;;;;;QAChE;IACF;IAIA,qBACE,8OAAC;QAAQ,IAAG;QAAW,WAAU;kBAC/B,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCACX,EAAE;;;;;;sCAEL,8OAAC;4BAAE,WAAU;sCACV,EAAE;;;;;;sCAEL,8OAAC;4BAAE,WAAU;sCACV,EAAE;;;;;;;;;;;;8BAOP,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CACX,EAAE;;;;;;8CAEL,8OAAC;oCAAE,WAAU;8CACV,EAAE;;;;;;;;;;;;sCAIP,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAM,WAAU;;kDACf,8OAAC;wCAAM,WAAU;kDACf,cAAA,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAA4D;;;;;;8DAG1E,8OAAC;oDAAG,WAAU;8DAA8D;;;;;;8DAG5E,8OAAC;oDAAG,WAAU;8DAA8D;;;;;;8DAG5E,8OAAC;oDAAG,WAAU;8DAA8D;;;;;;8DAG5E,8OAAC;oDAAG,WAAU;8DAA8D;;;;;;;;;;;;;;;;;kDAKhF,8OAAC;wCAAM,WAAU;kDACd,SAAS,GAAG,CAAC,CAAC,UAAU,8BACvB,8OAAC,gNAAK,CAAC,QAAQ;;kEACb,8OAAC;wDAAqC,WAAU;kEAC9C,cAAA,8OAAC;4DAAG,SAAS;4DAAG,WAAU;sEACvB,SAAS,QAAQ;;;;;;uDAFb,CAAC,SAAS,EAAE,eAAe;;;;;oDAKnC,SAAS,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,0BACzB,8OAAC;4DAAyC,WAAU;;8EAClD,8OAAC;oEAAG,WAAU;8EACX,KAAK,OAAO;;;;;;8EAEf,8OAAC;oEAAG,WAAU;8EACX,mBAAmB,KAAK,OAAO;;;;;;8EAElC,8OAAC;oEAAG,WAAU;8EACX,mBAAmB,KAAK,OAAO;;;;;;8EAElC,8OAAC;oEAAG,WAAU;8EACX,mBAAmB,KAAK,MAAM;;;;;;;2DAX1B,GAAG,cAAc,CAAC,EAAE,WAAW;;;;;;+CAPvB,CAAC,eAAe,EAAE,eAAe;;;;;;;;;;;;;;;;;;;;;sCA4B9D,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,WAAU;8CACV,EAAE;;;;;;8CAEL,8OAAC;oCAAO,WAAU;8CACf,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOjB", "debugId": null}}, {"offset": {"line": 658, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/webs/apiflow/packages/website/src/app/%5Blocale%5D/page.tsx"], "sourcesContent": ["import HeroSection from \"@/components/sections/HeroSection\";\nimport ComparisonSection from \"@/components/sections/ComparisonSection\";\n\nexport default function Home() {\n  return (\n    <div className=\"min-h-screen\">\n      <HeroSection />\n      <ComparisonSection />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,+KAAW;;;;;0BACZ,8OAAC,qLAAiB;;;;;;;;;;;AAGxB", "debugId": null}}]}