<!DOCTYPE html><html lang="en" class="dark"><head></head><div id="__next"><main class="jsx-2165555507"><script>((e,i,s,u,m,a,l,h)=>{let d=document.documentElement,w=["light","dark"];function p(n){(Array.isArray(e)?e:[e]).forEach(y=>{let k=y==="class",S=k&&a?m.map(f=>a[f]||f):m;k?(d.classList.remove(...S),d.classList.add(a&&a[n]?a[n]:n)):d.setAttribute(y,n)}),R(n)}function R(n){h&&w.includes(n)&&(d.style.colorScheme=n)}function c(){return window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light"}if(u)p(u);else try{let n=localStorage.getItem(i)||s,y=l&&n==="system"?c():n;p(y)}catch(n){}})("class","isDarkMode","system",null,["dark","light","true","false","system"],{"true":"dark","false":"light","dark":"dark","light":"light"},true,true)</script><style>:root {
    --primary: 22 163 74;
    --primary-light: 74 222 128;
    --primary-dark: 22 101 52;
    --background-light: 255 255 255;
    --background-dark: 10 13 13;
    --gray-50: 243 247 245;
    --gray-100: 238 242 240;
    --gray-200: 223 227 224;
    --gray-300: 206 211 208;
    --gray-400: 159 163 160;
    --gray-500: 112 116 114;
    --gray-600: 80 84 82;
    --gray-700: 63 67 64;
    --gray-800: 38 42 39;
    --gray-900: 23 27 25;
    --gray-950: 10 15 12;
  }</style><style>:root {
  --primary: 17 120 102;
  --primary-light: 74 222 128;
  --primary-dark: 22 101 52;
  --background-light: 255 255 255;
  --background-dark: 15 17 23;
}</style><main class="h-screen bg-background-light dark:bg-background-dark text-left"><article class="bg-custom bg-fixed bg-center bg-cover relative flex flex-col items-center justify-center h-full"><div class="w-full max-w-xl px-10"><span class="inline-flex mb-6 rounded-full px-3 py-1 text-sm font-semibold mr-4 text-white p-1 bg-primary">Error <!-- -->500</span><h1 class="font-semibold mb-3 text-3xl">Page not found!</h1><p class="text-lg text-gray-600 dark:text-gray-400 mb-6">An unexpected error occurred. Please <a class="font-medium text-gray-700 dark:text-gray-100 border-b hover:border-b-[2px] border-primary-dark dark:border-primary-light" href="mailto:<EMAIL>">contact support</a> to get help.</p></div></article></main></main></div><script id="__NEXT_DATA__" type="application/json">{"props":{"pageProps":{}},"page":"/500","query":{},"buildId":"8cd_4nc9JJH6YJT8tTh1g","assetPrefix":"/mintlify-assets","nextExport":true,"autoExport":true,"isFallback":false,"scriptLoader":[]}</script></html>
