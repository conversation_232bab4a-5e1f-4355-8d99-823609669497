<!DOCTYPE html>
<html lang="en">
  <head>
    <link rel="preload" as="font" type="font/woff2" href="/assets/inter-cyrillic-ext-wght-normal-B2xhLi22.woff2" crossorigin="anonymous">
    <link rel="preload" as="font" type="font/woff2" href="/assets/inter-cyrillic-wght-normal-CMZtQduZ.woff2" crossorigin="anonymous">
    <link rel="preload" as="font" type="font/woff2" href="/assets/inter-greek-ext-wght-normal-CGAr0uHJ.woff2" crossorigin="anonymous">
    <link rel="preload" as="font" type="font/woff2" href="/assets/inter-greek-wght-normal-CaVNZxsx.woff2" crossorigin="anonymous">
    <link rel="preload" as="font" type="font/woff2" href="/assets/inter-vietnamese-wght-normal-CBcvBZtf.woff2" crossorigin="anonymous">
    <link rel="preload" as="font" type="font/woff2" href="/assets/inter-latin-ext-wght-normal-DO1Apj_S.woff2" crossorigin="anonymous">
    <link rel="preload" as="font" type="font/woff2" href="/assets/inter-latin-wght-normal-Dx4kXJAl.woff2" crossorigin="anonymous">
    <link rel="preload" as="font" type="font/woff2" href="/assets/roboto-mono-cyrillic-ext-wght-normal-BUDPrIko.woff2" crossorigin="anonymous">
    <link rel="preload" as="font" type="font/woff2" href="/assets/roboto-mono-cyrillic-wght-normal-HUlVHixE.woff2" crossorigin="anonymous">
    <link rel="preload" as="font" type="font/woff2" href="/assets/roboto-mono-greek-wght-normal-BJJTbwTT.woff2" crossorigin="anonymous">
    <link rel="preload" as="font" type="font/woff2" href="/assets/roboto-mono-vietnamese-wght-normal-DlC-zuDL.woff2" crossorigin="anonymous">
    <link rel="preload" as="font" type="font/woff2" href="/assets/roboto-mono-latin-ext-wght-normal-QAYlOegK.woff2" crossorigin="anonymous">
    <link rel="preload" as="font" type="font/woff2" href="/assets/roboto-mono-latin-wght-normal-CZtBPCCa.woff2" crossorigin="anonymous">
    <link rel="preload" as="font" type="font/woff2" href="/assets/material-symbols-rounded-latin-wght-normal-CXvURC-M.woff2" crossorigin="anonymous">
    <link rel="preload" as="font" type="font/ttf" href="/assets/codicon-DCmgc-ay.ttf" crossorigin="anonymous">

    <script type="module" crossorigin src="/assets/polyfills-CX3I7EAY.js"></script>

    <script>
      globalThis.import_meta_env = JSON.parse('"import_meta_env_placeholder"')
    </script>
    <title>Hoppscotch 鈥?Open source API development ecosystem</title>
    <meta charset="UTF-8" />
    <link rel="icon" href="/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="apple-touch-icon" href="/icon.png" />
    <meta name="keywords" content="hoppscotch, hopp scotch, hoppscotch online, hoppscotch app, postwoman, postwoman chrome, postwoman online, postwoman for mac, postwoman app, postwoman for windows, postwoman google chrome, postwoman chrome app, get postwoman, postwoman web, postwoman android, postwoman app for chrome, postwoman mobile app, postwoman web app, api, request, testing, tool, rest, websocket, sse, graphql, socketio">
    <meta name="X-UA-Compatible" content="IE=edge, chrome=1">
    <meta name="name" content="Hoppscotch 鈥?Open source API development ecosystem">
    <meta name="description" content="Helps you create requests faster, saving precious time on development.">
    <meta name="image" content="https://hoppscotch.io/banner.png">
    <meta name="og:title" content="Hoppscotch 鈥?Open source API development ecosystem">
    <meta name="og:description" content="Helps you create requests faster, saving precious time on development.">
    <meta name="og:image" content="https://hoppscotch.io/banner.png">
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:site" content="@hoppscotch_io">
    <meta name="twitter:creator" content="@hoppscotch_io">
    <meta name="twitter:title" content="Hoppscotch 鈥?Open source API development ecosystem">
    <meta name="twitter:description" content="Helps you create requests faster, saving precious time on development.">
    <meta name="twitter:image" content="https://hoppscotch.io/banner.png">
    <meta name="application-name" content="Hoppscotch">
    <meta name="msapplication-TileImage" content="https://hoppscotch.io/icon.png">
    <meta name="msapplication-TileColor" content="#181818">
    <meta name="msapplication-tap-highlight" content="no">
    <meta name="apple-mobile-web-app-title" content="Hoppscotch">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="theme-color" content="#181818" media="(prefers-color-scheme: dark)">
    <meta name="theme-color" content="#ffffff" media="(prefers-color-scheme: light)">
    <meta name="supported-color-schemes" content="light dark">
    <meta name="mask-icon" content="/icon.png" color="#181818">
    <script type="module" crossorigin src="/assets/index-qGdHpmT0.js"></script>
    <link rel="stylesheet" crossorigin href="/assets/index-CVzbRV5i.css">
  <link rel="manifest" href="/manifest.webmanifest" crossorigin="use-credentials"></head>
  <body>
    <div id="app"></div>
    <script>
      // Shims to make swagger-parser package work
      window.global = window
    </script>
  </body>
</html>
