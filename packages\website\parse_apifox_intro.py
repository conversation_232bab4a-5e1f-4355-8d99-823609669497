from bs4 import BeautifulSoup
from pathlib import Path
import sys

sys.stdout.reconfigure(encoding='utf-8')
html = Path('apifox_intro_blog.html').read_text(encoding='gb18030', errors='ignore')
soup = BeautifulSoup(html, 'html.parser')
article = soup.find('article')
if not article:
    print('Article not found')
else:
    title = article.find('h1')
    if title:
        print('Title:', title.get_text(strip=True))
    for heading in article.find_all(['h2', 'h3', 'h4']):
        print('\n', heading.name.upper(), heading.get_text(strip=True))
        for sibling in heading.find_next_siblings():
            if getattr(sibling, 'name', None) in ['h2', 'h3', 'h4']:
                break
            if getattr(sibling, 'name', None) == 'p':
                text = sibling.get_text(strip=True)
                if text:
                    print('-', text)
