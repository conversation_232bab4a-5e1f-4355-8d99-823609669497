<!DOCTYPE html>
<html lang="zh">
<head>

    <title>Apifox Blog</title>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="HandheldFriendly" content="True" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="keywords" content="博客教程,Blog,API 教程,最佳实践,教程分享" />

    <link rel="stylesheet" type="text/css" href="/blog/assets/built/screen.css?v=526a981474" />
    <link rel="stylesheet" href="/blog/assets/built/highlight.css?v=526a981474">
    
    <script src="https://cdn.apifox.com/static/js/jquery/3.5.1/jquery.min.js"></script>
    <link rel="icon" href="https://cdn.apifox.com/logo/apifox-logo-512.png">

    <meta name="generator" content="Ghost 5.30" />
    <link rel="alternate" type="application/rss+xml" title="Apifox Blog" href="https://apifox.com/blog/rss/" />
    <script defer src="https://cdn.jsdelivr.net/ghost/portal@~2.24/umd/portal.min.js" data-ghost="http://apifox.com/blog/" data-key="90d90e697b4fa3a05d19096012" data-api="http://apifox.com/blog/ghost/api/content/" crossorigin="anonymous"></script><style id="gh-members-styles">.gh-post-upgrade-cta-content,
.gh-post-upgrade-cta {
    display: flex;
    flex-direction: column;
    align-items: center;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    text-align: center;
    width: 100%;
    color: #ffffff;
    font-size: 16px;
}

.gh-post-upgrade-cta-content {
    border-radius: 8px;
    padding: 40px 4vw;
}

.gh-post-upgrade-cta h2 {
    color: #ffffff;
    font-size: 28px;
    letter-spacing: -0.2px;
    margin: 0;
    padding: 0;
}

.gh-post-upgrade-cta p {
    margin: 20px 0 0;
    padding: 0;
}

.gh-post-upgrade-cta small {
    font-size: 16px;
    letter-spacing: -0.2px;
}

.gh-post-upgrade-cta a {
    color: #ffffff;
    cursor: pointer;
    font-weight: 500;
    box-shadow: none;
    text-decoration: underline;
}

.gh-post-upgrade-cta a:hover {
    color: #ffffff;
    opacity: 0.8;
    box-shadow: none;
    text-decoration: underline;
}

.gh-post-upgrade-cta a.gh-btn {
    display: block;
    background: #ffffff;
    text-decoration: none;
    margin: 28px 0 0;
    padding: 8px 18px;
    border-radius: 4px;
    font-size: 16px;
    font-weight: 600;
}

.gh-post-upgrade-cta a.gh-btn:hover {
    opacity: 0.92;
}</style>
    <script defer src="https://cdn.jsdelivr.net/ghost/sodo-search@~1.1/umd/sodo-search.min.js" data-key="90d90e697b4fa3a05d19096012" data-styles="https://cdn.jsdelivr.net/ghost/sodo-search@~1.1/umd/main.css" data-sodo-search="http://apifox.com/blog/" crossorigin="anonymous"></script>
    <link href="http://apifox.com/blog/webmention" rel="webmention" />
    <script defer src="/blog/public/cards.min.js?v=526a981474"></script>
    <link rel="stylesheet" type="text/css" href="/blog/public/cards.min.css?v=526a981474">
    <script defer src="/blog/public/member-attribution.min.js?v=526a981474"></script>
    <style>
  /*表格样式*/
  table {
    width:800px !important;
    white-space: normal;
  }
  .gh-content table:not(.gist table){
      background: none !important;
    }
    
/*把订阅移除*/
    .ui-subscribe-btn{
	  display: none !important;
	}

</style>
<meta http-equiv="Content-Security-Policy" content="upgrade-insecure-requests">

<style>:root {--ghost-accent-color: #DF0A1B;}</style>

</head>
<body class=" is-head-left-logo has-cover">
<div class="viewport">

    <header id="gh-head" class="gh-head outer is-header-hidden">
        <div class="gh-head-inner inner">
            <div class="gh-head-brand">
                <a class="gh-head-logo" href="http://apifox.com/blog">
                    <svg width="166" height="32" viewBox="0 0 166 32" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M30.4223 21.5456C30.2367 21.5584 30.0487 21.564 29.8623 21.564C29.7999 21.564 29.7375 21.564 29.6759 21.564C29.7904 21.1191 29.8706 20.666 29.9159 20.2087C29.9447 19.9135 29.9599 19.6143 29.9599 19.3111C29.96 18.9724 29.941 18.6339 29.9031 18.2974C29.0246 18.7333 28.0805 19.022 27.1084 19.1519C26.838 19.1887 26.5639 19.2126 26.286 19.2239C26.3744 18.7841 26.4295 18.3382 26.4508 17.8902C26.458 17.7413 26.462 17.5917 26.462 17.4413C26.4626 16.7977 26.3945 16.1559 26.2588 15.5268C25.5711 15.9324 24.8322 16.2442 24.0618 16.4541C23.7834 16.5298 23.4999 16.5925 23.2114 16.6421L23.2042 16.6325C23.2881 16.2126 23.3416 15.7872 23.3642 15.3596C23.3738 15.1995 23.3778 15.0395 23.3778 14.8715C23.3779 14.3274 23.3289 13.7843 23.2314 13.249C22.2825 14.2443 21.1413 15.0362 19.8769 15.5767C18.6125 16.1173 17.2515 16.3951 15.8764 16.3932C14.2529 16.3958 12.6526 16.0077 11.2105 15.262C11.8666 15.0075 12.4906 14.6769 13.0698 14.2771C11.5315 13.5383 10.2334 12.3799 9.32489 10.9353C8.4164 9.49078 7.93461 7.81894 7.93501 6.11247C7.93472 5.56563 7.98397 5.01988 8.08221 4.48194C8.17863 3.94875 8.32352 3.42547 8.51506 2.91862C8.70375 2.41868 8.93696 1.93671 9.21191 1.47851C9.52321 0.955863 9.8867 0.466121 10.2968 0.0167981C8.91227 0.646328 7.61545 1.45315 6.43891 2.41698C3.45763 4.85337 1.36062 8.20284 0.471249 11.9489C0.156919 13.277 -0.00122231 14.6372 2.25956e-05 16.002C-0.00470319 18.9404 0.731929 21.8325 2.14177 24.4106C2.2373 23.5639 2.45281 22.735 2.78181 21.9489C3.84403 24.5156 5.52771 26.778 7.68136 28.5325C9.83501 30.287 12.3911 31.4786 15.1196 32C14.6507 31.3897 14.2624 30.7215 13.9643 30.0119C15.4168 30.4831 16.93 30.7415 18.4566 30.7791C18.5918 30.7791 18.7278 30.7839 18.8638 30.7839C21.2892 30.7876 23.6827 30.2324 25.8588 29.1614C25.4964 28.957 25.1489 28.7273 24.8187 28.4742C27.2354 27.5482 29.3735 26.0167 31.0279 24.0266C31.7948 23.1053 32.4488 22.0957 32.9761 21.0192C32.1549 21.3178 31.2946 21.4951 30.4223 21.5456Z" fill="url(#paint0_linear_2092_2228)"/>
<path d="M34.457 13.2194C34.369 12.0702 34.1492 10.9349 33.8017 9.83594C33.6636 10.2086 33.4883 10.5664 33.2785 10.904C33.0071 9.10916 32.3776 7.38719 31.4273 5.84054C30.477 4.29388 29.2254 2.95413 27.7469 1.90094C26.7208 1.16704 25.5966 0.581002 24.4074 0.160012C24.2474 0.102941 24.0855 0.0496037 23.9218 0C25.0431 1.44699 25.7509 3.17097 25.9699 4.98838C25.9779 5.05159 25.9851 5.11478 25.9915 5.17879C25.9919 5.18358 25.9919 5.18841 25.9915 5.1932C25.9979 5.25881 26.0043 5.32441 26.0091 5.39082V5.42521C26.0147 5.49082 26.0188 5.55642 26.0228 5.62203C26.0268 5.68763 26.03 5.75403 26.0324 5.81964C26.0324 5.86604 26.0324 5.91324 26.0372 5.95965C26.0368 5.96711 26.0368 5.97459 26.0372 5.98205C26.0372 6.01005 26.0372 6.03886 26.0372 6.06686C26.0372 6.11727 26.0372 6.16846 26.0372 6.21887C26.0401 8.29592 25.4044 10.3237 24.2162 12.0273C24.3469 12.6458 24.4129 13.2761 24.413 13.9083C24.4133 14.3347 24.3833 14.7606 24.3234 15.1827C24.8989 15.0466 25.4593 14.8534 25.9964 14.6059C26.3952 14.4234 26.7801 14.2116 27.1476 13.9723C27.2228 14.2339 27.2869 14.5003 27.3373 14.7723C27.4446 15.3348 27.4982 15.9062 27.4973 16.4788C27.4975 16.9341 27.4636 17.3888 27.3957 17.8389C28.1905 17.7565 28.9708 17.5681 29.7158 17.2789C30.1068 17.1274 30.4864 16.9483 30.8519 16.7429C30.9068 17.0489 30.9458 17.3576 30.9687 17.6677C30.9858 17.8928 30.9943 18.12 30.9943 18.3494C30.9947 18.9763 30.9301 19.6016 30.8015 20.2151C31.7376 20.1766 32.6619 19.992 33.5409 19.6679C34.1714 17.9803 34.4934 16.193 34.4914 14.3915C34.4983 13.9963 34.4869 13.6056 34.457 13.2194Z" fill="url(#paint1_linear_2092_2228)"/>
<path d="M54.7596 23.8311L48.2983 6.58431H44.5706L38.0596 23.8311H40.9423L42.6819 19.0099H50.0876L51.8023 23.8311H54.7596ZM46.2605 9.11914H46.5587L49.2426 16.5994H43.5517L46.2605 9.11914Z" fill="black"/>
<path d="M69.4585 17.1212C69.4585 12.7474 68.2656 10.1629 63.9664 10.1629C61.7795 10.1629 60.363 10.9581 59.7417 12.5238V10.4114H57.0577V28.8013H59.7417V21.7187C60.3381 23.2346 61.5558 24.0796 63.9664 24.0796C66.029 24.0796 67.4456 23.4583 68.2408 22.2157C69.0609 20.9483 69.4585 19.2584 69.4585 17.1212ZM66.6752 17.1212C66.6503 20.004 66.2278 21.8678 63.3948 21.9424C60.6115 21.9424 59.5926 20.2773 59.5926 17.1212C59.5926 13.8657 60.5866 12.2752 63.3948 12.2752C66.1533 12.2752 66.6752 14.1888 66.6752 17.1212Z" fill="black"/>
<path d="M72.6573 6.65887C72.6573 7.97598 72.9555 8.17479 74.2229 8.17479C75.4407 8.17479 75.7637 7.92628 75.7637 6.65887C75.7637 5.3666 75.4655 5.19264 74.2229 5.19264C72.9555 5.19264 72.6573 5.3666 72.6573 6.65887ZM75.5401 23.8311V10.4114H72.8561V23.8311H75.5401Z" fill="black"/>
<path d="M82.9057 10.4114C82.9057 7.57836 83.3282 6.88253 85.192 6.88253C85.7139 6.88253 86.1612 6.93223 86.534 7.00678V4.96898C86.0121 4.89443 85.4903 4.84473 85.0181 4.84473C81.862 4.89443 80.2964 5.76422 80.2218 10.2871V10.4114H78.0349V12.5486H80.2218V23.8311H82.9057V12.5486H85.863V10.4114H82.9057Z" fill="black"/>
<path d="M87.352 17.1212C87.352 22.4145 89.5389 24.0796 93.689 24.0796C97.8889 24.0796 100.026 22.4145 100.026 17.1212C100.026 11.8279 97.8889 10.1629 93.689 10.1629C89.5389 10.1629 87.352 11.8279 87.352 17.1212ZM97.2427 17.1212C97.2427 20.8241 96.4475 21.9424 93.689 21.9424C91.0299 21.9424 90.1353 20.7992 90.1353 17.1212C90.1353 13.4184 90.9554 12.2752 93.689 12.2752C96.373 12.2752 97.2427 13.4184 97.2427 17.1212Z" fill="black"/>
<path d="M104.266 23.8311L107.621 18.6123H107.77L111.025 23.8311H114.132L109.534 16.9473L113.883 10.4114H110.852L107.795 15.3816H107.646L104.539 10.4114H101.483L105.832 16.9721L101.209 23.8311H104.266Z" fill="black"/>
<path d="M123.532 10.72V25H130.092C131.612 25 132.812 24.72 133.652 24.16C134.632 23.48 135.132 22.42 135.132 20.98C135.132 20.02 134.892 19.24 134.432 18.68C133.952 18.1 133.252 17.72 132.312 17.54C133.032 17.26 133.572 16.88 133.952 16.36C134.332 15.8 134.532 15.12 134.532 14.32C134.532 13.24 134.152 12.38 133.412 11.74C132.612 11.06 131.492 10.72 130.072 10.72H123.532ZM125.872 12.64H129.492C130.452 12.64 131.132 12.8 131.572 13.12C131.972 13.42 132.192 13.92 132.192 14.6C132.192 15.34 131.972 15.88 131.572 16.22C131.152 16.54 130.452 16.72 129.452 16.72H125.872V12.64ZM125.872 18.64H129.812C130.852 18.64 131.612 18.82 132.092 19.18C132.552 19.54 132.792 20.12 132.792 20.94C132.792 21.74 132.472 22.3 131.832 22.66C131.332 22.94 130.632 23.08 129.752 23.08H125.872V18.64ZM137.34 10.44V25H139.62V10.44H137.34ZM147.019 14.38C145.439 14.38 144.179 14.88 143.239 15.92C142.279 16.94 141.819 18.24 141.819 19.84C141.819 21.42 142.279 22.72 143.219 23.72C144.179 24.76 145.439 25.28 147.019 25.28C148.579 25.28 149.859 24.76 150.819 23.72C151.739 22.72 152.219 21.42 152.219 19.84C152.219 18.24 151.739 16.94 150.799 15.92C149.839 14.88 148.579 14.38 147.019 14.38ZM147.019 16.24C147.959 16.24 148.699 16.6 149.219 17.36C149.659 18 149.899 18.84 149.899 19.84C149.899 20.82 149.659 21.64 149.219 22.3C148.699 23.04 147.959 23.42 147.019 23.42C146.059 23.42 145.339 23.04 144.819 22.3C144.379 21.66 144.159 20.84 144.159 19.84C144.159 18.84 144.379 18 144.819 17.36C145.339 16.6 146.059 16.24 147.019 16.24ZM158.511 14.38C157.111 14.38 155.971 14.86 155.111 15.82C154.231 16.78 153.811 18.04 153.811 19.6C153.811 21.16 154.231 22.42 155.111 23.36C155.971 24.28 157.111 24.76 158.511 24.76C159.791 24.76 160.811 24.22 161.571 23.16V24.68C161.571 26.52 160.711 27.44 158.991 27.44C158.191 27.44 157.611 27.32 157.251 27.1C156.871 26.86 156.631 26.46 156.511 25.88H154.231C154.431 27.04 154.911 27.88 155.671 28.44C156.431 28.96 157.531 29.24 158.991 29.24C162.231 29.24 163.851 27.6 163.851 24.34V14.66H161.571V15.92C160.811 14.88 159.791 14.38 158.511 14.38ZM158.871 16.22C159.731 16.22 160.411 16.52 160.891 17.12C161.351 17.72 161.591 18.54 161.591 19.6C161.591 20.64 161.351 21.44 160.891 22.04C160.411 22.6 159.731 22.9 158.851 22.9C157.851 22.9 157.151 22.56 156.711 21.9C156.331 21.34 156.151 20.58 156.151 19.6C156.151 18.52 156.391 17.7 156.871 17.12C157.351 16.52 158.011 16.22 158.871 16.22Z" fill="#101828" fill-opacity="0.56"/>
<defs>
<linearGradient id="paint0_linear_2092_2228" x1="0" y1="0" x2="31.9101" y2="34.3965" gradientUnits="userSpaceOnUse">
<stop offset="0.01" stop-color="#FF387E"/>
<stop offset="0.18" stop-color="#FF386D"/>
<stop offset="0.67" stop-color="#F44A53"/>
<stop offset="1" stop-color="#F35E3A"/>
</linearGradient>
<linearGradient id="paint1_linear_2092_2228" x1="0" y1="0" x2="31.9101" y2="34.3965" gradientUnits="userSpaceOnUse">
<stop offset="0.01" stop-color="#FF387E"/>
<stop offset="0.18" stop-color="#FF386D"/>
<stop offset="0.67" stop-color="#F44A53"/>
<stop offset="1" stop-color="#F35E3A"/>
</linearGradient>
</defs>
</svg>
                </a>
            </div>

            <div class="ui-menu-action">
                <a class="ui-menu-action-item" href="http://apifox.com/blog/viewpoint/">
                    观点
                </a>
                <a class="ui-menu-action-item" href="http://apifox.com/blog/best-practice/">
                    最佳实践
                </a>
                <a class="ui-menu-action-item" href="http://apifox.com/blog/news/">
                    资讯
                </a>
                <a class="ui-menu-action-item" href="http://apifox.com/blog/product-news/">
                    产品动态
                </a>
                <a class="ui-menu-action-item" href="http://apifox.com/blog/clients/">
                    客户案例
                </a>

                <div class="ui-menu-action-btn">
                    <svg class="icon-active" width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M4.51855 6.02515H20.5185M4.51855 12.0251H20.5185M4.51855 18.0251H20.5185" stroke="currentColor" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                </div>

                <div class="ui-menu-dropdown">
                    <div class="ui-menu-dropdown-content">
                        <a class="ui-menu-dropdown-card" href="http://apifox.com/blog/viewpoint/">
                            <div class="menu-card-left">
                                <img src="https://cdn.apifox.com/www/assets/image/blog/light-bulb.svg">
                            </div>
                            <div class="menu-card-right">
                                <p class="menu-card-right-title">观点</p>
                                <p class="menu-card-right-content">了解行业洞见，获取 Apifox 最新观点</p>
                            </div>
                        </a>
                        <a class="ui-menu-dropdown-card" href="http://apifox.com/blog/best-practice/">
                            <div class="menu-card-left">
                                <img src="https://cdn.apifox.com/www/assets/image/blog/best-practice.svg">
                            </div>
                            <div class="menu-card-right">
                                <p class="menu-card-right-title">最佳实践</p>
                                <p class="menu-card-right-content">学习优秀的 API 开发协作实践经验</p>
                            </div>
                        </a>
                        <a class="ui-menu-dropdown-card" href="http://apifox.com/blog/news/">
                            <div class="menu-card-left">
                                <img src="https://cdn.apifox.com/www/assets/image/blog/consult.svg">
                            </div>
                            <div class="menu-card-right">
                                <p class="menu-card-right-title">资讯</p>
                                <p class="menu-card-right-content">随时随地了解 Apifox 最新发展动向</p>
                            </div>
                        </a>
                        <a class="ui-menu-dropdown-card" href="http://apifox.com/blog/product-news/">
                            <div class="menu-card-left">
                                <img src="https://cdn.apifox.com/www/assets/image/blog/product-news.svg">
                            </div>
                            <div class="menu-card-right">
                                <p class="menu-card-right-title">产品动态</p>
                                <p class="menu-card-right-content">掌握 Apifox 产品更新动态</p>
                            </div>
                        </a>
                        <a class="ui-menu-dropdown-card" href="http://apifox.com/blog/clients/">
                            <div class="menu-card-left">
                                <img src="https://cdn.apifox.com/www/assets/image/blog/clients.svg">
                            </div>
                            <div class="menu-card-right">
                                <p class="menu-card-right-title">客户案例</p>
                                <p class="menu-card-right-content">受数千家先进企业信任的 Apifox，为各行各业的标杆企业提高企业研发生产力。</p>
                            </div>
                        </a>
                    </div>
                    <div class="ui-menu-dropdown-footer">
                        <a class="ui-btn-primary transition-all" href="https://apifox.com/" target="_blank" style="width: 167px;">免费使用 Apifox</a>
                    </div>
                </div>
            </div>

            <div class="gh-head-actions">
                <button class="gh-search gh-icon-btn text-white-85 ui-btn-search" data-ghost-search><svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M9.98438 0.484375C4.73769 0.484375 0.484375 4.73769 0.484375 9.98438C0.484375 15.2311 4.73769 19.4844 9.98438 19.4844C12.2475 19.4844 14.3258 18.693 15.9576 17.3719L17.8085 19.2228C18.199 19.6133 18.8322 19.6133 19.2227 19.2228C19.6132 18.8322 19.6132 18.1991 19.2227 17.8085L17.3719 15.9577C18.693 14.3259 19.4844 12.2475 19.4844 9.98438C19.4844 4.73769 15.2311 0.484375 9.98438 0.484375ZM2.48438 9.98438C2.48438 5.84226 5.84226 2.48438 9.98438 2.48438C14.1265 2.48438 17.4844 5.84226 17.4844 9.98438C17.4844 14.1265 14.1265 17.4844 9.98438 17.4844C5.84226 17.4844 2.48438 14.1265 2.48438 9.98438Z" fill="currentColor"/>
</svg>
</button>
                <a class="ui-btn-primary transition-all" href="https://apifox.com/" target="_blank" style="width: 167px;">免费使用 Apifox</a>
            </div>
        </div>
    </header>

    <div class="site-content" style="margin-bottom: 80px;">
        

<section class="outer error-content">
    <div class="inner">
        <section class="error-message">
            <h1 class="error-code">404</h1>
            <p class="error-description">Page not found</p>
            <a class="error-link" href="http://apifox.com/blog">Go to the front page →</a>
        </section>
    </div>
</section>

<aside class="read-more-wrap outer">
    <div class="read-more inner">
                    <article class="post-card post">
    <a class="post-card-image-link" href="/blog/apifox-pm-script/">

        <img class="post-card-image"
            srcset="https://cdn.apifox.com/blog/2025/09/pm-script.png 300w,
                    https://cdn.apifox.com/blog/2025/09/pm-script.png 600w,
                    https://cdn.apifox.com/blog/2025/09/pm-script.png 1000w,
                    https://cdn.apifox.com/blog/2025/09/pm-script.png 2000w"
            sizes="(max-width: 1000px) 400px, 800px"
            src="https://cdn.apifox.com/blog/2025/09/pm-script.png"
            alt="掌握 Apifox 中 pm 脚本的基本使用"
            loading="lazy"
        />


    </a>

    <div class="post-card-content">
        <header class="post-card-header">
            <div class="post-card-tags">

                    <span 
                        class="post-card-primary-tag" 
                    >
                        <a 
                            href="http://apifox.com/blog/best-practice/" 
                            style="color: #0075FF;background: #0075FF1A;"
                            onmouseover="this.style.borderColor='#0075FF';"
                            onmouseleave="this.style.borderColor='transparent';"
                        >
                            最佳实践
                        </a>
                    </span>



            </div>
            <a class="post-card-content-link" href="/blog/apifox-pm-script/">
                <h2 class="post-card-title">
                    掌握 Apifox 中 pm 脚本的基本使用
                </h2>
            </a>
        </header>
            <a class="post-card-content-link" href="/blog/apifox-pm-script/">
                <div class="post-card-excerpt">Apifox 完全兼容 Postman 的脚本 API，这意味着你可以在前置脚本和后置脚本中使用这些强大的功能来处理请求数据、响应结果，以及进行各种自动化操作。</div>
            </a>
        <div class="post-card-author">
            <div class="post-card-author-list">
                    <span class="post-card-author-list-item">
                            <img class="post-card-author-avatar" src="https://cdn.apifox.cn/blog/2023/04/512---.png"  loading="lazy"/>
                        <a href="/blog/author/apifox/">Apifox</a>
                    </span>
            </div>
            <footer class="post-card-meta">
                <div style="justify-content: end;gap: 0.5rem;">
                    <time class="post-card-meta-date" datetime="2025-09-15">2025-09-15</time>
                </div>
            </footer>
        </div>
    </div>
</article>
                    <article class="post-card post">
    <a class="post-card-image-link" href="/blog/published-docs-debug/">

        <img class="post-card-image"
            srcset="https://cdn.apifox.com/blog/2025/09/Group-2609652.png 300w,
                    https://cdn.apifox.com/blog/2025/09/Group-2609652.png 600w,
                    https://cdn.apifox.com/blog/2025/09/Group-2609652.png 1000w,
                    https://cdn.apifox.com/blog/2025/09/Group-2609652.png 2000w"
            sizes="(max-width: 1000px) 400px, 800px"
            src="https://cdn.apifox.com/blog/2025/09/Group-2609652.png"
            alt="如何让 Apifox 发布的在线文档具备更好的调试体验？"
            loading="lazy"
        />


    </a>

    <div class="post-card-content">
        <header class="post-card-header">
            <div class="post-card-tags">

                    <span 
                        class="post-card-primary-tag" 
                    >
                        <a 
                            href="http://apifox.com/blog/best-practice/" 
                            style="color: #0075FF;background: #0075FF1A;"
                            onmouseover="this.style.borderColor='#0075FF';"
                            onmouseleave="this.style.borderColor='transparent';"
                        >
                            最佳实践
                        </a>
                    </span>



            </div>
            <a class="post-card-content-link" href="/blog/published-docs-debug/">
                <h2 class="post-card-title">
                    如何让 Apifox 发布的在线文档具备更好的调试体验？
                </h2>
            </a>
        </header>
            <a class="post-card-content-link" href="/blog/published-docs-debug/">
                <div class="post-card-excerpt">如何在 Apifox 中进行合理的配置，让发布的在线文档具备更好的调试体验呢？你们期待的最佳实践来啦！</div>
            </a>
        <div class="post-card-author">
            <div class="post-card-author-list">
                    <span class="post-card-author-list-item">
                            <img class="post-card-author-avatar" src="https://cdn.apifox.cn/blog/2023/04/512---.png"  loading="lazy"/>
                        <a href="/blog/author/apifox/">Apifox</a>
                    </span>
            </div>
            <footer class="post-card-meta">
                <div style="justify-content: end;gap: 0.5rem;">
                    <time class="post-card-meta-date" datetime="2025-09-11">2025-09-11</time>
                </div>
            </footer>
        </div>
    </div>
</article>
                    <article class="post-card post">
    <a class="post-card-image-link" href="/blog/params/">

        <img class="post-card-image"
            srcset="https://cdn.apifox.com/blog/2025/09/copy.png 300w,
                    https://cdn.apifox.com/blog/2025/09/copy.png 600w,
                    https://cdn.apifox.com/blog/2025/09/copy.png 1000w,
                    https://cdn.apifox.com/blog/2025/09/copy.png 2000w"
            sizes="(max-width: 1000px) 400px, 800px"
            src="https://cdn.apifox.com/blog/2025/09/copy.png"
            alt="理解和掌握 Apifox 中的变量（临时、环境、模块、全局变量等）"
            loading="lazy"
        />


    </a>

    <div class="post-card-content">
        <header class="post-card-header">
            <div class="post-card-tags">

                    <span 
                        class="post-card-primary-tag" 
                    >
                        <a 
                            href="http://apifox.com/blog/best-practice/" 
                            style="color: #0075FF;background: #0075FF1A;"
                            onmouseover="this.style.borderColor='#0075FF';"
                            onmouseleave="this.style.borderColor='transparent';"
                        >
                            最佳实践
                        </a>
                    </span>



            </div>
            <a class="post-card-content-link" href="/blog/params/">
                <h2 class="post-card-title">
                    理解和掌握 Apifox 中的变量（临时、环境、模块、全局变量等）
                </h2>
            </a>
        </header>
            <a class="post-card-content-link" href="/blog/params/">
                <div class="post-card-excerpt">Apifox 提供了多种类型的变量，每种都有特定的使用场景。本篇教程带你掌握 Apifox 目前支持的所有变量的使用方法！快来看看吧！</div>
            </a>
        <div class="post-card-author">
            <div class="post-card-author-list">
                    <span class="post-card-author-list-item">
                            <img class="post-card-author-avatar" src="https://cdn.apifox.cn/blog/2023/04/512---.png"  loading="lazy"/>
                        <a href="/blog/author/apifox/">Apifox</a>
                    </span>
            </div>
            <footer class="post-card-meta">
                <div style="justify-content: end;gap: 0.5rem;">
                    <time class="post-card-meta-date" datetime="2025-09-04">2025-09-04</time>
                </div>
            </footer>
        </div>
    </div>
</article>
    </div>
</aside>
    </div>

    <div class="ui-subscribe-btn">
        <svg width="16" height="18" viewBox="0 0 16 18" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M7.97986 4.97988V9.77988M5.57986 7.37988H10.3799M1.59998 17V1.8C1.59998 1.35817 1.95815 1 2.39998 1H13.6C14.0418 1 14.4 1.35817 14.4 1.8V17L7.99998 13.6909L1.59998 17Z" stroke="white" stroke-width="1.6" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
        订阅

        <div class="ui-subscribe-btn-hover-content">
            <img src="https://cdn.apifox.com/www/assets/image/blog/qrcode.png" width="260" alt="qrcode">
            <h4 style="font-size: 20px; margin: 12px 0;">订阅</h4>
            <p style="font-size: 14px;">随时随地获取 Apifox 最新动态</p>
        </div>
    </div>


    <footer class="ui-footer">
    <div class="ui-footer-container">
        <div class="ui-footer-brand">
            <svg width="166" height="32" viewBox="0 0 166 32" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M30.4223 21.5456C30.2367 21.5584 30.0487 21.564 29.8623 21.564C29.7999 21.564 29.7375 21.564 29.6759 21.564C29.7904 21.1191 29.8706 20.666 29.9159 20.2087C29.9447 19.9135 29.9599 19.6143 29.9599 19.3111C29.96 18.9724 29.941 18.6339 29.9031 18.2974C29.0246 18.7333 28.0805 19.022 27.1084 19.1519C26.838 19.1887 26.5639 19.2126 26.286 19.2239C26.3744 18.7841 26.4295 18.3382 26.4508 17.8902C26.458 17.7413 26.462 17.5917 26.462 17.4413C26.4626 16.7977 26.3945 16.1559 26.2588 15.5268C25.5711 15.9324 24.8322 16.2442 24.0618 16.4541C23.7834 16.5298 23.4999 16.5925 23.2114 16.6421L23.2042 16.6325C23.2881 16.2126 23.3416 15.7872 23.3642 15.3596C23.3738 15.1995 23.3778 15.0395 23.3778 14.8715C23.3779 14.3274 23.3289 13.7843 23.2314 13.249C22.2825 14.2443 21.1413 15.0362 19.8769 15.5767C18.6125 16.1173 17.2515 16.3951 15.8764 16.3932C14.2529 16.3958 12.6526 16.0077 11.2105 15.262C11.8666 15.0075 12.4906 14.6769 13.0698 14.2771C11.5315 13.5383 10.2334 12.3799 9.32489 10.9353C8.4164 9.49078 7.93461 7.81894 7.93501 6.11247C7.93472 5.56563 7.98397 5.01988 8.08221 4.48194C8.17863 3.94875 8.32352 3.42547 8.51506 2.91862C8.70375 2.41868 8.93696 1.93671 9.21191 1.47851C9.52321 0.955863 9.8867 0.466121 10.2968 0.0167981C8.91227 0.646328 7.61545 1.45315 6.43891 2.41698C3.45763 4.85337 1.36062 8.20284 0.471249 11.9489C0.156919 13.277 -0.00122231 14.6372 2.25956e-05 16.002C-0.00470319 18.9404 0.731929 21.8325 2.14177 24.4106C2.2373 23.5639 2.45281 22.735 2.78181 21.9489C3.84403 24.5156 5.52771 26.778 7.68136 28.5325C9.83501 30.287 12.3911 31.4786 15.1196 32C14.6507 31.3897 14.2624 30.7215 13.9643 30.0119C15.4168 30.4831 16.93 30.7415 18.4566 30.7791C18.5918 30.7791 18.7278 30.7839 18.8638 30.7839C21.2892 30.7876 23.6827 30.2324 25.8588 29.1614C25.4964 28.957 25.1489 28.7273 24.8187 28.4742C27.2354 27.5482 29.3735 26.0167 31.0279 24.0266C31.7948 23.1053 32.4488 22.0957 32.9761 21.0192C32.1549 21.3178 31.2946 21.4951 30.4223 21.5456Z" fill="url(#paint0_linear_2092_2228)"/>
<path d="M34.457 13.2194C34.369 12.0702 34.1492 10.9349 33.8017 9.83594C33.6636 10.2086 33.4883 10.5664 33.2785 10.904C33.0071 9.10916 32.3776 7.38719 31.4273 5.84054C30.477 4.29388 29.2254 2.95413 27.7469 1.90094C26.7208 1.16704 25.5966 0.581002 24.4074 0.160012C24.2474 0.102941 24.0855 0.0496037 23.9218 0C25.0431 1.44699 25.7509 3.17097 25.9699 4.98838C25.9779 5.05159 25.9851 5.11478 25.9915 5.17879C25.9919 5.18358 25.9919 5.18841 25.9915 5.1932C25.9979 5.25881 26.0043 5.32441 26.0091 5.39082V5.42521C26.0147 5.49082 26.0188 5.55642 26.0228 5.62203C26.0268 5.68763 26.03 5.75403 26.0324 5.81964C26.0324 5.86604 26.0324 5.91324 26.0372 5.95965C26.0368 5.96711 26.0368 5.97459 26.0372 5.98205C26.0372 6.01005 26.0372 6.03886 26.0372 6.06686C26.0372 6.11727 26.0372 6.16846 26.0372 6.21887C26.0401 8.29592 25.4044 10.3237 24.2162 12.0273C24.3469 12.6458 24.4129 13.2761 24.413 13.9083C24.4133 14.3347 24.3833 14.7606 24.3234 15.1827C24.8989 15.0466 25.4593 14.8534 25.9964 14.6059C26.3952 14.4234 26.7801 14.2116 27.1476 13.9723C27.2228 14.2339 27.2869 14.5003 27.3373 14.7723C27.4446 15.3348 27.4982 15.9062 27.4973 16.4788C27.4975 16.9341 27.4636 17.3888 27.3957 17.8389C28.1905 17.7565 28.9708 17.5681 29.7158 17.2789C30.1068 17.1274 30.4864 16.9483 30.8519 16.7429C30.9068 17.0489 30.9458 17.3576 30.9687 17.6677C30.9858 17.8928 30.9943 18.12 30.9943 18.3494C30.9947 18.9763 30.9301 19.6016 30.8015 20.2151C31.7376 20.1766 32.6619 19.992 33.5409 19.6679C34.1714 17.9803 34.4934 16.193 34.4914 14.3915C34.4983 13.9963 34.4869 13.6056 34.457 13.2194Z" fill="url(#paint1_linear_2092_2228)"/>
<path d="M54.7596 23.8311L48.2983 6.58431H44.5706L38.0596 23.8311H40.9423L42.6819 19.0099H50.0876L51.8023 23.8311H54.7596ZM46.2605 9.11914H46.5587L49.2426 16.5994H43.5517L46.2605 9.11914Z" fill="black"/>
<path d="M69.4585 17.1212C69.4585 12.7474 68.2656 10.1629 63.9664 10.1629C61.7795 10.1629 60.363 10.9581 59.7417 12.5238V10.4114H57.0577V28.8013H59.7417V21.7187C60.3381 23.2346 61.5558 24.0796 63.9664 24.0796C66.029 24.0796 67.4456 23.4583 68.2408 22.2157C69.0609 20.9483 69.4585 19.2584 69.4585 17.1212ZM66.6752 17.1212C66.6503 20.004 66.2278 21.8678 63.3948 21.9424C60.6115 21.9424 59.5926 20.2773 59.5926 17.1212C59.5926 13.8657 60.5866 12.2752 63.3948 12.2752C66.1533 12.2752 66.6752 14.1888 66.6752 17.1212Z" fill="black"/>
<path d="M72.6573 6.65887C72.6573 7.97598 72.9555 8.17479 74.2229 8.17479C75.4407 8.17479 75.7637 7.92628 75.7637 6.65887C75.7637 5.3666 75.4655 5.19264 74.2229 5.19264C72.9555 5.19264 72.6573 5.3666 72.6573 6.65887ZM75.5401 23.8311V10.4114H72.8561V23.8311H75.5401Z" fill="black"/>
<path d="M82.9057 10.4114C82.9057 7.57836 83.3282 6.88253 85.192 6.88253C85.7139 6.88253 86.1612 6.93223 86.534 7.00678V4.96898C86.0121 4.89443 85.4903 4.84473 85.0181 4.84473C81.862 4.89443 80.2964 5.76422 80.2218 10.2871V10.4114H78.0349V12.5486H80.2218V23.8311H82.9057V12.5486H85.863V10.4114H82.9057Z" fill="black"/>
<path d="M87.352 17.1212C87.352 22.4145 89.5389 24.0796 93.689 24.0796C97.8889 24.0796 100.026 22.4145 100.026 17.1212C100.026 11.8279 97.8889 10.1629 93.689 10.1629C89.5389 10.1629 87.352 11.8279 87.352 17.1212ZM97.2427 17.1212C97.2427 20.8241 96.4475 21.9424 93.689 21.9424C91.0299 21.9424 90.1353 20.7992 90.1353 17.1212C90.1353 13.4184 90.9554 12.2752 93.689 12.2752C96.373 12.2752 97.2427 13.4184 97.2427 17.1212Z" fill="black"/>
<path d="M104.266 23.8311L107.621 18.6123H107.77L111.025 23.8311H114.132L109.534 16.9473L113.883 10.4114H110.852L107.795 15.3816H107.646L104.539 10.4114H101.483L105.832 16.9721L101.209 23.8311H104.266Z" fill="black"/>
<path d="M123.532 10.72V25H130.092C131.612 25 132.812 24.72 133.652 24.16C134.632 23.48 135.132 22.42 135.132 20.98C135.132 20.02 134.892 19.24 134.432 18.68C133.952 18.1 133.252 17.72 132.312 17.54C133.032 17.26 133.572 16.88 133.952 16.36C134.332 15.8 134.532 15.12 134.532 14.32C134.532 13.24 134.152 12.38 133.412 11.74C132.612 11.06 131.492 10.72 130.072 10.72H123.532ZM125.872 12.64H129.492C130.452 12.64 131.132 12.8 131.572 13.12C131.972 13.42 132.192 13.92 132.192 14.6C132.192 15.34 131.972 15.88 131.572 16.22C131.152 16.54 130.452 16.72 129.452 16.72H125.872V12.64ZM125.872 18.64H129.812C130.852 18.64 131.612 18.82 132.092 19.18C132.552 19.54 132.792 20.12 132.792 20.94C132.792 21.74 132.472 22.3 131.832 22.66C131.332 22.94 130.632 23.08 129.752 23.08H125.872V18.64ZM137.34 10.44V25H139.62V10.44H137.34ZM147.019 14.38C145.439 14.38 144.179 14.88 143.239 15.92C142.279 16.94 141.819 18.24 141.819 19.84C141.819 21.42 142.279 22.72 143.219 23.72C144.179 24.76 145.439 25.28 147.019 25.28C148.579 25.28 149.859 24.76 150.819 23.72C151.739 22.72 152.219 21.42 152.219 19.84C152.219 18.24 151.739 16.94 150.799 15.92C149.839 14.88 148.579 14.38 147.019 14.38ZM147.019 16.24C147.959 16.24 148.699 16.6 149.219 17.36C149.659 18 149.899 18.84 149.899 19.84C149.899 20.82 149.659 21.64 149.219 22.3C148.699 23.04 147.959 23.42 147.019 23.42C146.059 23.42 145.339 23.04 144.819 22.3C144.379 21.66 144.159 20.84 144.159 19.84C144.159 18.84 144.379 18 144.819 17.36C145.339 16.6 146.059 16.24 147.019 16.24ZM158.511 14.38C157.111 14.38 155.971 14.86 155.111 15.82C154.231 16.78 153.811 18.04 153.811 19.6C153.811 21.16 154.231 22.42 155.111 23.36C155.971 24.28 157.111 24.76 158.511 24.76C159.791 24.76 160.811 24.22 161.571 23.16V24.68C161.571 26.52 160.711 27.44 158.991 27.44C158.191 27.44 157.611 27.32 157.251 27.1C156.871 26.86 156.631 26.46 156.511 25.88H154.231C154.431 27.04 154.911 27.88 155.671 28.44C156.431 28.96 157.531 29.24 158.991 29.24C162.231 29.24 163.851 27.6 163.851 24.34V14.66H161.571V15.92C160.811 14.88 159.791 14.38 158.511 14.38ZM158.871 16.22C159.731 16.22 160.411 16.52 160.891 17.12C161.351 17.72 161.591 18.54 161.591 19.6C161.591 20.64 161.351 21.44 160.891 22.04C160.411 22.6 159.731 22.9 158.851 22.9C157.851 22.9 157.151 22.56 156.711 21.9C156.331 21.34 156.151 20.58 156.151 19.6C156.151 18.52 156.391 17.7 156.871 17.12C157.351 16.52 158.011 16.22 158.871 16.22Z" fill="#101828" fill-opacity="0.56"/>
<defs>
<linearGradient id="paint0_linear_2092_2228" x1="0" y1="0" x2="31.9101" y2="34.3965" gradientUnits="userSpaceOnUse">
<stop offset="0.01" stop-color="#FF387E"/>
<stop offset="0.18" stop-color="#FF386D"/>
<stop offset="0.67" stop-color="#F44A53"/>
<stop offset="1" stop-color="#F35E3A"/>
</linearGradient>
<linearGradient id="paint1_linear_2092_2228" x1="0" y1="0" x2="31.9101" y2="34.3965" gradientUnits="userSpaceOnUse">
<stop offset="0.01" stop-color="#FF387E"/>
<stop offset="0.18" stop-color="#FF386D"/>
<stop offset="0.67" stop-color="#F44A53"/>
<stop offset="1" stop-color="#F35E3A"/>
</linearGradient>
</defs>
</svg>
            <p style="margin-top: 16px; margin-bottom: 20px;">节省研发团队的每一分钟</p>
        </div>

        <div class="ui-footer-link">
            <p class="ui-footer-link-title">产品</p>
            <a href="https://apifox.com/help/app/changelog/" class="ui-footer-link-item" target="_blank">产品更新日志</a>
            <a href="https://apifox.com/help/applications-and-plugins/idea/start" class="ui-footer-link-item" target="_blank">IDEA 插件</a>
            <a href="https://apifox.com/help/" class="ui-footer-link-item" target="_blank">帮助中心</a>
            <a href="https://apifox.com/help/" class="ui-footer-link-item" target="_blank">快速上手</a>
            <a href="https://apifox.com/help/app/privatization-deployment/" class="ui-footer-link-item" target="_blank">私有化部署</a>
            <a href="https://apifox.com/blog/" class="ui-footer-link-item" target="_blank">Blog</a>
        </div>

        <div class="ui-footer-link">
            <p class="ui-footer-link-title">关于</p>
            <a href="https://apifox.com/help/faq" class="ui-footer-link-item" target="_blank">FAQ</a>
            <a href="https://apifox.com/help/overview/contact-us#%E7%94%A8%E6%88%B7%E7%BE%A4/" class="ui-footer-link-item" target="_blank">联系我们</a>
            <a href="https://www.zhipin.com/gongsir/a3e8ca9375c8c8b91XV53Nq0EVM~.html?ka=company-jobs/" class="ui-footer-link-item" target="_blank">加入我们</a>
            <a href="https://wenjuan.feishu.cn/m/cfm?t=sM9oJhYsViMi-likl" class="ui-footer-link-item" target="_blank">成为合作伙伴</a>
        </div>

        <div class="ui-footer-link">
            <p class="ui-footer-link-title">资源</p>
            <a href="https://apifox.com/help/api-docs/api-design#%E5%BF%AB%E9%80%9F%E4%B8%8A%E6%89%8B/" class="ui-footer-link-item" target="_blank">接口 (API) 文档工具</a>
            <a href="https://apifox.com/help/api-docs/api-design#%E5%BF%AB%E9%80%9F%E4%B8%8A%E6%89%8B/" class="ui-footer-link-item" target="_blank">接口 (API) 文档管理工具</a>
            <a href="https://apifox.com/help/automated-testing/test-scenarios/creating-test-case" class="ui-footer-link-item" target="_blank">接口 (API) 自动化测试工具</a>
            <a href="https://apifox.com/help/api-mock/intro-to-mock" class="ui-footer-link-item" target="_blank">接口 (API) Mock 工具</a>
        </div>
    </div>

    <div style="background: rgba(0, 0, 0, 0.04); font-size: 14px; padding: 20px 0; text-align: center; color: rgba(0, 0, 0, 0.40);">
        ©2024 Apifox All Rights Reserved 广州睿狐科技有限公司 <span>粤 ICP 备 2021010720 号</span>
    </div>
</footer>
</div>


<script src="/blog/assets/built/casper.js?v=526a981474"></script>
<script src="https://cdn.apifox.com/static/js/highlight.js/11.9.0/highlight.min.js"  ></script>
<script  >hljs.highlightAll();</script>
<script>
$(document).ready(function () {
    // Mobile Menu Trigger
    $('.gh-burger').click(function () {
        $('body').toggleClass('gh-head-open');
    });
    // FitVids - Makes video embeds responsive
    $(".gh-content").fitVids();
});
</script>

<script>
    window.addEventListener('message', function(e) {
        if (e.data.size) {
            var iframeNode = document.getElementById('footer-iframe')
            iframeNode.setAttribute('height', e.data.size.height + 10)
        }
    }, false)
</script>

<script src="https://cdn.apifox.com/www/assets/js/apifox-blog-event-tracking.min.js" async="true"></script>

<script defer="" src="https://cdn.apifox.com/www/assets/js/blog/sodo-search/0.0.1/umd/sodo-search.min.js" data-styles="https://cdn.apifox.com/www/assets/js/blog/sodo-search/0.0.1/umd/main.css" data-sodo-search="https://www.apifox.com/apiskills"></script>
<script type="text/javascript" defer src="https://cdn.apifox.com/www/assets/js/user-tracking.min.js"></script>
</body>
</html>
