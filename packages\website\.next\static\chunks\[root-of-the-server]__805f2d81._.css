/* [next]/internal/font/google/geist_a71539c9.module.css [app-client] (css) */
@font-face {
  font-family: Geist;
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url("../media/8a480f0b521d4e75-s.8e0177b5.woff2") format("woff2");
  unicode-range: U+301, U+400-45F, U+490-491, U+4B0-4B1, U+2116;
}

@font-face {
  font-family: Geist;
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url("../media/7178b3e590c64307-s.b97b3418.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Geist;
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url("../media/caa3a2e1cccd8315-s.p.853070df.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Geist Fallback;
  src: local(Arial);
  ascent-override: 95.94%;
  descent-override: 28.16%;
  line-gap-override: 0.0%;
  size-adjust: 104.76%;
}

.geist_a71539c9-module__T19VSG__className {
  font-family: Geist, Geist Fallback;
  font-style: normal;
}

.geist_a71539c9-module__T19VSG__variable {
  --font-geist-sans: "Geist", "Geist Fallback";
}

/* [next]/internal/font/google/geist_mono_8d43a2aa.module.css [app-client] (css) */
@font-face {
  font-family: Geist Mono;
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url("../media/4fa387ec64143e14-s.c1fdd6c2.woff2") format("woff2");
  unicode-range: U+301, U+400-45F, U+490-491, U+4B0-4B1, U+2116;
}

@font-face {
  font-family: Geist Mono;
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url("../media/bbc41e54d2fcbd21-s.799d8ef8.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Geist Mono;
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url("../media/797e433ab948586e-s.p.dbea232f.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Geist Mono Fallback;
  src: local(Arial);
  ascent-override: 74.67%;
  descent-override: 21.92%;
  line-gap-override: 0.0%;
  size-adjust: 134.59%;
}

.geist_mono_8d43a2aa-module__8Li5zG__className {
  font-family: Geist Mono, Geist Mono Fallback;
  font-style: normal;
}

.geist_mono_8d43a2aa-module__8Li5zG__variable {
  --font-geist-mono: "Geist Mono", "Geist Mono Fallback";
}

/* [project]/packages/website/src/app/globals.css [app-client] (css) */
@layer properties {
  @supports (((-webkit-hyphens: none)) and (not (margin-trim: inline))) or ((-moz-orient: inline) and (not (color: rgb(from red r g b)))) {
    *, :before, :after {
      --tw-translate-x: 0;
      --tw-translate-y: 0;
      --tw-translate-z: 0;
      --tw-scale-x: 1;
      --tw-scale-y: 1;
      --tw-scale-z: 1;
      --tw-rotate-x: initial;
      --tw-rotate-y: initial;
      --tw-rotate-z: initial;
      --tw-skew-x: initial;
      --tw-skew-y: initial;
      --tw-space-y-reverse: 0;
      --tw-space-x-reverse: 0;
      --tw-divide-x-reverse: 0;
      --tw-border-style: solid;
      --tw-divide-y-reverse: 0;
      --tw-gradient-position: initial;
      --tw-gradient-from: rgba(0, 0, 0, 0);
      --tw-gradient-via: rgba(0, 0, 0, 0);
      --tw-gradient-to: rgba(0, 0, 0, 0);
      --tw-gradient-stops: initial;
      --tw-gradient-via-stops: initial;
      --tw-gradient-from-position: 0%;
      --tw-gradient-via-position: 50%;
      --tw-gradient-to-position: 100%;
      --tw-leading: initial;
      --tw-font-weight: initial;
      --tw-tracking: initial;
      --tw-ordinal: initial;
      --tw-slashed-zero: initial;
      --tw-numeric-figure: initial;
      --tw-numeric-spacing: initial;
      --tw-numeric-fraction: initial;
      --tw-shadow: 0 0 rgba(0, 0, 0, 0);
      --tw-shadow-color: initial;
      --tw-shadow-alpha: 100%;
      --tw-inset-shadow: 0 0 rgba(0, 0, 0, 0);
      --tw-inset-shadow-color: initial;
      --tw-inset-shadow-alpha: 100%;
      --tw-ring-color: initial;
      --tw-ring-shadow: 0 0 rgba(0, 0, 0, 0);
      --tw-inset-ring-color: initial;
      --tw-inset-ring-shadow: 0 0 rgba(0, 0, 0, 0);
      --tw-ring-inset: initial;
      --tw-ring-offset-width: 0px;
      --tw-ring-offset-color: #fff;
      --tw-ring-offset-shadow: 0 0 rgba(0, 0, 0, 0);
      --tw-outline-style: solid;
      --tw-blur: initial;
      --tw-brightness: initial;
      --tw-contrast: initial;
      --tw-grayscale: initial;
      --tw-hue-rotate: initial;
      --tw-invert: initial;
      --tw-opacity: initial;
      --tw-saturate: initial;
      --tw-sepia: initial;
      --tw-drop-shadow: initial;
      --tw-drop-shadow-color: initial;
      --tw-drop-shadow-alpha: 100%;
      --tw-drop-shadow-size: initial;
      --tw-backdrop-blur: initial;
      --tw-backdrop-brightness: initial;
      --tw-backdrop-contrast: initial;
      --tw-backdrop-grayscale: initial;
      --tw-backdrop-hue-rotate: initial;
      --tw-backdrop-invert: initial;
      --tw-backdrop-opacity: initial;
      --tw-backdrop-saturate: initial;
      --tw-backdrop-sepia: initial;
      --tw-duration: initial;
      --tw-ease: initial;
      --tw-content: "";
    }

    ::backdrop {
      --tw-translate-x: 0;
      --tw-translate-y: 0;
      --tw-translate-z: 0;
      --tw-scale-x: 1;
      --tw-scale-y: 1;
      --tw-scale-z: 1;
      --tw-rotate-x: initial;
      --tw-rotate-y: initial;
      --tw-rotate-z: initial;
      --tw-skew-x: initial;
      --tw-skew-y: initial;
      --tw-space-y-reverse: 0;
      --tw-space-x-reverse: 0;
      --tw-divide-x-reverse: 0;
      --tw-border-style: solid;
      --tw-divide-y-reverse: 0;
      --tw-gradient-position: initial;
      --tw-gradient-from: rgba(0, 0, 0, 0);
      --tw-gradient-via: rgba(0, 0, 0, 0);
      --tw-gradient-to: rgba(0, 0, 0, 0);
      --tw-gradient-stops: initial;
      --tw-gradient-via-stops: initial;
      --tw-gradient-from-position: 0%;
      --tw-gradient-via-position: 50%;
      --tw-gradient-to-position: 100%;
      --tw-leading: initial;
      --tw-font-weight: initial;
      --tw-tracking: initial;
      --tw-ordinal: initial;
      --tw-slashed-zero: initial;
      --tw-numeric-figure: initial;
      --tw-numeric-spacing: initial;
      --tw-numeric-fraction: initial;
      --tw-shadow: 0 0 rgba(0, 0, 0, 0);
      --tw-shadow-color: initial;
      --tw-shadow-alpha: 100%;
      --tw-inset-shadow: 0 0 rgba(0, 0, 0, 0);
      --tw-inset-shadow-color: initial;
      --tw-inset-shadow-alpha: 100%;
      --tw-ring-color: initial;
      --tw-ring-shadow: 0 0 rgba(0, 0, 0, 0);
      --tw-inset-ring-color: initial;
      --tw-inset-ring-shadow: 0 0 rgba(0, 0, 0, 0);
      --tw-ring-inset: initial;
      --tw-ring-offset-width: 0px;
      --tw-ring-offset-color: #fff;
      --tw-ring-offset-shadow: 0 0 rgba(0, 0, 0, 0);
      --tw-outline-style: solid;
      --tw-blur: initial;
      --tw-brightness: initial;
      --tw-contrast: initial;
      --tw-grayscale: initial;
      --tw-hue-rotate: initial;
      --tw-invert: initial;
      --tw-opacity: initial;
      --tw-saturate: initial;
      --tw-sepia: initial;
      --tw-drop-shadow: initial;
      --tw-drop-shadow-color: initial;
      --tw-drop-shadow-alpha: 100%;
      --tw-drop-shadow-size: initial;
      --tw-backdrop-blur: initial;
      --tw-backdrop-brightness: initial;
      --tw-backdrop-contrast: initial;
      --tw-backdrop-grayscale: initial;
      --tw-backdrop-hue-rotate: initial;
      --tw-backdrop-invert: initial;
      --tw-backdrop-opacity: initial;
      --tw-backdrop-saturate: initial;
      --tw-backdrop-sepia: initial;
      --tw-duration: initial;
      --tw-ease: initial;
      --tw-content: "";
    }
  }
}

@layer theme {
  :root, :host {
    --font-sans: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", SimSun, sans-serif;
    --font-mono: var(--font-geist-mono);
    --color-red-400: #ff6568;
    --color-red-500: #fb2c36;
    --color-red-600: #e40014;
    --color-amber-500: #f99c00;
    --color-yellow-500: #edb200;
    --color-yellow-600: #cd8900;
    --color-green-50: #f0fdf4;
    --color-green-100: #dcfce7;
    --color-green-200: #b9f8cf;
    --color-green-500: #00c758;
    --color-green-600: #00a544;
    --color-green-800: #016630;
    --color-emerald-500: #00bb7f;
    --color-emerald-600: #009767;
    --color-blue-50: #eff6ff;
    --color-blue-100: #dbeafe;
    --color-blue-200: #bedbff;
    --color-blue-400: #54a2ff;
    --color-blue-500: #3080ff;
    --color-blue-600: #155dfc;
    --color-blue-700: #1447e6;
    --color-blue-800: #193cb8;
    --color-purple-50: #faf5ff;
    --color-purple-100: #f3e8ff;
    --color-purple-200: #e9d5ff;
    --color-purple-400: #c07eff;
    --color-purple-500: #ac4bff;
    --color-purple-600: #9810fa;
    --color-purple-700: #8200da;
    --color-slate-200: #e2e8f0;
    --color-slate-900: #0f172b;
    --color-gray-50: #f9fafb;
    --color-gray-100: #f3f4f6;
    --color-gray-200: #e5e7eb;
    --color-gray-300: #d1d5dc;
    --color-gray-400: #99a1af;
    --color-gray-500: #6a7282;
    --color-gray-600: #4a5565;
    --color-gray-700: #364153;
    --color-gray-800: #1e2939;
    --color-gray-900: #101828;
    --color-gray-950: #030712;
    --color-neutral-200: #e5e5e5;
    --color-neutral-300: #d4d4d4;
    --color-neutral-400: #a1a1a1;
    --color-neutral-500: #737373;
    --color-neutral-600: #525252;
    --color-neutral-700: #404040;
    --color-neutral-800: #262626;
    --color-black: #000;
    --color-white: #fff;
    --spacing: .25rem;
    --breakpoint-xl: 80rem;
    --container-xs: 20rem;
    --container-sm: 24rem;
    --container-md: 28rem;
    --container-lg: 32rem;
    --container-xl: 36rem;
    --container-2xl: 42rem;
    --container-3xl: 48rem;
    --container-4xl: 56rem;
    --container-7xl: 80rem;
    --text-xs: .75rem;
    --text-xs--line-height: calc(1 / .75);
    --text-sm: .875rem;
    --text-sm--line-height: calc(1.25 / .875);
    --text-base: 1rem;
    --text-base--line-height: calc(1.5 / 1);
    --text-lg: 1.125rem;
    --text-lg--line-height: calc(1.75 / 1.125);
    --text-xl: 1.25rem;
    --text-xl--line-height: calc(1.75 / 1.25);
    --text-2xl: 1.5rem;
    --text-2xl--line-height: calc(2 / 1.5);
    --text-3xl: 1.875rem;
    --text-3xl--line-height: calc(2.25 / 1.875);
    --text-4xl: 2.25rem;
    --text-4xl--line-height: calc(2.5 / 2.25);
    --text-5xl: 3rem;
    --text-5xl--line-height: 1;
    --text-6xl: 3.75rem;
    --text-6xl--line-height: 1;
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    --font-weight-extrabold: 800;
    --tracking-tight: -.025em;
    --tracking-normal: 0em;
    --tracking-wide: .025em;
    --tracking-wider: .05em;
    --leading-normal: 1.5;
    --leading-relaxed: 1.625;
    --radius-sm: .25rem;
    --radius-md: .375rem;
    --radius-lg: .5rem;
    --radius-xl: .75rem;
    --radius-2xl: 1rem;
    --radius-3xl: 1.5rem;
    --ease-out: cubic-bezier(0, 0, .2, 1);
    --ease-in-out: cubic-bezier(.4, 0, .2, 1);
    --animate-spin: spin 1s linear infinite;
    --animate-pulse: pulse 2s cubic-bezier(.4, 0, .6, 1) infinite;
    --blur-sm: 8px;
    --blur-lg: 16px;
    --blur-xl: 24px;
    --default-transition-duration: .15s;
    --default-transition-timing-function: cubic-bezier(.4, 0, .2, 1);
    --default-font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", SimSun, sans-serif;
    --default-mono-font-family: var(--font-geist-mono);
  }

  @supports (color: color(display-p3 0 0 0)) {
    :root, :host {
      --color-red-400: color(display-p3 .933534 .431676 .423491);
      --color-red-500: color(display-p3 .903738 .262579 .253307);
      --color-red-600: color(display-p3 .830323 .140383 .133196);
      --color-amber-500: color(display-p3 .93994 .620584 .0585367);
      --color-yellow-500: color(display-p3 .903651 .703062 .0745389);
      --color-yellow-600: color(display-p3 .776342 .542492 .041709);
      --color-green-50: color(display-p3 .950677 .990571 .959366);
      --color-green-100: color(display-p3 .885269 .984329 .910368);
      --color-green-200: color(display-p3 .776442 .964383 .823412);
      --color-green-500: color(display-p3 .308734 .774754 .374307);
      --color-green-600: color(display-p3 .243882 .640824 .294808);
      --color-green-800: color(display-p3 .168568 .395123 .211217);
      --color-emerald-500: color(display-p3 .267113 .726847 .508397);
      --color-emerald-600: color(display-p3 .206557 .589057 .413962);
      --color-blue-50: color(display-p3 .941826 .963151 .995385);
      --color-blue-100: color(display-p3 .869214 .915931 .989622);
      --color-blue-200: color(display-p3 .76688 .855207 .987483);
      --color-blue-400: color(display-p3 .397443 .62813 .992116);
      --color-blue-500: color(display-p3 .266422 .491219 .988624);
      --color-blue-600: color(display-p3 .174493 .358974 .950247);
      --color-blue-700: color(display-p3 .1379 .274983 .867624);
      --color-blue-800: color(display-p3 .134023 .230647 .695537);
      --color-purple-50: color(display-p3 .977045 .961759 .996715);
      --color-purple-100: color(display-p3 .945034 .910569 .992972);
      --color-purple-200: color(display-p3 .901181 .835978 .992237);
      --color-purple-400: color(display-p3 .719919 .492497 .995173);
      --color-purple-500: color(display-p3 .629519 .30089 .990817);
      --color-purple-600: color(display-p3 .546729 .130167 .94439);
      --color-purple-700: color(display-p3 .465298 .0652579 .824397);
      --color-slate-200: color(display-p3 .890322 .909405 .939294);
      --color-slate-900: color(display-p3 .0639692 .0891152 .163036);
      --color-gray-50: color(display-p3 .977213 .98084 .985102);
      --color-gray-100: color(display-p3 .953567 .956796 .964321);
      --color-gray-200: color(display-p3 .899787 .906171 .92106);
      --color-gray-300: color(display-p3 .822033 .835264 .858521);
      --color-gray-400: color(display-p3 .605734 .630385 .680158);
      --color-gray-500: color(display-p3 .421287 .446085 .504784);
      --color-gray-600: color(display-p3 .297358 .332176 .39043);
      --color-gray-700: color(display-p3 .219968 .253721 .318679);
      --color-gray-800: color(display-p3 .125854 .159497 .216835);
      --color-gray-900: color(display-p3 .070423 .0928982 .151928);
      --color-gray-950: color(display-p3 .0144294 .0270755 .068534);
      --color-neutral-200: color(display-p3 .898161 .898161 .898161);
      --color-neutral-300: color(display-p3 .831444 .831444 .831444);
      --color-neutral-400: color(display-p3 .630163 .630163 .630163);
      --color-neutral-500: color(display-p3 .451519 .451519 .451519);
      --color-neutral-600: color(display-p3 .321993 .321993 .321993);
      --color-neutral-700: color(display-p3 .250471 .250471 .250471);
      --color-neutral-800: color(display-p3 .149382 .149382 .149382);
    }
  }

  @supports (color: lab(0% 0 0)) {
    :root, :host {
      --color-red-400: lab(63.7053% 60.7449 31.3109);
      --color-red-500: lab(55.4814% 75.0732 48.8528);
      --color-red-600: lab(48.4493% 77.4328 61.5452);
      --color-amber-500: lab(72.7183% 31.8672 97.9407);
      --color-yellow-500: lab(76.3898% 14.5258 98.4589);
      --color-yellow-600: lab(62.7799% 22.4198 86.1544);
      --color-green-50: lab(98.1563% -5.60117 2.75913);
      --color-green-100: lab(96.186% -13.8464 6.52362);
      --color-green-200: lab(92.4222% -26.4702 12.9427);
      --color-green-500: lab(70.5521% -66.5147 45.8072);
      --color-green-600: lab(59.0978% -58.6621 41.2579);
      --color-green-800: lab(37.4616% -36.7971 22.9692);
      --color-emerald-500: lab(66.9756% -58.27 19.5419);
      --color-emerald-600: lab(55.0481% -49.9246 15.93);
      --color-blue-50: lab(96.492% -1.14647 -5.11479);
      --color-blue-100: lab(92.0301% -2.24757 -11.6453);
      --color-blue-200: lab(86.15% -4.04379 -21.0797);
      --color-blue-400: lab(65.0361% -1.42062 -56.9803);
      --color-blue-500: lab(54.1736% 13.3368 -74.6839);
      --color-blue-600: lab(44.0605% 29.0279 -86.0352);
      --color-blue-700: lab(36.9089% 35.0961 -85.6872);
      --color-blue-800: lab(30.2514% 27.7854 -70.2699);
      --color-purple-50: lab(97.1626% 2.99937 -4.13398);
      --color-purple-100: lab(93.3333% 6.9744 -9.83434);
      --color-purple-200: lab(87.8405% 13.4282 -18.7159);
      --color-purple-400: lab(63.6946% 47.6127 -59.2066);
      --color-purple-500: lab(52.0183% 66.11 -78.2316);
      --color-purple-600: lab(43.0295% 75.21 -86.5669);
      --color-purple-700: lab(36.1758% 69.8525 -80.0381);
      --color-slate-200: lab(91.7353% -.998765 -4.76968);
      --color-slate-900: lab(7.78673% 1.82346 -15.0537);
      --color-gray-50: lab(98.2596% -.247031 -.706708);
      --color-gray-100: lab(96.1596% -.082314 -1.13575);
      --color-gray-200: lab(91.6229% -.159085 -2.26791);
      --color-gray-300: lab(85.1236% -.612259 -3.7138);
      --color-gray-400: lab(65.9269% -.832707 -8.17474);
      --color-gray-500: lab(47.7841% -.393212 -10.0268);
      --color-gray-600: lab(35.6337% -1.58697 -10.8425);
      --color-gray-700: lab(27.1134% -.956401 -12.3224);
      --color-gray-800: lab(16.1051% -1.18239 -11.7533);
      --color-gray-900: lab(8.11897% .811279 -12.254);
      --color-gray-950: lab(1.90334% .278696 -5.48866);
      --color-neutral-200: lab(90.952% -.0000596046 0);
      --color-neutral-300: lab(84.92% 0 0);
      --color-neutral-400: lab(66.128% -.0000298023 .0000119209);
      --color-neutral-500: lab(48.496% 0 0);
      --color-neutral-600: lab(34.924% 0 0);
      --color-neutral-700: lab(27.036% 0 0);
      --color-neutral-800: lab(15.204% 0 0);
    }
  }
}

@layer base {
  *, :after, :before {
    box-sizing: border-box;
    border: 0 solid;
    margin: 0;
    padding: 0;
  }

  ::backdrop {
    box-sizing: border-box;
    border: 0 solid;
    margin: 0;
    padding: 0;
  }

  ::-webkit-file-upload-button {
    box-sizing: border-box;
    border: 0 solid;
    margin: 0;
    padding: 0;
  }

  ::file-selector-button {
    box-sizing: border-box;
    border: 0 solid;
    margin: 0;
    padding: 0;
  }

  html, :host {
    -webkit-text-size-adjust: 100%;
    -moz-tab-size: 4;
    tab-size: 4;
    line-height: 1.5;
    font-family: var(--default-font-family, ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji");
    font-feature-settings: var(--default-font-feature-settings, normal);
    font-variation-settings: var(--default-font-variation-settings, normal);
    -webkit-tap-highlight-color: transparent;
  }

  hr {
    height: 0;
    color: inherit;
    border-top-width: 1px;
  }

  abbr:where([title]) {
    -webkit-text-decoration: underline dotted;
    text-decoration: underline dotted;
  }

  h1, h2, h3, h4, h5, h6 {
    font-size: inherit;
    font-weight: inherit;
  }

  a {
    color: inherit;
    -webkit-text-decoration: inherit;
    -webkit-text-decoration: inherit;
    text-decoration: inherit;
  }

  b, strong {
    font-weight: bolder;
  }

  code, kbd, samp, pre {
    font-family: var(--default-mono-font-family, ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace);
    font-feature-settings: var(--default-mono-font-feature-settings, normal);
    font-variation-settings: var(--default-mono-font-variation-settings, normal);
    font-size: 1em;
  }

  small {
    font-size: 80%;
  }

  sub, sup {
    vertical-align: baseline;
    font-size: 75%;
    line-height: 0;
    position: relative;
  }

  sub {
    bottom: -.25em;
  }

  sup {
    top: -.5em;
  }

  table {
    text-indent: 0;
    border-color: inherit;
    border-collapse: collapse;
  }

  :-moz-focusring {
    outline: auto;
  }

  progress {
    vertical-align: baseline;
  }

  summary {
    display: list-item;
  }

  ol, ul, menu {
    list-style: none;
  }

  img, svg, video, canvas, audio, iframe, embed, object {
    vertical-align: middle;
    display: block;
  }

  img, video {
    max-width: 100%;
    height: auto;
  }

  button, input, select, optgroup, textarea {
    font: inherit;
    font-feature-settings: inherit;
    font-variation-settings: inherit;
    letter-spacing: inherit;
    color: inherit;
    opacity: 1;
    background-color: rgba(0, 0, 0, 0);
    border-radius: 0;
  }

  ::-webkit-file-upload-button {
    font: inherit;
    font-feature-settings: inherit;
    font-variation-settings: inherit;
    letter-spacing: inherit;
    color: inherit;
    opacity: 1;
    background-color: rgba(0, 0, 0, 0);
    border-radius: 0;
  }

  ::file-selector-button {
    font: inherit;
    font-feature-settings: inherit;
    font-variation-settings: inherit;
    letter-spacing: inherit;
    color: inherit;
    opacity: 1;
    background-color: rgba(0, 0, 0, 0);
    border-radius: 0;
  }

  :where(select:-webkit-any([multiple], [size])) optgroup {
    font-weight: bolder;
  }

  :where(select:-moz-any([multiple], [size])) optgroup {
    font-weight: bolder;
  }

  :where(select:is([multiple], [size])) optgroup {
    font-weight: bolder;
  }

  :where(select:-webkit-any([multiple], [size])) optgroup option:not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    padding-left: 20px;
  }

  :where(select:-moz-any([multiple], [size])) optgroup option:not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    padding-left: 20px;
  }

  :where(select:is([multiple], [size])) optgroup option:not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    padding-left: 20px;
  }

  :where(select:-webkit-any([multiple], [size])) optgroup option:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    padding-right: 20px;
  }

  :where(select:-moz-any([multiple], [size])) optgroup option:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    padding-right: 20px;
  }

  :where(select:is([multiple], [size])) optgroup option:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    padding-right: 20px;
  }

  :not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)))::-webkit-file-upload-button {
    margin-right: 4px;
  }

  :not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)))::file-selector-button {
    margin-right: 4px;
  }

  :not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)))::file-selector-button {
    margin-right: 4px;
  }

  :-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))::-webkit-file-upload-button {
    margin-left: 4px;
  }

  :-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))::file-selector-button {
    margin-left: 4px;
  }

  :is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))::file-selector-button {
    margin-left: 4px;
  }

  ::placeholder {
    opacity: 1;
  }

  @supports (not ((-webkit-appearance: -apple-pay-button))) or (contain-intrinsic-size: 1px) {
    ::placeholder {
      color: currentColor;
    }

    @supports (color: color-mix(in lab, red, red)) {
      ::placeholder {
        color: color-mix(in oklab, currentcolor 50%, transparent);
      }
    }
  }

  textarea {
    resize: vertical;
  }

  ::-webkit-search-decoration {
    -webkit-appearance: none;
  }

  ::-webkit-date-and-time-value {
    min-height: 1lh;
    text-align: inherit;
  }

  ::-webkit-datetime-edit {
    display: inline-flex;
  }

  ::-webkit-datetime-edit-fields-wrapper {
    padding: 0;
  }

  ::-webkit-datetime-edit {
    padding-top: 0;
    padding-bottom: 0;
  }

  ::-webkit-datetime-edit-year-field {
    padding-top: 0;
    padding-bottom: 0;
  }

  ::-webkit-datetime-edit-month-field {
    padding-top: 0;
    padding-bottom: 0;
  }

  ::-webkit-datetime-edit-day-field {
    padding-top: 0;
    padding-bottom: 0;
  }

  ::-webkit-datetime-edit-hour-field {
    padding-top: 0;
    padding-bottom: 0;
  }

  ::-webkit-datetime-edit-minute-field {
    padding-top: 0;
    padding-bottom: 0;
  }

  ::-webkit-datetime-edit-second-field {
    padding-top: 0;
    padding-bottom: 0;
  }

  ::-webkit-datetime-edit-millisecond-field {
    padding-top: 0;
    padding-bottom: 0;
  }

  ::-webkit-datetime-edit-meridiem-field {
    padding-top: 0;
    padding-bottom: 0;
  }

  ::-webkit-calendar-picker-indicator {
    line-height: 1;
  }

  :-moz-ui-invalid {
    box-shadow: none;
  }

  button {
    -webkit-appearance: button;
    -moz-appearance: button;
    appearance: button;
  }

  input:where([type="button"], [type="reset"], [type="submit"]) {
    -webkit-appearance: button;
    -moz-appearance: button;
    appearance: button;
  }

  ::-webkit-file-upload-button {
    -webkit-appearance: button;
    -moz-appearance: button;
    appearance: button;
  }

  ::file-selector-button {
    -webkit-appearance: button;
    -moz-appearance: button;
    appearance: button;
  }

  ::-webkit-inner-spin-button {
    height: auto;
  }

  ::-webkit-outer-spin-button {
    height: auto;
  }

  [hidden]:where(:not([hidden="until-found"])) {
    display: none !important;
  }
}

@layer components;

@layer utilities {
  .\@container\/bottom-banner-qr-code {
    container: bottom-banner-qr-code / inline-size;
  }

  .\@container\/doc-content {
    container: doc-content / inline-size;
  }

  .\@container\/environment-url {
    container: environment-url / inline-size;
  }

  .\@container\/footer {
    container: footer / inline-size;
  }

  .\@container\/http-api-content {
    container: http-api-content / inline-size;
  }

  .\@container\/request-raw-card {
    container: request-raw-card / inline-size;
  }

  .\@container\/request-schema-card {
    container: request-schema-card / inline-size;
  }

  .\@container\/response-panel {
    container: response-panel / inline-size;
  }

  .pointer-events-auto {
    pointer-events: auto;
  }

  .pointer-events-none {
    pointer-events: none;
  }

  .collapse {
    visibility: collapse;
  }

  .invisible {
    visibility: hidden;
  }

  .visible {
    visibility: visible;
  }

  .visible\! {
    visibility: visible !important;
  }

  .sr-only {
    clip-path: inset(50%);
    white-space: nowrap;
    border-width: 0;
    width: 1px;
    height: 1px;
    margin: -1px;
    padding: 0;
    position: absolute;
    overflow: hidden;
  }

  .absolute {
    position: absolute;
  }

  .fixed {
    position: fixed;
  }

  .relative {
    position: relative;
  }

  .static {
    position: static;
  }

  .sticky {
    position: -webkit-sticky;
    position: sticky;
  }

  .inset-0 {
    inset: calc(var(--spacing) * 0);
  }

  .inset-x-0 {
    inset-inline: calc(var(--spacing) * 0);
  }

  .inset-y-0 {
    inset-block: calc(var(--spacing) * 0);
  }

  .-top-0\.5 {
    top: calc(var(--spacing) * -.5);
  }

  .-top-2 {
    top: calc(var(--spacing) * -2);
  }

  .-top-4 {
    top: calc(var(--spacing) * -4);
  }

  .-top-px {
    top: -1px;
  }

  .top-0 {
    top: calc(var(--spacing) * 0);
  }

  .top-2 {
    top: calc(var(--spacing) * 2);
  }

  .top-6 {
    top: calc(var(--spacing) * 6);
  }

  .top-\[0\.4rem\] {
    top: .4rem;
  }

  .top-\[1\.2rem\] {
    top: 1.2rem;
  }

  .top-\[2rem\] {
    top: 2rem;
  }

  .top-\[4em\] {
    top: 4em;
  }

  .top-\[57px\] {
    top: 57px;
  }

  .top-\[160px\] {
    top: 160px;
  }

  .top-\[calc\(6\.5rem-var\(--sidenav-move-up\,0px\)\)\] {
    top: calc(6.5rem - var(--sidenav-move-up, 0px));
  }

  .top-full {
    top: 100%;
  }

  .-right-0\.5 {
    right: calc(var(--spacing) * -.5);
  }

  .right-0 {
    right: calc(var(--spacing) * 0);
  }

  .right-2 {
    right: calc(var(--spacing) * 2);
  }

  .right-2\.5 {
    right: calc(var(--spacing) * 2.5);
  }

  .right-6 {
    right: calc(var(--spacing) * 6);
  }

  .right-auto {
    right: auto;
  }

  .bottom-0 {
    bottom: calc(var(--spacing) * 0);
  }

  .bottom-6 {
    bottom: calc(var(--spacing) * 6);
  }

  .bottom-\[-9px\] {
    bottom: -9px;
  }

  .bottom-\[9px\] {
    bottom: 9px;
  }

  .bottom-\[100px\] {
    bottom: 100px;
  }

  .-left-1 {
    left: calc(var(--spacing) * -1);
  }

  .left-0 {
    left: calc(var(--spacing) * 0);
  }

  .left-1 {
    left: calc(var(--spacing) * 1);
  }

  .left-1\/2 {
    left: 50%;
  }

  .left-2 {
    left: calc(var(--spacing) * 2);
  }

  .left-2\/4 {
    left: 50%;
  }

  .left-6 {
    left: calc(var(--spacing) * 6);
  }

  .left-\[-59px\] {
    left: -59px;
  }

  .left-\[-80px\] {
    left: -80px;
  }

  .left-\[42px\] {
    left: 42px;
  }

  .isolate {
    isolation: isolate;
  }

  .\!z-\[1002\] {
    z-index: 1002 !important;
  }

  .-z-10 {
    z-index: calc(10 * -1);
  }

  .z-2 {
    z-index: 2;
  }

  .z-10 {
    z-index: 10;
  }

  .z-20 {
    z-index: 20;
  }

  .z-30 {
    z-index: 30;
  }

  .z-40 {
    z-index: 40;
  }

  .z-50 {
    z-index: 50;
  }

  .z-2147483647 {
    z-index: 2147483647;
  }

  .z-\[1\] {
    z-index: 1;
  }

  .z-\[2\] {
    z-index: 2;
  }

  .z-\[8\] {
    z-index: 8;
  }

  .z-\[9\] {
    z-index: 9;
  }

  .z-\[50\] {
    z-index: 50;
  }

  .z-\[60\] {
    z-index: 60;
  }

  .z-\[1000\] {
    z-index: 1000;
  }

  .order-1 {
    order: 1;
  }

  .order-last {
    order: 9999;
  }

  .col-span-1 {
    grid-column: span 1 / span 1;
  }

  .col-span-2 {
    grid-column: span 2 / span 2;
  }

  .col-span-6 {
    grid-column: span 6 / span 6;
  }

  .col-span-12 {
    grid-column: span 12 / span 12;
  }

  .container {
    width: 100%;
  }

  @media (min-width: 40rem) {
    .container {
      max-width: 40rem;
    }
  }

  @media (min-width: 48rem) {
    .container {
      max-width: 48rem;
    }
  }

  @media (min-width: 64rem) {
    .container {
      max-width: 64rem;
    }
  }

  @media (min-width: 80rem) {
    .container {
      max-width: 80rem;
    }
  }

  @media (min-width: 96rem) {
    .container {
      max-width: 96rem;
    }
  }

  .container\! {
    width: 100% !important;
  }

  @media (min-width: 40rem) {
    .container\! {
      max-width: 40rem !important;
    }
  }

  @media (min-width: 48rem) {
    .container\! {
      max-width: 48rem !important;
    }
  }

  @media (min-width: 64rem) {
    .container\! {
      max-width: 64rem !important;
    }
  }

  @media (min-width: 80rem) {
    .container\! {
      max-width: 80rem !important;
    }
  }

  @media (min-width: 96rem) {
    .container\! {
      max-width: 96rem !important;
    }
  }

  .-m-2 {
    margin: calc(var(--spacing) * -2);
  }

  .-m-\[48px\] {
    margin: -48px;
  }

  .m-0 {
    margin: calc(var(--spacing) * 0);
  }

  .m-1 {
    margin: calc(var(--spacing) * 1);
  }

  .m-128 {
    margin: calc(var(--spacing) * 128);
  }

  .m-auto {
    margin: auto;
  }

  .-mx-1 {
    margin-inline: calc(var(--spacing) * -1);
  }

  .-mx-4 {
    margin-inline: calc(var(--spacing) * -4);
  }

  .mx-0 {
    margin-inline: calc(var(--spacing) * 0);
  }

  .mx-0\.5 {
    margin-inline: calc(var(--spacing) * .5);
  }

  .mx-2 {
    margin-inline: calc(var(--spacing) * 2);
  }

  .mx-3 {
    margin-inline: calc(var(--spacing) * 3);
  }

  .mx-4 {
    margin-inline: calc(var(--spacing) * 4);
  }

  .mx-5 {
    margin-inline: calc(var(--spacing) * 5);
  }

  .mx-8 {
    margin-inline: calc(var(--spacing) * 8);
  }

  .mx-\[8px\] {
    margin-left: 8px;
    margin-right: 8px;
  }

  .mx-\[max\(calc\(\(100vw-1560px\)\/2\)\,0px\)\] {
    margin-left: max(50vw - 780px, 0px);
    margin-right: max(50vw - 780px, 0px);
  }

  .mx-\[max\(calc\(\(100vw-1800px\)\/2\)\,0px\)\] {
    margin-left: max(50vw - 900px, 0px);
    margin-right: max(50vw - 900px, 0px);
  }

  .mx-auto {
    margin-left: auto;
    margin-right: auto;
  }

  .-my-0\.5 {
    margin-block: calc(var(--spacing) * -.5);
  }

  .my-0 {
    margin-block: calc(var(--spacing) * 0);
  }

  .my-1 {
    margin-block: calc(var(--spacing) * 1);
  }

  .my-2 {
    margin-block: calc(var(--spacing) * 2);
  }

  .my-2\.5 {
    margin-block: calc(var(--spacing) * 2.5);
  }

  .my-3 {
    margin-block: calc(var(--spacing) * 3);
  }

  .my-4 {
    margin-block: calc(var(--spacing) * 4);
  }

  .my-5 {
    margin-block: calc(var(--spacing) * 5);
  }

  .my-6 {
    margin-block: calc(var(--spacing) * 6);
  }

  .my-\[1px\] {
    margin-top: 1px;
    margin-bottom: 1px;
  }

  .my-\[10px\] {
    margin-top: 10px;
    margin-bottom: 10px;
  }

  .-mt-4 {
    margin-top: calc(var(--spacing) * -4);
  }

  .mt-0 {
    margin-top: calc(var(--spacing) * 0);
  }

  .mt-0\.5 {
    margin-top: calc(var(--spacing) * .5);
  }

  .mt-1 {
    margin-top: calc(var(--spacing) * 1);
  }

  .mt-2 {
    margin-top: calc(var(--spacing) * 2);
  }

  .mt-2\.5 {
    margin-top: calc(var(--spacing) * 2.5);
  }

  .mt-3 {
    margin-top: calc(var(--spacing) * 3);
  }

  .mt-4 {
    margin-top: calc(var(--spacing) * 4);
  }

  .mt-5 {
    margin-top: calc(var(--spacing) * 5);
  }

  .mt-6 {
    margin-top: calc(var(--spacing) * 6);
  }

  .mt-8 {
    margin-top: calc(var(--spacing) * 8);
  }

  .mt-10 {
    margin-top: calc(var(--spacing) * 10);
  }

  .mt-12 {
    margin-top: calc(var(--spacing) * 12);
  }

  .mt-14 {
    margin-top: calc(var(--spacing) * 14);
  }

  .mt-16 {
    margin-top: calc(var(--spacing) * 16);
  }

  .mt-20 {
    margin-top: calc(var(--spacing) * 20);
  }

  .mt-28 {
    margin-top: calc(var(--spacing) * 28);
  }

  .mt-32 {
    margin-top: calc(var(--spacing) * 32);
  }

  .mt-\[-0\.1rem\] {
    margin-top: -.1rem;
  }

  .mt-\[0\.8rem\] {
    margin-top: .8rem;
  }

  .mt-\[20px\] {
    margin-top: 20px;
  }

  .mt-\[30px\] {
    margin-top: 30px;
  }

  .mt-\[40px\] {
    margin-top: 40px;
  }

  .-mr-0\.5 {
    margin-right: calc(var(--spacing) * -.5);
  }

  .-mr-2 {
    margin-right: calc(var(--spacing) * -2);
  }

  .mr-0 {
    margin-right: calc(var(--spacing) * 0);
  }

  .mr-1 {
    margin-right: calc(var(--spacing) * 1);
  }

  .mr-1\.5 {
    margin-right: calc(var(--spacing) * 1.5);
  }

  .mr-2 {
    margin-right: calc(var(--spacing) * 2);
  }

  .mr-3 {
    margin-right: calc(var(--spacing) * 3);
  }

  .mr-4 {
    margin-right: calc(var(--spacing) * 4);
  }

  .mr-6 {
    margin-right: calc(var(--spacing) * 6);
  }

  .mr-8 {
    margin-right: calc(var(--spacing) * 8);
  }

  .mr-16 {
    margin-right: calc(var(--spacing) * 16);
  }

  .mr-\[-4px\] {
    margin-right: -4px;
  }

  .mr-\[0\.8rem\] {
    margin-right: .8rem;
  }

  .mb-0 {
    margin-bottom: calc(var(--spacing) * 0);
  }

  .mb-1 {
    margin-bottom: calc(var(--spacing) * 1);
  }

  .mb-2 {
    margin-bottom: calc(var(--spacing) * 2);
  }

  .mb-2\.5 {
    margin-bottom: calc(var(--spacing) * 2.5);
  }

  .mb-3 {
    margin-bottom: calc(var(--spacing) * 3);
  }

  .mb-3\.5 {
    margin-bottom: calc(var(--spacing) * 3.5);
  }

  .mb-4 {
    margin-bottom: calc(var(--spacing) * 4);
  }

  .mb-5 {
    margin-bottom: calc(var(--spacing) * 5);
  }

  .mb-6 {
    margin-bottom: calc(var(--spacing) * 6);
  }

  .mb-8 {
    margin-bottom: calc(var(--spacing) * 8);
  }

  .mb-10 {
    margin-bottom: calc(var(--spacing) * 10);
  }

  .mb-12 {
    margin-bottom: calc(var(--spacing) * 12);
  }

  .mb-14 {
    margin-bottom: calc(var(--spacing) * 14);
  }

  .mb-16 {
    margin-bottom: calc(var(--spacing) * 16);
  }

  .mb-20 {
    margin-bottom: calc(var(--spacing) * 20);
  }

  .mb-\[1px\] {
    margin-bottom: 1px;
  }

  .mb-\[10px\] {
    margin-bottom: 10px;
  }

  .-ml-1 {
    margin-left: calc(var(--spacing) * -1);
  }

  .ml-1 {
    margin-left: calc(var(--spacing) * 1);
  }

  .ml-2 {
    margin-left: calc(var(--spacing) * 2);
  }

  .ml-3 {
    margin-left: calc(var(--spacing) * 3);
  }

  .ml-4 {
    margin-left: calc(var(--spacing) * 4);
  }

  .ml-6 {
    margin-left: calc(var(--spacing) * 6);
  }

  .ml-8 {
    margin-left: calc(var(--spacing) * 8);
  }

  .ml-\[0\.5rem\] {
    margin-left: .5rem;
  }

  .ml-\[1\.375rem\] {
    margin-left: 1.375rem;
  }

  .ml-\[10px\] {
    margin-left: 10px;
  }

  .ml-\[50\%\] {
    margin-left: 50%;
  }

  .ml-auto {
    margin-left: auto;
  }

  .box-border {
    box-sizing: border-box;
  }

  .line-clamp-2 {
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    display: -webkit-box;
    overflow: hidden;
  }

  .\!block {
    display: block !important;
  }

  .\!flex {
    display: flex !important;
  }

  .block {
    display: block;
  }

  .contents {
    display: contents;
  }

  .contents\! {
    display: contents !important;
  }

  .flex {
    display: flex;
  }

  .grid {
    display: grid;
  }

  .hidden {
    display: none;
  }

  .inline {
    display: inline;
  }

  .inline\! {
    display: inline !important;
  }

  .inline-block {
    display: inline-block;
  }

  .inline-flex {
    display: inline-flex;
  }

  .list-item {
    display: list-item;
  }

  .table {
    display: table;
  }

  .aspect-\[4\/3\] {
    aspect-ratio: 4 / 3;
  }

  .aspect-\[16\/9\] {
    aspect-ratio: 16 / 9;
  }

  .aspect-square {
    aspect-ratio: 1;
  }

  .size-3\.5 {
    width: calc(var(--spacing) * 3.5);
    height: calc(var(--spacing) * 3.5);
  }

  .size-4 {
    width: calc(var(--spacing) * 4);
    height: calc(var(--spacing) * 4);
  }

  .size-5 {
    width: calc(var(--spacing) * 5);
    height: calc(var(--spacing) * 5);
  }

  .size-7 {
    width: calc(var(--spacing) * 7);
    height: calc(var(--spacing) * 7);
  }

  .size-\[13px\] {
    width: 13px;
    height: 13px;
  }

  .\!h-auto {
    height: auto !important;
  }

  .h-0 {
    height: calc(var(--spacing) * 0);
  }

  .h-1 {
    height: calc(var(--spacing) * 1);
  }

  .h-2 {
    height: calc(var(--spacing) * 2);
  }

  .h-2\.5 {
    height: calc(var(--spacing) * 2.5);
  }

  .h-3 {
    height: calc(var(--spacing) * 3);
  }

  .h-3\.5 {
    height: calc(var(--spacing) * 3.5);
  }

  .h-4 {
    height: calc(var(--spacing) * 4);
  }

  .h-5 {
    height: calc(var(--spacing) * 5);
  }

  .h-6 {
    height: calc(var(--spacing) * 6);
  }

  .h-7 {
    height: calc(var(--spacing) * 7);
  }

  .h-8 {
    height: calc(var(--spacing) * 8);
  }

  .h-9 {
    height: calc(var(--spacing) * 9);
  }

  .h-10 {
    height: calc(var(--spacing) * 10);
  }

  .h-11 {
    height: calc(var(--spacing) * 11);
  }

  .h-12 {
    height: calc(var(--spacing) * 12);
  }

  .h-14 {
    height: calc(var(--spacing) * 14);
  }

  .h-16 {
    height: calc(var(--spacing) * 16);
  }

  .h-20 {
    height: calc(var(--spacing) * 20);
  }

  .h-32 {
    height: calc(var(--spacing) * 32);
  }

  .h-46 {
    height: calc(var(--spacing) * 46);
  }

  .h-100 {
    height: calc(var(--spacing) * 100);
  }

  .h-\[0\.2rem\] {
    height: .2rem;
  }

  .h-\[1px\] {
    height: 1px;
  }

  .h-\[2\.4rem\] {
    height: 2.4rem;
  }

  .h-\[2px\] {
    height: 2px;
  }

  .h-\[3rem\] {
    height: 3rem;
  }

  .h-\[14px\] {
    height: 14px;
  }

  .h-\[18px\] {
    height: 18px;
  }

  .h-\[22px\] {
    height: 22px;
  }

  .h-\[24px\] {
    height: 24px;
  }

  .h-\[36px\] {
    height: 36px;
  }

  .h-\[48px\] {
    height: 48px;
  }

  .h-\[49px\] {
    height: 49px;
  }

  .h-\[54px\] {
    height: 54px;
  }

  .h-\[57px\] {
    height: 57px;
  }

  .h-\[100px\] {
    height: 100px;
  }

  .h-\[140px\] {
    height: 140px;
  }

  .h-\[160px\] {
    height: 160px;
  }

  .h-\[460px\] {
    height: 460px;
  }

  .h-\[540px\] {
    height: 540px;
  }

  .h-\[600px\] {
    height: 600px;
  }

  .h-\[1600px\] {
    height: 1600px;
  }

  .h-\[calc\(100\%-100px\)\] {
    height: calc(100% - 100px);
  }

  .h-\[calc\(100vh-4rem\)\] {
    height: calc(100vh - 4rem);
  }

  .h-\[calc\(100vh-8rem\)\] {
    height: calc(100vh - 8rem);
  }

  .h-\[calc\(100vh-54px\)\] {
    height: calc(100vh - 54px);
  }

  .h-auto {
    height: auto;
  }

  .h-fit {
    height: -moz-fit-content;
    height: fit-content;
  }

  .h-full {
    height: 100%;
  }

  .h-screen {
    height: 100vh;
  }

  .\!max-h-none {
    max-height: none !important;
  }

  .max-h-0 {
    max-height: calc(var(--spacing) * 0);
  }

  .max-h-14 {
    max-height: calc(var(--spacing) * 14);
  }

  .max-h-48 {
    max-height: calc(var(--spacing) * 48);
  }

  .max-h-96 {
    max-height: calc(var(--spacing) * 96);
  }

  .max-h-\[50px\] {
    max-height: 50px;
  }

  .max-h-\[60vh\] {
    max-height: 60vh;
  }

  .max-h-\[90px\] {
    max-height: 90px;
  }

  .max-h-\[180px\] {
    max-height: 180px;
  }

  .max-h-\[200px\] {
    max-height: 200px;
  }

  .max-h-\[500px\] {
    max-height: 500px;
  }

  .max-h-full {
    max-height: 100%;
  }

  .min-h-0 {
    min-height: calc(var(--spacing) * 0);
  }

  .min-h-\[32px\] {
    min-height: 32px;
  }

  .min-h-\[520px\] {
    min-height: 520px;
  }

  .min-h-full {
    min-height: 100%;
  }

  .min-h-screen {
    min-height: 100vh;
  }

  .w-0 {
    width: calc(var(--spacing) * 0);
  }

  .w-0\.5 {
    width: calc(var(--spacing) * .5);
  }

  .w-1 {
    width: calc(var(--spacing) * 1);
  }

  .w-1\/2 {
    width: 50%;
  }

  .w-1\/4 {
    width: 25%;
  }

  .w-2 {
    width: calc(var(--spacing) * 2);
  }

  .w-2\.5 {
    width: calc(var(--spacing) * 2.5);
  }

  .w-2\/3 {
    width: 66.6667%;
  }

  .w-3 {
    width: calc(var(--spacing) * 3);
  }

  .w-3\.5 {
    width: calc(var(--spacing) * 3.5);
  }

  .w-4 {
    width: calc(var(--spacing) * 4);
  }

  .w-5 {
    width: calc(var(--spacing) * 5);
  }

  .w-6 {
    width: calc(var(--spacing) * 6);
  }

  .w-8 {
    width: calc(var(--spacing) * 8);
  }

  .w-10 {
    width: calc(var(--spacing) * 10);
  }

  .w-11 {
    width: calc(var(--spacing) * 11);
  }

  .w-12 {
    width: calc(var(--spacing) * 12);
  }

  .w-14 {
    width: calc(var(--spacing) * 14);
  }

  .w-16 {
    width: calc(var(--spacing) * 16);
  }

  .w-20 {
    width: calc(var(--spacing) * 20);
  }

  .w-24 {
    width: calc(var(--spacing) * 24);
  }

  .w-28 {
    width: calc(var(--spacing) * 28);
  }

  .w-32 {
    width: calc(var(--spacing) * 32);
  }

  .w-46 {
    width: calc(var(--spacing) * 46);
  }

  .w-48 {
    width: calc(var(--spacing) * 48);
  }

  .w-64 {
    width: calc(var(--spacing) * 64);
  }

  .w-72 {
    width: calc(var(--spacing) * 72);
  }

  .w-96 {
    width: calc(var(--spacing) * 96);
  }

  .w-100 {
    width: calc(var(--spacing) * 100);
  }

  .w-\[1\.8rem\] {
    width: 1.8rem;
  }

  .w-\[1px\] {
    width: 1px;
  }

  .w-\[2\.4rem\] {
    width: 2.4rem;
  }

  .w-\[2\.05rem\] {
    width: 2.05rem;
  }

  .w-\[2px\] {
    width: 2px;
  }

  .w-\[3rem\] {
    width: 3rem;
  }

  .w-\[14px\] {
    width: 14px;
  }

  .w-\[18rem\] {
    width: 18rem;
  }

  .w-\[19rem\] {
    width: 19rem;
  }

  .w-\[22px\] {
    width: 22px;
  }

  .w-\[23rem\] {
    width: 23rem;
  }

  .w-\[80px\] {
    width: 80px;
  }

  .w-\[96px\] {
    width: 96px;
  }

  .w-\[100vw\] {
    width: 100vw;
  }

  .w-\[113px\] {
    width: 113px;
  }

  .w-\[192px\] {
    width: 192px;
  }

  .w-\[200px\] {
    width: 200px;
  }

  .w-\[276px\] {
    width: 276px;
  }

  .w-\[285px\] {
    width: 285px;
  }

  .w-\[300px\] {
    width: 300px;
  }

  .w-\[360px\] {
    width: 360px;
  }

  .w-\[560px\] {
    width: 560px;
  }

  .w-\[1920px\] {
    width: 1920px;
  }

  .w-\[calc\(100\%-24px\)\] {
    width: calc(100% - 24px);
  }

  .w-auto {
    width: auto;
  }

  .w-fit {
    width: -moz-fit-content;
    width: fit-content;
  }

  .w-full {
    width: 100%;
  }

  .w-screen {
    width: 100vw;
  }

  .max-w-1\/3 {
    max-width: 33.3333%;
  }

  .max-w-2xl {
    max-width: var(--container-2xl);
  }

  .max-w-3xl {
    max-width: var(--container-3xl);
  }

  .max-w-4xl {
    max-width: var(--container-4xl);
  }

  .max-w-7xl {
    max-width: var(--container-7xl);
  }

  .max-w-\[4rem\] {
    max-width: 4rem;
  }

  .max-w-\[16rem\] {
    max-width: 16rem;
  }

  .max-w-\[28rem\] {
    max-width: 28rem;
  }

  .max-w-\[70px\] {
    max-width: 70px;
  }

  .max-w-\[80\%\] {
    max-width: 80%;
  }

  .max-w-\[200px\] {
    max-width: 200px;
  }

  .max-w-\[560px\] {
    max-width: 560px;
  }

  .max-w-\[1224px\] {
    max-width: 1224px;
  }

  .max-w-\[1560px\] {
    max-width: 1560px;
  }

  .max-w-\[1800px\] {
    max-width: 1800px;
  }

  .max-w-full {
    max-width: 100%;
  }

  .max-w-md {
    max-width: var(--container-md);
  }

  .max-w-screen-xl {
    max-width: var(--breakpoint-xl);
  }

  .max-w-sm {
    max-width: var(--container-sm);
  }

  .max-w-xl {
    max-width: var(--container-xl);
  }

  .max-w-xs {
    max-width: var(--container-xs);
  }

  .min-w-0 {
    min-width: calc(var(--spacing) * 0);
  }

  .min-w-4 {
    min-width: calc(var(--spacing) * 4);
  }

  .min-w-7 {
    min-width: calc(var(--spacing) * 7);
  }

  .min-w-20 {
    min-width: calc(var(--spacing) * 20);
  }

  .min-w-64 {
    min-width: calc(var(--spacing) * 64);
  }

  .min-w-\[6rem\] {
    min-width: 6rem;
  }

  .min-w-\[12rem\] {
    min-width: 12rem;
  }

  .min-w-\[42px\] {
    min-width: 42px;
  }

  .min-w-\[43px\] {
    min-width: 43px;
  }

  .min-w-\[120px\] {
    min-width: 120px;
  }

  .min-w-\[180px\] {
    min-width: 180px;
  }

  .min-w-\[250px\] {
    min-width: 250px;
  }

  .min-w-fit {
    min-width: -moz-fit-content;
    min-width: fit-content;
  }

  .min-w-full {
    min-width: 100%;
  }

  .\!flex-1 {
    flex: 1 !important;
  }

  .\!flex-initial {
    flex: 0 auto !important;
  }

  .flex-0 {
    flex: 0;
  }

  .flex-1 {
    flex: 1;
  }

  .flex-none {
    flex: none;
  }

  .flex-shrink {
    flex-shrink: 1;
  }

  .flex-shrink-0 {
    flex-shrink: 0;
  }

  .shrink {
    flex-shrink: 1;
  }

  .shrink-0 {
    flex-shrink: 0;
  }

  .flex-grow, .grow {
    flex-grow: 1;
  }

  .table-fixed {
    table-layout: fixed;
  }

  .-translate-x-1\/2 {
    --tw-translate-x: calc(calc(1 / 2 * 100%) * -1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .translate-y-\[100px\] {
    --tw-translate-y: 100px;
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .scale-105 {
    --tw-scale-x: 105%;
    --tw-scale-y: 105%;
    --tw-scale-z: 105%;
    scale: var(--tw-scale-x) var(--tw-scale-y);
  }

  .-rotate-180 {
    rotate: -180deg;
  }

  .rotate-90 {
    rotate: 90deg;
  }

  .rotate-180 {
    rotate: 180deg;
  }

  .transform {
    transform: var(--tw-rotate-x, ) var(--tw-rotate-y, ) var(--tw-rotate-z, ) var(--tw-skew-x, ) var(--tw-skew-y, );
  }

  .transform\! {
    transform: var(--tw-rotate-x, ) var(--tw-rotate-y, ) var(--tw-rotate-z, ) var(--tw-skew-x, ) var(--tw-skew-y, ) !important;
  }

  .animate-none {
    animation: none;
  }

  .animate-pulse {
    animation: var(--animate-pulse);
  }

  .animate-spin {
    animation: var(--animate-spin);
  }

  .\!cursor-default {
    cursor: default !important;
  }

  .\!cursor-not-allowed {
    cursor: not-allowed !important;
  }

  .cursor-auto {
    cursor: auto;
  }

  .cursor-col-resize {
    cursor: col-resize;
  }

  .cursor-default {
    cursor: default;
  }

  .cursor-grab {
    cursor: grab;
  }

  .cursor-grabbing {
    cursor: grabbing;
  }

  .cursor-help {
    cursor: help;
  }

  .cursor-move {
    cursor: move;
  }

  .cursor-not-allowed {
    cursor: not-allowed;
  }

  .cursor-pointer {
    cursor: pointer;
  }

  .cursor-text {
    cursor: text;
  }

  .resize {
    resize: both;
  }

  .resize\! {
    resize: both !important;
  }

  .scroll-m-4 {
    scroll-margin: calc(var(--spacing) * 4);
  }

  .list-disc {
    list-style-type: disc;
  }

  .list-none {
    list-style-type: none;
  }

  .appearance-none {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
  }

  .grid-cols-1 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }

  .grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .grid-cols-5 {
    grid-template-columns: repeat(5, minmax(0, 1fr));
  }

  .grid-cols-12 {
    grid-template-columns: repeat(12, minmax(0, 1fr));
  }

  .grid-cols-\[repeat\(auto-fit\,minmax\(240px\,1fr\)\)\] {
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  }

  .grid-rows-1 {
    grid-template-rows: repeat(1, minmax(0, 1fr));
  }

  .grid-rows-4 {
    grid-template-rows: repeat(4, minmax(0, 1fr));
  }

  .\!flex-col {
    flex-direction: column !important;
  }

  .\!flex-row-reverse {
    flex-direction: row-reverse !important;
  }

  .flex-col {
    flex-direction: column;
  }

  .flex-col-reverse {
    flex-direction: column-reverse;
  }

  .flex-row {
    flex-direction: row;
  }

  .flex-row-reverse {
    flex-direction: row-reverse;
  }

  .flex-nowrap {
    flex-wrap: nowrap;
  }

  .flex-wrap {
    flex-wrap: wrap;
  }

  .content-center {
    align-content: center;
  }

  .items-baseline {
    align-items: baseline;
  }

  .items-center {
    align-items: center;
  }

  .items-end {
    align-items: flex-end;
  }

  .items-start {
    align-items: flex-start;
  }

  .items-stretch {
    align-items: stretch;
  }

  .justify-between {
    justify-content: space-between;
  }

  .justify-center {
    justify-content: center;
  }

  .justify-end {
    justify-content: flex-end;
  }

  .justify-start {
    justify-content: flex-start;
  }

  .gap-1 {
    gap: calc(var(--spacing) * 1);
  }

  .gap-1\.5 {
    gap: calc(var(--spacing) * 1.5);
  }

  .gap-2 {
    gap: calc(var(--spacing) * 2);
  }

  .gap-2\.5 {
    gap: calc(var(--spacing) * 2.5);
  }

  .gap-3 {
    gap: calc(var(--spacing) * 3);
  }

  .gap-4 {
    gap: calc(var(--spacing) * 4);
  }

  .gap-5 {
    gap: calc(var(--spacing) * 5);
  }

  .gap-6 {
    gap: calc(var(--spacing) * 6);
  }

  .gap-8 {
    gap: calc(var(--spacing) * 8);
  }

  .gap-9 {
    gap: calc(var(--spacing) * 9);
  }

  .gap-10 {
    gap: calc(var(--spacing) * 10);
  }

  .gap-12 {
    gap: calc(var(--spacing) * 12);
  }

  .gap-16 {
    gap: calc(var(--spacing) * 16);
  }

  .gap-20 {
    gap: calc(var(--spacing) * 20);
  }

  .gap-\[2px\] {
    gap: 2px;
  }

  .gap-\[6px\] {
    gap: 6px;
  }

  :where(.space-y-1 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-top: calc(calc(var(--spacing) * 1) * var(--tw-space-y-reverse));
    margin-bottom: calc(calc(var(--spacing) * 1) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-2 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-top: calc(calc(var(--spacing) * 2) * var(--tw-space-y-reverse));
    margin-bottom: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-2\.5 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-top: calc(calc(var(--spacing) * 2.5) * var(--tw-space-y-reverse));
    margin-bottom: calc(calc(var(--spacing) * 2.5) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-3 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-top: calc(calc(var(--spacing) * 3) * var(--tw-space-y-reverse));
    margin-bottom: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-4 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-top: calc(calc(var(--spacing) * 4) * var(--tw-space-y-reverse));
    margin-bottom: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-6 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-top: calc(calc(var(--spacing) * 6) * var(--tw-space-y-reverse));
    margin-bottom: calc(calc(var(--spacing) * 6) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-8 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-top: calc(calc(var(--spacing) * 8) * var(--tw-space-y-reverse));
    margin-bottom: calc(calc(var(--spacing) * 8) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-12 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-top: calc(calc(var(--spacing) * 12) * var(--tw-space-y-reverse));
    margin-bottom: calc(calc(var(--spacing) * 12) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-20 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-top: calc(calc(var(--spacing) * 20) * var(--tw-space-y-reverse));
    margin-bottom: calc(calc(var(--spacing) * 20) * calc(1 - var(--tw-space-y-reverse)));
  }

  .gap-x-2 {
    column-gap: calc(var(--spacing) * 2);
  }

  .gap-x-3 {
    column-gap: calc(var(--spacing) * 3);
  }

  .gap-x-4 {
    column-gap: calc(var(--spacing) * 4);
  }

  .gap-x-5 {
    column-gap: calc(var(--spacing) * 5);
  }

  .gap-x-8 {
    column-gap: calc(var(--spacing) * 8);
  }

  :where(.-space-x-1 > :not(:last-child)) {
    --tw-space-x-reverse: 0;
  }

  :where(.-space-x-1 > :not(:last-child)):not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    margin-left: calc(calc(var(--spacing) * -1) * var(--tw-space-x-reverse));
    margin-right: calc(calc(var(--spacing) * -1) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.-space-x-1 > :not(:last-child)):not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    margin-left: calc(calc(var(--spacing) * -1) * var(--tw-space-x-reverse));
    margin-right: calc(calc(var(--spacing) * -1) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.-space-x-1 > :not(:last-child)):not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    margin-left: calc(calc(var(--spacing) * -1) * var(--tw-space-x-reverse));
    margin-right: calc(calc(var(--spacing) * -1) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.-space-x-1 > :not(:last-child)):-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    margin-right: calc(calc(var(--spacing) * -1) * var(--tw-space-x-reverse));
    margin-left: calc(calc(var(--spacing) * -1) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.-space-x-1 > :not(:last-child)):-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    margin-right: calc(calc(var(--spacing) * -1) * var(--tw-space-x-reverse));
    margin-left: calc(calc(var(--spacing) * -1) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.-space-x-1 > :not(:last-child)):is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    margin-right: calc(calc(var(--spacing) * -1) * var(--tw-space-x-reverse));
    margin-left: calc(calc(var(--spacing) * -1) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-1 > :not(:last-child)) {
    --tw-space-x-reverse: 0;
  }

  :where(.space-x-1 > :not(:last-child)):not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    margin-left: calc(calc(var(--spacing) * 1) * var(--tw-space-x-reverse));
    margin-right: calc(calc(var(--spacing) * 1) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-1 > :not(:last-child)):not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    margin-left: calc(calc(var(--spacing) * 1) * var(--tw-space-x-reverse));
    margin-right: calc(calc(var(--spacing) * 1) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-1 > :not(:last-child)):not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    margin-left: calc(calc(var(--spacing) * 1) * var(--tw-space-x-reverse));
    margin-right: calc(calc(var(--spacing) * 1) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-1 > :not(:last-child)):-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    margin-right: calc(calc(var(--spacing) * 1) * var(--tw-space-x-reverse));
    margin-left: calc(calc(var(--spacing) * 1) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-1 > :not(:last-child)):-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    margin-right: calc(calc(var(--spacing) * 1) * var(--tw-space-x-reverse));
    margin-left: calc(calc(var(--spacing) * 1) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-1 > :not(:last-child)):is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    margin-right: calc(calc(var(--spacing) * 1) * var(--tw-space-x-reverse));
    margin-left: calc(calc(var(--spacing) * 1) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-2 > :not(:last-child)) {
    --tw-space-x-reverse: 0;
  }

  :where(.space-x-2 > :not(:last-child)):not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    margin-left: calc(calc(var(--spacing) * 2) * var(--tw-space-x-reverse));
    margin-right: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-2 > :not(:last-child)):not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    margin-left: calc(calc(var(--spacing) * 2) * var(--tw-space-x-reverse));
    margin-right: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-2 > :not(:last-child)):not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    margin-left: calc(calc(var(--spacing) * 2) * var(--tw-space-x-reverse));
    margin-right: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-2 > :not(:last-child)):-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    margin-right: calc(calc(var(--spacing) * 2) * var(--tw-space-x-reverse));
    margin-left: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-2 > :not(:last-child)):-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    margin-right: calc(calc(var(--spacing) * 2) * var(--tw-space-x-reverse));
    margin-left: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-2 > :not(:last-child)):is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    margin-right: calc(calc(var(--spacing) * 2) * var(--tw-space-x-reverse));
    margin-left: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-2\.5 > :not(:last-child)) {
    --tw-space-x-reverse: 0;
  }

  :where(.space-x-2\.5 > :not(:last-child)):not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    margin-left: calc(calc(var(--spacing) * 2.5) * var(--tw-space-x-reverse));
    margin-right: calc(calc(var(--spacing) * 2.5) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-2\.5 > :not(:last-child)):not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    margin-left: calc(calc(var(--spacing) * 2.5) * var(--tw-space-x-reverse));
    margin-right: calc(calc(var(--spacing) * 2.5) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-2\.5 > :not(:last-child)):not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    margin-left: calc(calc(var(--spacing) * 2.5) * var(--tw-space-x-reverse));
    margin-right: calc(calc(var(--spacing) * 2.5) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-2\.5 > :not(:last-child)):-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    margin-right: calc(calc(var(--spacing) * 2.5) * var(--tw-space-x-reverse));
    margin-left: calc(calc(var(--spacing) * 2.5) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-2\.5 > :not(:last-child)):-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    margin-right: calc(calc(var(--spacing) * 2.5) * var(--tw-space-x-reverse));
    margin-left: calc(calc(var(--spacing) * 2.5) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-2\.5 > :not(:last-child)):is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    margin-right: calc(calc(var(--spacing) * 2.5) * var(--tw-space-x-reverse));
    margin-left: calc(calc(var(--spacing) * 2.5) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-3 > :not(:last-child)) {
    --tw-space-x-reverse: 0;
  }

  :where(.space-x-3 > :not(:last-child)):not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    margin-left: calc(calc(var(--spacing) * 3) * var(--tw-space-x-reverse));
    margin-right: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-3 > :not(:last-child)):not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    margin-left: calc(calc(var(--spacing) * 3) * var(--tw-space-x-reverse));
    margin-right: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-3 > :not(:last-child)):not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    margin-left: calc(calc(var(--spacing) * 3) * var(--tw-space-x-reverse));
    margin-right: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-3 > :not(:last-child)):-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    margin-right: calc(calc(var(--spacing) * 3) * var(--tw-space-x-reverse));
    margin-left: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-3 > :not(:last-child)):-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    margin-right: calc(calc(var(--spacing) * 3) * var(--tw-space-x-reverse));
    margin-left: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-3 > :not(:last-child)):is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    margin-right: calc(calc(var(--spacing) * 3) * var(--tw-space-x-reverse));
    margin-left: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-4 > :not(:last-child)) {
    --tw-space-x-reverse: 0;
  }

  :where(.space-x-4 > :not(:last-child)):not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    margin-left: calc(calc(var(--spacing) * 4) * var(--tw-space-x-reverse));
    margin-right: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-4 > :not(:last-child)):not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    margin-left: calc(calc(var(--spacing) * 4) * var(--tw-space-x-reverse));
    margin-right: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-4 > :not(:last-child)):not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    margin-left: calc(calc(var(--spacing) * 4) * var(--tw-space-x-reverse));
    margin-right: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-4 > :not(:last-child)):-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    margin-right: calc(calc(var(--spacing) * 4) * var(--tw-space-x-reverse));
    margin-left: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-4 > :not(:last-child)):-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    margin-right: calc(calc(var(--spacing) * 4) * var(--tw-space-x-reverse));
    margin-left: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-4 > :not(:last-child)):is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    margin-right: calc(calc(var(--spacing) * 4) * var(--tw-space-x-reverse));
    margin-left: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-6 > :not(:last-child)) {
    --tw-space-x-reverse: 0;
  }

  :where(.space-x-6 > :not(:last-child)):not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    margin-left: calc(calc(var(--spacing) * 6) * var(--tw-space-x-reverse));
    margin-right: calc(calc(var(--spacing) * 6) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-6 > :not(:last-child)):not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    margin-left: calc(calc(var(--spacing) * 6) * var(--tw-space-x-reverse));
    margin-right: calc(calc(var(--spacing) * 6) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-6 > :not(:last-child)):not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    margin-left: calc(calc(var(--spacing) * 6) * var(--tw-space-x-reverse));
    margin-right: calc(calc(var(--spacing) * 6) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-6 > :not(:last-child)):-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    margin-right: calc(calc(var(--spacing) * 6) * var(--tw-space-x-reverse));
    margin-left: calc(calc(var(--spacing) * 6) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-6 > :not(:last-child)):-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    margin-right: calc(calc(var(--spacing) * 6) * var(--tw-space-x-reverse));
    margin-left: calc(calc(var(--spacing) * 6) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-6 > :not(:last-child)):is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    margin-right: calc(calc(var(--spacing) * 6) * var(--tw-space-x-reverse));
    margin-left: calc(calc(var(--spacing) * 6) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-8 > :not(:last-child)) {
    --tw-space-x-reverse: 0;
  }

  :where(.space-x-8 > :not(:last-child)):not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    margin-left: calc(calc(var(--spacing) * 8) * var(--tw-space-x-reverse));
    margin-right: calc(calc(var(--spacing) * 8) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-8 > :not(:last-child)):not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    margin-left: calc(calc(var(--spacing) * 8) * var(--tw-space-x-reverse));
    margin-right: calc(calc(var(--spacing) * 8) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-8 > :not(:last-child)):not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    margin-left: calc(calc(var(--spacing) * 8) * var(--tw-space-x-reverse));
    margin-right: calc(calc(var(--spacing) * 8) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-8 > :not(:last-child)):-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    margin-right: calc(calc(var(--spacing) * 8) * var(--tw-space-x-reverse));
    margin-left: calc(calc(var(--spacing) * 8) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-8 > :not(:last-child)):-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    margin-right: calc(calc(var(--spacing) * 8) * var(--tw-space-x-reverse));
    margin-left: calc(calc(var(--spacing) * 8) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-8 > :not(:last-child)):is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    margin-right: calc(calc(var(--spacing) * 8) * var(--tw-space-x-reverse));
    margin-left: calc(calc(var(--spacing) * 8) * calc(1 - var(--tw-space-x-reverse)));
  }

  .gap-y-2 {
    row-gap: calc(var(--spacing) * 2);
  }

  .gap-y-4 {
    row-gap: calc(var(--spacing) * 4);
  }

  .gap-y-6 {
    row-gap: calc(var(--spacing) * 6);
  }

  .gap-y-8 {
    row-gap: calc(var(--spacing) * 8);
  }

  :where(.divide-x > :not(:last-child)) {
    --tw-divide-x-reverse: 0;
    border-inline-style: var(--tw-border-style);
  }

  :where(.divide-x > :not(:last-child)):not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    border-left-width: calc(1px * var(--tw-divide-x-reverse));
    border-right-width: calc(1px * calc(1 - var(--tw-divide-x-reverse)));
  }

  :where(.divide-x > :not(:last-child)):not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    border-left-width: calc(1px * var(--tw-divide-x-reverse));
    border-right-width: calc(1px * calc(1 - var(--tw-divide-x-reverse)));
  }

  :where(.divide-x > :not(:last-child)):not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
    border-left-width: calc(1px * var(--tw-divide-x-reverse));
    border-right-width: calc(1px * calc(1 - var(--tw-divide-x-reverse)));
  }

  :where(.divide-x > :not(:last-child)):-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    border-right-width: calc(1px * var(--tw-divide-x-reverse));
    border-left-width: calc(1px * calc(1 - var(--tw-divide-x-reverse)));
  }

  :where(.divide-x > :not(:last-child)):-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    border-right-width: calc(1px * var(--tw-divide-x-reverse));
    border-left-width: calc(1px * calc(1 - var(--tw-divide-x-reverse)));
  }

  :where(.divide-x > :not(:last-child)):is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
    border-right-width: calc(1px * var(--tw-divide-x-reverse));
    border-left-width: calc(1px * calc(1 - var(--tw-divide-x-reverse)));
  }

  :where(.divide-y > :not(:last-child)) {
    --tw-divide-y-reverse: 0;
    border-bottom-style: var(--tw-border-style);
    border-top-style: var(--tw-border-style);
    border-top-width: calc(1px * var(--tw-divide-y-reverse));
    border-bottom-width: calc(1px * calc(1 - var(--tw-divide-y-reverse)));
  }

  :where(.divide-y-4 > :not(:last-child)) {
    --tw-divide-y-reverse: 0;
    border-bottom-style: var(--tw-border-style);
    border-top-style: var(--tw-border-style);
    border-top-width: calc(4px * var(--tw-divide-y-reverse));
    border-bottom-width: calc(4px * calc(1 - var(--tw-divide-y-reverse)));
  }

  :where(.divide-dashed > :not(:last-child)) {
    --tw-border-style: dashed;
    border-style: dashed;
  }

  :where(.divide-emerald-600\/25 > :not(:last-child)) {
    border-color: rgba(0, 151, 103, .25);
  }

  @supports (color: color-mix(in lab, red, red)) {
    :where(.divide-emerald-600\/25 > :not(:last-child)) {
      border-color: color-mix(in oklab, var(--color-emerald-600) 25%, transparent);
    }
  }

  :where(.divide-gray-200 > :not(:last-child)) {
    border-color: var(--color-gray-200);
  }

  .self-center {
    align-self: center;
  }

  .self-start {
    align-self: flex-start;
  }

  .self-stretch {
    align-self: stretch;
  }

  .truncate {
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }

  .\!overflow-auto {
    overflow: auto !important;
  }

  .overflow-auto {
    overflow: auto;
  }

  .overflow-hidden {
    overflow: hidden;
  }

  .overflow-visible {
    overflow: visible;
  }

  .overflow-x-auto {
    overflow-x: auto;
  }

  .overflow-x-hidden {
    overflow-x: hidden;
  }

  .overflow-y-auto {
    overflow-y: auto;
  }

  .overflow-y-hidden {
    overflow-y: hidden;
  }

  .\!rounded {
    border-radius: .25rem !important;
  }

  .\!rounded-none {
    border-radius: 0 !important;
  }

  .rounded {
    border-radius: .25rem;
  }

  .rounded-2xl {
    border-radius: var(--radius-2xl);
  }

  .rounded-3xl {
    border-radius: var(--radius-3xl);
  }

  .rounded-\[5px\] {
    border-radius: 5px;
  }

  .rounded-\[10px\] {
    border-radius: 10px;
  }

  .rounded-\[32px\] {
    border-radius: 32px;
  }

  .rounded-full {
    border-radius: 3.40282e38px;
  }

  .rounded-lg {
    border-radius: var(--radius-lg);
  }

  .rounded-md {
    border-radius: var(--radius-md);
  }

  .rounded-none {
    border-radius: 0;
  }

  .rounded-sm {
    border-radius: var(--radius-sm);
  }

  .rounded-xl {
    border-radius: var(--radius-xl);
  }

  .rounded-t-\[14px\] {
    border-top-left-radius: 14px;
    border-top-right-radius: 14px;
  }

  .rounded-t-full {
    border-top-left-radius: 3.40282e38px;
    border-top-right-radius: 3.40282e38px;
  }

  .rounded-t-xl {
    border-top-left-radius: var(--radius-xl);
    border-top-right-radius: var(--radius-xl);
  }

  .rounded-l {
    border-top-left-radius: .25rem;
    border-bottom-left-radius: .25rem;
  }

  .rounded-r {
    border-top-right-radius: .25rem;
    border-bottom-right-radius: .25rem;
  }

  .rounded-b {
    border-bottom-right-radius: .25rem;
    border-bottom-left-radius: .25rem;
  }

  .rounded-b-xl {
    border-bottom-right-radius: var(--radius-xl);
    border-bottom-left-radius: var(--radius-xl);
  }

  .\!border-0 {
    border-style: var(--tw-border-style) !important;
    border-width: 0 !important;
  }

  .border {
    border-style: var(--tw-border-style);
    border-width: 1px;
  }

  .border-0 {
    border-style: var(--tw-border-style);
    border-width: 0;
  }

  .border-2 {
    border-style: var(--tw-border-style);
    border-width: 2px;
  }

  .border-4 {
    border-style: var(--tw-border-style);
    border-width: 4px;
  }

  .border-\[0\.5px\] {
    border-style: var(--tw-border-style);
    border-width: .5px;
  }

  .border-\[5px\] {
    border-style: var(--tw-border-style);
    border-width: 5px;
  }

  .border-t, .border-t-\[1px\] {
    border-top-style: var(--tw-border-style);
    border-top-width: 1px;
  }

  .border-r {
    border-right-style: var(--tw-border-style);
    border-right-width: 1px;
  }

  .border-r-0 {
    border-right-style: var(--tw-border-style);
    border-right-width: 0;
  }

  .border-b, .border-b-\[1px\] {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 1px;
  }

  .border-l {
    border-left-style: var(--tw-border-style);
    border-left-width: 1px;
  }

  .border-dashed {
    --tw-border-style: dashed;
    border-style: dashed;
  }

  .border-solid {
    --tw-border-style: solid;
    border-style: solid;
  }

  .\!border-amber-500 {
    border-color: var(--color-amber-500) !important;
  }

  .border-\[\#56575814\] {
    border-color: rgba(86, 87, 88, .08);
  }

  .border-\[\#E6E6E6\] {
    border-color: #e6e6e6;
  }

  .border-\[\#F9F9F9\] {
    border-color: #f9f9f9;
  }

  .border-\[\#FBF8FF\] {
    border-color: #fbf8ff;
  }

  .border-\[\#e6e6e6\] {
    border-color: #e6e6e6;
  }

  .border-\[\#e6e7e914\] {
    border-color: rgba(230, 231, 233, .08);
  }

  .border-amber-500 {
    border-color: var(--color-amber-500);
  }

  .border-blue-200 {
    border-color: var(--color-blue-200);
  }

  .border-blue-600\/25 {
    border-color: rgba(21, 93, 252, .25);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-blue-600\/25 {
      border-color: color-mix(in oklab, var(--color-blue-600) 25%, transparent);
    }
  }

  .border-emerald-600\/25 {
    border-color: rgba(0, 151, 103, .25);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-emerald-600\/25 {
      border-color: color-mix(in oklab, var(--color-emerald-600) 25%, transparent);
    }
  }

  .border-gray-100 {
    border-color: var(--color-gray-100);
  }

  .border-gray-200 {
    border-color: var(--color-gray-200);
  }

  .border-gray-300 {
    border-color: var(--color-gray-300);
  }

  .border-gray-500\/5 {
    border-color: rgba(106, 114, 130, .05);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-gray-500\/5 {
      border-color: color-mix(in oklab, var(--color-gray-500) 5%, transparent);
    }
  }

  .border-gray-800 {
    border-color: var(--color-gray-800);
  }

  .border-gray-950\/20 {
    border-color: rgba(3, 7, 18, .2);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-gray-950\/20 {
      border-color: color-mix(in oklab, var(--color-gray-950) 20%, transparent);
    }
  }

  .border-green-200 {
    border-color: var(--color-green-200);
  }

  .border-neutral-200 {
    border-color: var(--color-neutral-200);
  }

  .border-neutral-300 {
    border-color: var(--color-neutral-300);
  }

  .border-purple-200 {
    border-color: var(--color-purple-200);
  }

  .border-red-500 {
    border-color: var(--color-red-500);
  }

  .border-transparent {
    border-color: rgba(0, 0, 0, 0);
  }

  .border-white {
    border-color: var(--color-white);
  }

  .border-b-\[\#0000000a\] {
    border-bottom-color: rgba(0, 0, 0, .04);
  }

  .border-b-\[\#222A39\] {
    border-bottom-color: #222a39;
  }

  .\!bg-amber-500 {
    background-color: var(--color-amber-500) !important;
  }

  .\!bg-inherit {
    background-color: inherit !important;
  }

  .\!bg-transparent {
    background-color: rgba(0, 0, 0, 0) !important;
  }

  .bg-\[\#6b6b6b\] {
    background-color: #6b6b6b;
  }

  .bg-\[\#F0EDFF\] {
    background-color: #f0edff;
  }

  .bg-\[\#F9F9F9\] {
    background-color: #f9f9f9;
  }

  .bg-\[\#FBF8FF\] {
    background-color: #fbf8ff;
  }

  .bg-\[\#ffffff7a\] {
    background-color: rgba(255, 255, 255, .48);
  }

  .bg-\[\#fffffff2\] {
    background-color: rgba(255, 255, 255, .95);
  }

  .bg-background {
    background-color: var(--background);
  }

  .bg-black {
    background-color: var(--color-black);
  }

  .bg-black\/70 {
    background-color: rgba(0, 0, 0, .7);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-black\/70 {
      background-color: color-mix(in oklab, var(--color-black) 70%, transparent);
    }
  }

  .bg-blue-50 {
    background-color: var(--color-blue-50);
  }

  .bg-blue-100 {
    background-color: var(--color-blue-100);
  }

  .bg-blue-200 {
    background-color: var(--color-blue-200);
  }

  .bg-blue-400 {
    background-color: var(--color-blue-400);
  }

  .bg-blue-500\/10 {
    background-color: rgba(48, 128, 255, .1);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-blue-500\/10 {
      background-color: color-mix(in oklab, var(--color-blue-500) 10%, transparent);
    }
  }

  .bg-blue-600 {
    background-color: var(--color-blue-600);
  }

  .bg-emerald-500\/10 {
    background-color: rgba(0, 187, 127, .1);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-emerald-500\/10 {
      background-color: color-mix(in oklab, var(--color-emerald-500) 10%, transparent);
    }
  }

  .bg-gray-50 {
    background-color: var(--color-gray-50);
  }

  .bg-gray-100 {
    background-color: var(--color-gray-100);
  }

  .bg-gray-200 {
    background-color: var(--color-gray-200);
  }

  .bg-gray-400 {
    background-color: var(--color-gray-400);
  }

  .bg-gray-500 {
    background-color: var(--color-gray-500);
  }

  .bg-gray-900 {
    background-color: var(--color-gray-900);
  }

  .bg-green-50 {
    background-color: var(--color-green-50);
  }

  .bg-green-100 {
    background-color: var(--color-green-100);
  }

  .bg-green-500 {
    background-color: var(--color-green-500);
  }

  .bg-neutral-200 {
    background-color: var(--color-neutral-200);
  }

  .bg-neutral-800 {
    background-color: var(--color-neutral-800);
  }

  .bg-purple-50 {
    background-color: var(--color-purple-50);
  }

  .bg-purple-100 {
    background-color: var(--color-purple-100);
  }

  .bg-purple-400 {
    background-color: var(--color-purple-400);
  }

  .bg-purple-600 {
    background-color: var(--color-purple-600);
  }

  .bg-red-500 {
    background-color: var(--color-red-500);
  }

  .bg-transparent {
    background-color: rgba(0, 0, 0, 0);
  }

  .bg-white {
    background-color: var(--color-white);
  }

  .bg-white\/50 {
    background-color: rgba(255, 255, 255, .5);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-white\/50 {
      background-color: color-mix(in oklab, var(--color-white) 50%, transparent);
    }
  }

  .bg-white\/95 {
    background-color: rgba(255, 255, 255, .95);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-white\/95 {
      background-color: color-mix(in oklab, var(--color-white) 95%, transparent);
    }
  }

  .bg-gradient-to-b {
    --tw-gradient-position: to bottom in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }

  .bg-gradient-to-br {
    --tw-gradient-position: to bottom right in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }

  .bg-gradient-to-r {
    --tw-gradient-position: to right in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }

  .bg-gradient-to-tr {
    --tw-gradient-position: to top right in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }

  .bg-\[linear-gradient\(to_right\,\#FF6C37\,\#FFDE83\,\#FFAC37\,\#FF8C37\)\] {
    background-image: linear-gradient(to right, #ff6c37, #ffde83, #ffac37, #ff8c37);
  }

  .from-blue-50 {
    --tw-gradient-from: var(--color-blue-50);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-blue-600 {
    --tw-gradient-from: var(--color-blue-600);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-gray-50 {
    --tw-gradient-from: var(--color-gray-50);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .via-white {
    --tw-gradient-via: var(--color-white);
    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-via-stops);
  }

  .to-gray-100 {
    --tw-gradient-to: var(--color-gray-100);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-purple-50 {
    --tw-gradient-to: var(--color-purple-50);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-purple-600 {
    --tw-gradient-to: var(--color-purple-600);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .bg-\[length\:300\%_100\%\] {
    background-size: 300% 100%;
  }

  .bg-cover {
    background-size: cover;
  }

  .bg-fixed {
    background-attachment: fixed;
  }

  .bg-\[position\:0\%_0\%\] {
    background-position: 0 0;
  }

  .bg-center {
    background-position: center;
  }

  .fill-current {
    fill: currentColor;
  }

  .fill-gray-500 {
    fill: var(--color-gray-500);
  }

  .stroke-green-500 {
    stroke: var(--color-green-500);
  }

  .object-contain {
    object-fit: contain;
  }

  .object-cover {
    object-fit: cover;
  }

  .object-center {
    object-position: center;
  }

  .\!p-0 {
    padding: calc(var(--spacing) * 0) !important;
  }

  .\!p-0\.25 {
    padding: calc(var(--spacing) * .25) !important;
  }

  .\!p-0\.75 {
    padding: calc(var(--spacing) * .75) !important;
  }

  .\!p-1 {
    padding: calc(var(--spacing) * 1) !important;
  }

  .p-0 {
    padding: calc(var(--spacing) * 0);
  }

  .p-1 {
    padding: calc(var(--spacing) * 1);
  }

  .p-1\.5 {
    padding: calc(var(--spacing) * 1.5);
  }

  .p-2 {
    padding: calc(var(--spacing) * 2);
  }

  .p-3 {
    padding: calc(var(--spacing) * 3);
  }

  .p-4 {
    padding: calc(var(--spacing) * 4);
  }

  .p-5 {
    padding: calc(var(--spacing) * 5);
  }

  .p-6 {
    padding: calc(var(--spacing) * 6);
  }

  .p-8 {
    padding: calc(var(--spacing) * 8);
  }

  .p-10 {
    padding: calc(var(--spacing) * 10);
  }

  .p-12 {
    padding: calc(var(--spacing) * 12);
  }

  .p-20 {
    padding: calc(var(--spacing) * 20);
  }

  .p-\[1px\] {
    padding: 1px;
  }

  .p-\[6px\] {
    padding: 6px;
  }

  .p-\[10px\] {
    padding: 10px;
  }

  .\!px-0 {
    padding-inline: calc(var(--spacing) * 0) !important;
  }

  .\!px-1 {
    padding-inline: calc(var(--spacing) * 1) !important;
  }

  .px-0 {
    padding-inline: calc(var(--spacing) * 0);
  }

  .px-1 {
    padding-inline: calc(var(--spacing) * 1);
  }

  .px-1\.5 {
    padding-inline: calc(var(--spacing) * 1.5);
  }

  .px-2 {
    padding-inline: calc(var(--spacing) * 2);
  }

  .px-2\.5 {
    padding-inline: calc(var(--spacing) * 2.5);
  }

  .px-3 {
    padding-inline: calc(var(--spacing) * 3);
  }

  .px-3\.5 {
    padding-inline: calc(var(--spacing) * 3.5);
  }

  .px-4 {
    padding-inline: calc(var(--spacing) * 4);
  }

  .px-5 {
    padding-inline: calc(var(--spacing) * 5);
  }

  .px-6 {
    padding-inline: calc(var(--spacing) * 6);
  }

  .px-8 {
    padding-inline: calc(var(--spacing) * 8);
  }

  .px-10 {
    padding-inline: calc(var(--spacing) * 10);
  }

  .px-20 {
    padding-inline: calc(var(--spacing) * 20);
  }

  .px-\[1\.6rem\] {
    padding-left: 1.6rem;
    padding-right: 1.6rem;
  }

  .px-\[6px\] {
    padding-left: 6px;
    padding-right: 6px;
  }

  .px-\[10px\] {
    padding-left: 10px;
    padding-right: 10px;
  }

  .px-\[20px\] {
    padding-left: 20px;
    padding-right: 20px;
  }

  .\!py-0 {
    padding-block: calc(var(--spacing) * 0) !important;
  }

  .\!py-0\.5 {
    padding-block: calc(var(--spacing) * .5) !important;
  }

  .py-0\.5 {
    padding-block: calc(var(--spacing) * .5);
  }

  .py-1 {
    padding-block: calc(var(--spacing) * 1);
  }

  .py-1\.5 {
    padding-block: calc(var(--spacing) * 1.5);
  }

  .py-2 {
    padding-block: calc(var(--spacing) * 2);
  }

  .py-2\.5 {
    padding-block: calc(var(--spacing) * 2.5);
  }

  .py-3 {
    padding-block: calc(var(--spacing) * 3);
  }

  .py-4 {
    padding-block: calc(var(--spacing) * 4);
  }

  .py-5 {
    padding-block: calc(var(--spacing) * 5);
  }

  .py-6 {
    padding-block: calc(var(--spacing) * 6);
  }

  .py-8 {
    padding-block: calc(var(--spacing) * 8);
  }

  .py-10 {
    padding-block: calc(var(--spacing) * 10);
  }

  .py-12 {
    padding-block: calc(var(--spacing) * 12);
  }

  .py-16 {
    padding-block: calc(var(--spacing) * 16);
  }

  .py-20 {
    padding-block: calc(var(--spacing) * 20);
  }

  .py-\[0\.8rem\] {
    padding-top: .8rem;
    padding-bottom: .8rem;
  }

  .py-\[1px\] {
    padding-top: 1px;
    padding-bottom: 1px;
  }

  .py-\[2px\] {
    padding-top: 2px;
    padding-bottom: 2px;
  }

  .py-\[3px\] {
    padding-top: 3px;
    padding-bottom: 3px;
  }

  .py-\[5px\] {
    padding-top: 5px;
    padding-bottom: 5px;
  }

  .py-\[10px\] {
    padding-top: 10px;
    padding-bottom: 10px;
  }

  .py-\[15px\] {
    padding-top: 15px;
    padding-bottom: 15px;
  }

  .py-\[30px\] {
    padding-top: 30px;
    padding-bottom: 30px;
  }

  .py-px {
    padding-top: 1px;
    padding-bottom: 1px;
  }

  .pt-0 {
    padding-top: calc(var(--spacing) * 0);
  }

  .pt-1 {
    padding-top: calc(var(--spacing) * 1);
  }

  .pt-2 {
    padding-top: calc(var(--spacing) * 2);
  }

  .pt-3 {
    padding-top: calc(var(--spacing) * 3);
  }

  .pt-4 {
    padding-top: calc(var(--spacing) * 4);
  }

  .pt-5 {
    padding-top: calc(var(--spacing) * 5);
  }

  .pt-6 {
    padding-top: calc(var(--spacing) * 6);
  }

  .pt-8 {
    padding-top: calc(var(--spacing) * 8);
  }

  .pt-10 {
    padding-top: calc(var(--spacing) * 10);
  }

  .pt-16 {
    padding-top: calc(var(--spacing) * 16);
  }

  .pt-20 {
    padding-top: calc(var(--spacing) * 20);
  }

  .pt-40 {
    padding-top: calc(var(--spacing) * 40);
  }

  .pt-\[0\.3rem\] {
    padding-top: .3rem;
  }

  .pt-\[1\.6rem\] {
    padding-top: 1.6rem;
  }

  .pt-\[14px\] {
    padding-top: 14px;
  }

  .pr-1 {
    padding-right: calc(var(--spacing) * 1);
  }

  .pr-2 {
    padding-right: calc(var(--spacing) * 2);
  }

  .pr-3 {
    padding-right: calc(var(--spacing) * 3);
  }

  .pr-4 {
    padding-right: calc(var(--spacing) * 4);
  }

  .pr-8 {
    padding-right: calc(var(--spacing) * 8);
  }

  .pr-10 {
    padding-right: calc(var(--spacing) * 10);
  }

  .pr-\[20px\] {
    padding-right: 20px;
  }

  .pb-0 {
    padding-bottom: calc(var(--spacing) * 0);
  }

  .pb-1 {
    padding-bottom: calc(var(--spacing) * 1);
  }

  .pb-2 {
    padding-bottom: calc(var(--spacing) * 2);
  }

  .pb-2\.5 {
    padding-bottom: calc(var(--spacing) * 2.5);
  }

  .pb-3 {
    padding-bottom: calc(var(--spacing) * 3);
  }

  .pb-4 {
    padding-bottom: calc(var(--spacing) * 4);
  }

  .pb-5 {
    padding-bottom: calc(var(--spacing) * 5);
  }

  .pb-6 {
    padding-bottom: calc(var(--spacing) * 6);
  }

  .pb-10 {
    padding-bottom: calc(var(--spacing) * 10);
  }

  .pb-16 {
    padding-bottom: calc(var(--spacing) * 16);
  }

  .pb-28 {
    padding-bottom: calc(var(--spacing) * 28);
  }

  .pb-\[7px\] {
    padding-bottom: 7px;
  }

  .pb-\[15px\] {
    padding-bottom: 15px;
  }

  .pl-0 {
    padding-left: calc(var(--spacing) * 0);
  }

  .pl-1 {
    padding-left: calc(var(--spacing) * 1);
  }

  .pl-2 {
    padding-left: calc(var(--spacing) * 2);
  }

  .pl-3 {
    padding-left: calc(var(--spacing) * 3);
  }

  .pl-3\.5 {
    padding-left: calc(var(--spacing) * 3.5);
  }

  .pl-4 {
    padding-left: calc(var(--spacing) * 4);
  }

  .pl-5 {
    padding-left: calc(var(--spacing) * 5);
  }

  .pl-6 {
    padding-left: calc(var(--spacing) * 6);
  }

  .pl-8 {
    padding-left: calc(var(--spacing) * 8);
  }

  .pl-10 {
    padding-left: calc(var(--spacing) * 10);
  }

  .pl-\[1\.6rem\] {
    padding-left: 1.6rem;
  }

  .text-center {
    text-align: center;
  }

  .text-left {
    text-align: left;
  }

  .text-right {
    text-align: right;
  }

  .align-bottom {
    vertical-align: bottom;
  }

  .align-middle {
    vertical-align: middle;
  }

  .\!font-mono {
    font-family: var(--font-geist-mono) !important;
  }

  .font-mono {
    font-family: var(--font-geist-mono);
  }

  .\!text-2xl {
    font-size: var(--text-2xl) !important;
    line-height: var(--tw-leading, var(--text-2xl--line-height)) !important;
  }

  .text-2xl {
    font-size: var(--text-2xl);
    line-height: var(--tw-leading, var(--text-2xl--line-height));
  }

  .text-3xl {
    font-size: var(--text-3xl);
    line-height: var(--tw-leading, var(--text-3xl--line-height));
  }

  .text-4xl {
    font-size: var(--text-4xl);
    line-height: var(--tw-leading, var(--text-4xl--line-height));
  }

  .text-5xl {
    font-size: var(--text-5xl);
    line-height: var(--tw-leading, var(--text-5xl--line-height));
  }

  .text-base {
    font-size: var(--text-base);
    line-height: var(--tw-leading, var(--text-base--line-height));
  }

  .text-lg {
    font-size: var(--text-lg);
    line-height: var(--tw-leading, var(--text-lg--line-height));
  }

  .text-sm {
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
  }

  .text-xl {
    font-size: var(--text-xl);
    line-height: var(--tw-leading, var(--text-xl--line-height));
  }

  .text-xs {
    font-size: var(--text-xs);
    line-height: var(--tw-leading, var(--text-xs--line-height));
  }

  .\!text-\[12px\] {
    font-size: 12px !important;
  }

  .text-\[8px\] {
    font-size: 8px;
  }

  .text-\[12px\] {
    font-size: 12px;
  }

  .text-\[13px\] {
    font-size: 13px;
  }

  .text-\[14px\] {
    font-size: 14px;
  }

  .text-\[15px\] {
    font-size: 15px;
  }

  .text-\[16px\] {
    font-size: 16px;
  }

  .text-\[32px\] {
    font-size: 32px;
  }

  .text-\[40px\] {
    font-size: 40px;
  }

  .leading-4 {
    --tw-leading: calc(var(--spacing) * 4);
    line-height: calc(var(--spacing) * 4);
  }

  .leading-5 {
    --tw-leading: calc(var(--spacing) * 5);
    line-height: calc(var(--spacing) * 5);
  }

  .leading-6 {
    --tw-leading: calc(var(--spacing) * 6);
    line-height: calc(var(--spacing) * 6);
  }

  .leading-8 {
    --tw-leading: calc(var(--spacing) * 8);
    line-height: calc(var(--spacing) * 8);
  }

  .leading-10 {
    --tw-leading: calc(var(--spacing) * 10);
    line-height: calc(var(--spacing) * 10);
  }

  .leading-130 {
    --tw-leading: calc(var(--spacing) * 130);
    line-height: calc(var(--spacing) * 130);
  }

  .leading-140 {
    --tw-leading: calc(var(--spacing) * 140);
    line-height: calc(var(--spacing) * 140);
  }

  .leading-\[10px\] {
    --tw-leading: 10px;
    line-height: 10px;
  }

  .leading-\[21px\] {
    --tw-leading: 21px;
    line-height: 21px;
  }

  .leading-\[23px\] {
    --tw-leading: 23px;
    line-height: 23px;
  }

  .leading-normal {
    --tw-leading: var(--leading-normal);
    line-height: var(--leading-normal);
  }

  .leading-relaxed {
    --tw-leading: var(--leading-relaxed);
    line-height: var(--leading-relaxed);
  }

  .\!font-bold {
    --tw-font-weight: var(--font-weight-bold) !important;
    font-weight: var(--font-weight-bold) !important;
  }

  .font-bold {
    --tw-font-weight: var(--font-weight-bold);
    font-weight: var(--font-weight-bold);
  }

  .font-extrabold {
    --tw-font-weight: var(--font-weight-extrabold);
    font-weight: var(--font-weight-extrabold);
  }

  .font-medium {
    --tw-font-weight: var(--font-weight-medium);
    font-weight: var(--font-weight-medium);
  }

  .font-normal {
    --tw-font-weight: var(--font-weight-normal);
    font-weight: var(--font-weight-normal);
  }

  .font-semibold {
    --tw-font-weight: var(--font-weight-semibold);
    font-weight: var(--font-weight-semibold);
  }

  .tracking-normal {
    --tw-tracking: var(--tracking-normal);
    letter-spacing: var(--tracking-normal);
  }

  .tracking-tight {
    --tw-tracking: var(--tracking-tight);
    letter-spacing: var(--tracking-tight);
  }

  .tracking-wide {
    --tw-tracking: var(--tracking-wide);
    letter-spacing: var(--tracking-wide);
  }

  .tracking-wider {
    --tw-tracking: var(--tracking-wider);
    letter-spacing: var(--tracking-wider);
  }

  .text-nowrap {
    text-wrap: nowrap;
  }

  .break-all {
    word-break: break-all;
  }

  .overflow-ellipsis {
    text-overflow: ellipsis;
  }

  .whitespace-normal {
    white-space: normal;
  }

  .whitespace-nowrap {
    white-space: nowrap;
  }

  .whitespace-pre-wrap {
    white-space: pre-wrap;
  }

  .\!text-blue-500 {
    color: var(--color-blue-500) !important;
  }

  .\!text-emerald-500 {
    color: var(--color-emerald-500) !important;
  }

  .\!text-gray-500 {
    color: var(--color-gray-500) !important;
  }

  .\!text-green-500 {
    color: var(--color-green-500) !important;
  }

  .\!text-red-500 {
    color: var(--color-red-500) !important;
  }

  .\!text-yellow-500 {
    color: var(--color-yellow-500) !important;
  }

  .text-\[\#6F2CBA\] {
    color: #6f2cba;
  }

  .text-\[\#6b6b6b\] {
    color: #6b6b6b;
  }

  .text-\[\#007bff\] {
    color: #007bff;
  }

  .text-\[\#0265D2\] {
    color: #0265d2;
  }

  .text-\[\#333\] {
    color: #333;
  }

  .text-\[\#564e6e\] {
    color: #564e6e;
  }

  .text-\[\#212121\] {
    color: #212121;
  }

  .text-amber-500 {
    color: var(--color-amber-500);
  }

  .text-black {
    color: var(--color-black);
  }

  .text-blue-100 {
    color: var(--color-blue-100);
  }

  .text-blue-600 {
    color: var(--color-blue-600);
  }

  .text-blue-700 {
    color: var(--color-blue-700);
  }

  .text-blue-800 {
    color: var(--color-blue-800);
  }

  .text-current {
    color: currentColor;
  }

  .text-gray-300 {
    color: var(--color-gray-300);
  }

  .text-gray-400 {
    color: var(--color-gray-400);
  }

  .text-gray-500 {
    color: var(--color-gray-500);
  }

  .text-gray-600 {
    color: var(--color-gray-600);
  }

  .text-gray-700 {
    color: var(--color-gray-700);
  }

  .text-gray-800 {
    color: var(--color-gray-800);
  }

  .text-gray-900 {
    color: var(--color-gray-900);
  }

  .text-green-500 {
    color: var(--color-green-500);
  }

  .text-green-600 {
    color: var(--color-green-600);
  }

  .text-green-800 {
    color: var(--color-green-800);
  }

  .text-neutral-400 {
    color: var(--color-neutral-400);
  }

  .text-neutral-500 {
    color: var(--color-neutral-500);
  }

  .text-neutral-600 {
    color: var(--color-neutral-600);
  }

  .text-neutral-700 {
    color: var(--color-neutral-700);
  }

  .text-purple-500 {
    color: var(--color-purple-500);
  }

  .text-purple-600 {
    color: var(--color-purple-600);
  }

  .text-red-400 {
    color: var(--color-red-400);
  }

  .text-red-500 {
    color: var(--color-red-500);
  }

  .text-slate-900 {
    color: var(--color-slate-900);
  }

  .text-white {
    color: var(--color-white);
  }

  .text-yellow-500 {
    color: var(--color-yellow-500);
  }

  .text-yellow-600 {
    color: var(--color-yellow-600);
  }

  .capitalize {
    text-transform: capitalize;
  }

  .lowercase {
    text-transform: lowercase;
  }

  .uppercase {
    text-transform: uppercase;
  }

  .italic {
    font-style: italic;
  }

  .ordinal {
    --tw-ordinal: ordinal;
    font-variant-numeric: var(--tw-ordinal, ) var(--tw-slashed-zero, ) var(--tw-numeric-figure, ) var(--tw-numeric-spacing, ) var(--tw-numeric-fraction, );
  }

  .line-through {
    -webkit-text-decoration-line: line-through;
    text-decoration-line: line-through;
  }

  .no-underline {
    -webkit-text-decoration-line: none;
    text-decoration-line: none;
  }

  .underline {
    -webkit-text-decoration-line: underline;
    text-decoration-line: underline;
  }

  .antialiased {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  .placeholder-gray-600::placeholder {
    color: var(--color-gray-600);
  }

  .opacity-0 {
    opacity: 0;
  }

  .opacity-25 {
    opacity: .25;
  }

  .opacity-40 {
    opacity: .4;
  }

  .opacity-50 {
    opacity: .5;
  }

  .opacity-75 {
    opacity: .75;
  }

  .opacity-80 {
    opacity: .8;
  }

  .opacity-90 {
    opacity: .9;
  }

  .shadow {
    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgba(0, 0, 0, .1)), 0 1px 2px -1px var(--tw-shadow-color, rgba(0, 0, 0, .1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-2xl {
    --tw-shadow: 0 25px 50px -12px var(--tw-shadow-color, rgba(0, 0, 0, .25));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-\[0_4px_15px_0_rgba\(255\,108\,55\,0\.45\)\] {
    --tw-shadow: 0 4px 15px 0 var(--tw-shadow-color, rgba(255, 108, 55, .45));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-lg {
    --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, rgba(0, 0, 0, .1)), 0 4px 6px -4px var(--tw-shadow-color, rgba(0, 0, 0, .1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-md {
    --tw-shadow: 0 4px 6px -1px var(--tw-shadow-color, rgba(0, 0, 0, .1)), 0 2px 4px -2px var(--tw-shadow-color, rgba(0, 0, 0, .1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-sm {
    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgba(0, 0, 0, .1)), 0 1px 2px -1px var(--tw-shadow-color, rgba(0, 0, 0, .1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-xl {
    --tw-shadow: 0 20px 25px -5px var(--tw-shadow-color, rgba(0, 0, 0, .1)), 0 8px 10px -6px var(--tw-shadow-color, rgba(0, 0, 0, .1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .ring, .ring-1 {
    --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .ring-2 {
    --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-gray-900\/5 {
    --tw-shadow-color: rgba(16, 24, 40, .05);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .shadow-gray-900\/5 {
      --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, var(--color-gray-900) 5%, transparent) var(--tw-shadow-alpha), transparent);
    }
  }

  .ring-blue-600 {
    --tw-ring-color: var(--color-blue-600);
  }

  .ring-gray-200 {
    --tw-ring-color: var(--color-gray-200);
  }

  .ring-gray-400\/20 {
    --tw-ring-color: rgba(153, 161, 175, .2);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .ring-gray-400\/20 {
      --tw-ring-color: color-mix(in oklab, var(--color-gray-400) 20%, transparent);
    }
  }

  .ring-gray-950\/5 {
    --tw-ring-color: rgba(3, 7, 18, .05);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .ring-gray-950\/5 {
      --tw-ring-color: color-mix(in oklab, var(--color-gray-950) 5%, transparent);
    }
  }

  .outline {
    outline-style: var(--tw-outline-style);
    outline-width: 1px;
  }

  .blur {
    --tw-blur: blur(8px);
    filter: var(--tw-blur, ) var(--tw-brightness, ) var(--tw-contrast, ) var(--tw-grayscale, ) var(--tw-hue-rotate, ) var(--tw-invert, ) var(--tw-saturate, ) var(--tw-sepia, ) var(--tw-drop-shadow, );
  }

  .drop-shadow-\[var\(--ui-text-color\)_80px_0\] {
    --tw-drop-shadow-size: drop-shadow(var(--tw-drop-shadow-color, var(--ui-text-color)) 80px 0);
    --tw-drop-shadow: var(--tw-drop-shadow-size);
    filter: var(--tw-blur, ) var(--tw-brightness, ) var(--tw-contrast, ) var(--tw-grayscale, ) var(--tw-hue-rotate, ) var(--tw-invert, ) var(--tw-saturate, ) var(--tw-sepia, ) var(--tw-drop-shadow, );
  }

  .invert {
    --tw-invert: invert(100%);
    filter: var(--tw-blur, ) var(--tw-brightness, ) var(--tw-contrast, ) var(--tw-grayscale, ) var(--tw-hue-rotate, ) var(--tw-invert, ) var(--tw-saturate, ) var(--tw-sepia, ) var(--tw-drop-shadow, );
  }

  .filter {
    filter: var(--tw-blur, ) var(--tw-brightness, ) var(--tw-contrast, ) var(--tw-grayscale, ) var(--tw-hue-rotate, ) var(--tw-invert, ) var(--tw-saturate, ) var(--tw-sepia, ) var(--tw-drop-shadow, );
  }

  .filter\! {
    filter: var(--tw-blur, ) var(--tw-brightness, ) var(--tw-contrast, ) var(--tw-grayscale, ) var(--tw-hue-rotate, ) var(--tw-invert, ) var(--tw-saturate, ) var(--tw-sepia, ) var(--tw-drop-shadow, ) !important;
  }

  .backdrop-blur {
    --tw-backdrop-blur: blur(8px);
    -webkit-backdrop-filter: var(--tw-backdrop-blur, ) var(--tw-backdrop-brightness, ) var(--tw-backdrop-contrast, ) var(--tw-backdrop-grayscale, ) var(--tw-backdrop-hue-rotate, ) var(--tw-backdrop-invert, ) var(--tw-backdrop-opacity, ) var(--tw-backdrop-saturate, ) var(--tw-backdrop-sepia, );
    backdrop-filter: var(--tw-backdrop-blur, ) var(--tw-backdrop-brightness, ) var(--tw-backdrop-contrast, ) var(--tw-backdrop-grayscale, ) var(--tw-backdrop-hue-rotate, ) var(--tw-backdrop-invert, ) var(--tw-backdrop-opacity, ) var(--tw-backdrop-saturate, ) var(--tw-backdrop-sepia, );
  }

  .backdrop-blur-lg {
    --tw-backdrop-blur: blur(var(--blur-lg));
    -webkit-backdrop-filter: var(--tw-backdrop-blur, ) var(--tw-backdrop-brightness, ) var(--tw-backdrop-contrast, ) var(--tw-backdrop-grayscale, ) var(--tw-backdrop-hue-rotate, ) var(--tw-backdrop-invert, ) var(--tw-backdrop-opacity, ) var(--tw-backdrop-saturate, ) var(--tw-backdrop-sepia, );
    backdrop-filter: var(--tw-backdrop-blur, ) var(--tw-backdrop-brightness, ) var(--tw-backdrop-contrast, ) var(--tw-backdrop-grayscale, ) var(--tw-backdrop-hue-rotate, ) var(--tw-backdrop-invert, ) var(--tw-backdrop-opacity, ) var(--tw-backdrop-saturate, ) var(--tw-backdrop-sepia, );
  }

  .backdrop-blur-sm {
    --tw-backdrop-blur: blur(var(--blur-sm));
    -webkit-backdrop-filter: var(--tw-backdrop-blur, ) var(--tw-backdrop-brightness, ) var(--tw-backdrop-contrast, ) var(--tw-backdrop-grayscale, ) var(--tw-backdrop-hue-rotate, ) var(--tw-backdrop-invert, ) var(--tw-backdrop-opacity, ) var(--tw-backdrop-saturate, ) var(--tw-backdrop-sepia, );
    backdrop-filter: var(--tw-backdrop-blur, ) var(--tw-backdrop-brightness, ) var(--tw-backdrop-contrast, ) var(--tw-backdrop-grayscale, ) var(--tw-backdrop-hue-rotate, ) var(--tw-backdrop-invert, ) var(--tw-backdrop-opacity, ) var(--tw-backdrop-saturate, ) var(--tw-backdrop-sepia, );
  }

  .backdrop-blur-xl {
    --tw-backdrop-blur: blur(var(--blur-xl));
    -webkit-backdrop-filter: var(--tw-backdrop-blur, ) var(--tw-backdrop-brightness, ) var(--tw-backdrop-contrast, ) var(--tw-backdrop-grayscale, ) var(--tw-backdrop-hue-rotate, ) var(--tw-backdrop-invert, ) var(--tw-backdrop-opacity, ) var(--tw-backdrop-saturate, ) var(--tw-backdrop-sepia, );
    backdrop-filter: var(--tw-backdrop-blur, ) var(--tw-backdrop-brightness, ) var(--tw-backdrop-contrast, ) var(--tw-backdrop-grayscale, ) var(--tw-backdrop-hue-rotate, ) var(--tw-backdrop-invert, ) var(--tw-backdrop-opacity, ) var(--tw-backdrop-saturate, ) var(--tw-backdrop-sepia, );
  }

  .transition {
    transition-property: color, background-color, border-color, outline-color, -webkit-text-decoration-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to, opacity, box-shadow, transform, translate, scale, rotate, filter, -webkit-backdrop-filter, backdrop-filter, display, content-visibility, overlay, pointer-events;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-\[background-position\] {
    transition-property: background-position;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-\[max-height\,opacity\] {
    transition-property: max-height, opacity;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-\[width\] {
    transition-property: width;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-all {
    transition-property: all;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-colors {
    transition-property: color, background-color, border-color, outline-color, -webkit-text-decoration-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-opacity {
    transition-property: opacity;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-shadow {
    transition-property: box-shadow;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-transform {
    transition-property: transform, translate, scale, rotate;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-none {
    transition-property: none;
  }

  .duration-100 {
    --tw-duration: .1s;
    transition-duration: .1s;
  }

  .duration-200 {
    --tw-duration: .2s;
    transition-duration: .2s;
  }

  .duration-300 {
    --tw-duration: .3s;
    transition-duration: .3s;
  }

  .duration-400 {
    --tw-duration: .4s;
    transition-duration: .4s;
  }

  .duration-500 {
    --tw-duration: .5s;
    transition-duration: .5s;
  }

  .duration-\[400ms\] {
    --tw-duration: .4s;
    transition-duration: .4s;
  }

  .ease-in-out {
    --tw-ease: var(--ease-in-out);
    transition-timing-function: var(--ease-in-out);
  }

  .ease-out {
    --tw-ease: var(--ease-out);
    transition-timing-function: var(--ease-out);
  }

  .outline-none {
    --tw-outline-style: none;
    outline-style: none;
  }

  .select-all {
    -webkit-user-select: all;
    -moz-user-select: all;
    user-select: all;
  }

  .select-none {
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
  }

  .\[transition\:transform_500ms\,opacity_200ms\,left_200ms\,width_400ms\] {
    transition: transform .5s, opacity .2s, left .2s, width .4s;
  }

  @media (hover: hover) {
    .group-hover\:block:is(:where(.group):hover *) {
      display: block;
    }
  }

  @media (hover: hover) {
    .group-hover\:flex:is(:where(.group):hover *) {
      display: flex;
    }
  }

  @media (hover: hover) {
    .group-hover\:inline-flex:is(:where(.group):hover *) {
      display: inline-flex;
    }
  }

  @media (hover: hover) {
    .group-hover\:-translate-y-1:is(:where(.group):hover *) {
      --tw-translate-y: calc(var(--spacing) * -1);
      translate: var(--tw-translate-x) var(--tw-translate-y);
    }
  }

  @media (hover: hover) {
    .group-hover\:bg-white:is(:where(.group):hover *) {
      background-color: var(--color-white);
    }
  }

  @media (hover: hover) {
    .group-hover\:fill-gray-700:is(:where(.group):hover *) {
      fill: var(--color-gray-700);
    }
  }

  @media (hover: hover) {
    .group-hover\:text-gray-600:is(:where(.group):hover *) {
      color: var(--color-gray-600);
    }
  }

  @media (hover: hover) {
    .group-hover\:text-gray-700:is(:where(.group):hover *) {
      color: var(--color-gray-700);
    }
  }

  @media (hover: hover) {
    .group-hover\:opacity-100:is(:where(.group):hover *) {
      opacity: 1;
    }
  }

  @media (hover: hover) {
    .group-hover\:ring-0:is(:where(.group):hover *) {
      --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }

  @media (hover: hover) {
    .group-hover\:brightness-100:is(:where(.group):hover *) {
      --tw-brightness: brightness(100%);
      filter: var(--tw-blur, ) var(--tw-brightness, ) var(--tw-contrast, ) var(--tw-grayscale, ) var(--tw-hue-rotate, ) var(--tw-invert, ) var(--tw-saturate, ) var(--tw-sepia, ) var(--tw-drop-shadow, );
    }
  }

  .group-data-\[state\=closed\]\:block:is(:where(.group)[data-state="closed"] *) {
    display: block;
  }

  .group-data-\[state\=closed\]\:hidden:is(:where(.group)[data-state="closed"] *) {
    display: none;
  }

  .group-data-\[state\=open\]\:block:is(:where(.group)[data-state="open"] *) {
    display: block;
  }

  .group-data-\[state\=open\]\:hidden:is(:where(.group)[data-state="open"] *) {
    display: none;
  }

  .peer-\[\.is-center\]\:max-w-3xl:is(:where(.peer).is-center ~ *) {
    max-width: var(--container-3xl);
  }

  .peer-\[\.is-custom\]\:contents:is(:where(.peer).is-custom ~ *) {
    display: contents;
  }

  .peer-\[\.is-not-custom\]\:mx-auto:is(:where(.peer).is-not-custom ~ *) {
    margin-left: auto;
    margin-right: auto;
  }

  .peer-\[\.is-not-custom\]\:px-4:is(:where(.peer).is-not-custom ~ *) {
    padding-inline: calc(var(--spacing) * 4);
  }

  .file\:mr-2::-webkit-file-upload-button {
    margin-right: calc(var(--spacing) * 2);
  }

  .file\:mr-2::file-selector-button {
    margin-right: calc(var(--spacing) * 2);
  }

  .file\:cursor-pointer::-webkit-file-upload-button {
    cursor: pointer;
  }

  .file\:cursor-pointer::file-selector-button {
    cursor: pointer;
  }

  .file\:rounded::-webkit-file-upload-button {
    border-radius: .25rem;
  }

  .file\:rounded::file-selector-button {
    border-radius: .25rem;
  }

  .file\:border-0::-webkit-file-upload-button {
    border-style: var(--tw-border-style);
    border-width: 0;
  }

  .file\:border-0::file-selector-button {
    border-style: var(--tw-border-style);
    border-width: 0;
  }

  .file\:px-4::-webkit-file-upload-button {
    padding-inline: calc(var(--spacing) * 4);
  }

  .file\:px-4::file-selector-button {
    padding-inline: calc(var(--spacing) * 4);
  }

  .file\:py-1::-webkit-file-upload-button {
    padding-block: calc(var(--spacing) * 1);
  }

  .file\:py-1::file-selector-button {
    padding-block: calc(var(--spacing) * 1);
  }

  .file\:transition::-webkit-file-upload-button {
    transition-property: color, background-color, border-color, outline-color, -webkit-text-decoration-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to, opacity, box-shadow, transform, translate, scale, rotate, filter, -webkit-backdrop-filter, backdrop-filter, display, content-visibility, overlay, pointer-events;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .file\:transition::file-selector-button {
    transition-property: color, background-color, border-color, outline-color, -webkit-text-decoration-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to, opacity, box-shadow, transform, translate, scale, rotate, filter, -webkit-backdrop-filter, backdrop-filter, display, content-visibility, overlay, pointer-events;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .before\:absolute:before {
    content: var(--tw-content);
    position: absolute;
  }

  .before\:top-1\/2:before {
    content: var(--tw-content);
    top: 50%;
  }

  .before\:left-1\/2:before {
    content: var(--tw-content);
    left: 50%;
  }

  .before\:aspect-square:before {
    content: var(--tw-content);
    aspect-ratio: 1;
  }

  .before\:w-full:before {
    content: var(--tw-content);
    width: 100%;
  }

  .before\:-translate-x-1\/2:before {
    content: var(--tw-content);
    --tw-translate-x: calc(calc(1 / 2 * 100%) * -1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .before\:-translate-y-1\/2:before {
    content: var(--tw-content);
    --tw-translate-y: calc(calc(1 / 2 * 100%) * -1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .before\:rounded-md:before {
    content: var(--tw-content);
    border-radius: var(--radius-md);
  }

  .first\:scroll-m-20:first-child {
    scroll-margin: calc(var(--spacing) * 20);
  }

  .last\:mb-0:last-child {
    margin-bottom: calc(var(--spacing) * 0);
  }

  .last\:border-b-0:last-child {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 0;
  }

  .focus-within\:w-full:focus-within {
    width: 100%;
  }

  :where(.focus-within\:divide-emerald-600\/20:focus-within > :not(:last-child)) {
    border-color: rgba(0, 151, 103, .2);
  }

  @supports (color: color-mix(in lab, red, red)) {
    :where(.focus-within\:divide-emerald-600\/20:focus-within > :not(:last-child)) {
      border-color: color-mix(in oklab, var(--color-emerald-600) 20%, transparent);
    }
  }

  .focus-within\:border-emerald-600\/20:focus-within {
    border-color: rgba(0, 151, 103, .2);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .focus-within\:border-emerald-600\/20:focus-within {
      border-color: color-mix(in oklab, var(--color-emerald-600) 20%, transparent);
    }
  }

  .focus-within\:bg-emerald-600\/20:focus-within {
    background-color: rgba(0, 151, 103, .2);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .focus-within\:bg-emerald-600\/20:focus-within {
      background-color: color-mix(in oklab, var(--color-emerald-600) 20%, transparent);
    }
  }

  @media (hover: hover) {
    .hover\:translate-x-2:hover {
      --tw-translate-x: calc(var(--spacing) * 2);
      translate: var(--tw-translate-x) var(--tw-translate-y);
    }
  }

  @media (hover: hover) {
    .hover\:scale-100:hover {
      --tw-scale-x: 100%;
      --tw-scale-y: 100%;
      --tw-scale-z: 100%;
      scale: var(--tw-scale-x) var(--tw-scale-y);
    }
  }

  @media (hover: hover) {
    .hover\:scale-105:hover {
      --tw-scale-x: 105%;
      --tw-scale-y: 105%;
      --tw-scale-z: 105%;
      scale: var(--tw-scale-x) var(--tw-scale-y);
    }
  }

  @media (hover: hover) {
    .hover\:scale-x-125:hover {
      --tw-scale-x: 125%;
      scale: var(--tw-scale-x) var(--tw-scale-y);
    }
  }

  @media (hover: hover) {
    .hover\:cursor-pointer:hover {
      cursor: pointer;
    }
  }

  @media (hover: hover) {
    :where(.hover\:divide-emerald-600\/20:hover > :not(:last-child)) {
      border-color: rgba(0, 151, 103, .2);
    }

    @supports (color: color-mix(in lab, red, red)) {
      :where(.hover\:divide-emerald-600\/20:hover > :not(:last-child)) {
        border-color: color-mix(in oklab, var(--color-emerald-600) 20%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:rounded-\[5px\]:hover {
      border-radius: 5px;
    }
  }

  @media (hover: hover) {
    .hover\:border-0:hover {
      border-style: var(--tw-border-style);
      border-width: 0;
    }
  }

  @media (hover: hover) {
    .hover\:border-b:hover {
      border-bottom-style: var(--tw-border-style);
      border-bottom-width: 1px;
    }
  }

  @media (hover: hover) {
    .hover\:border-b-0:hover {
      border-bottom-style: var(--tw-border-style);
      border-bottom-width: 0;
    }
  }

  @media (hover: hover) {
    .hover\:border-b-\[1px\]:hover {
      border-bottom-style: var(--tw-border-style);
      border-bottom-width: 1px;
    }
  }

  @media (hover: hover) {
    .hover\:border-b-\[2px\]:hover {
      border-bottom-style: var(--tw-border-style);
      border-bottom-width: 2px;
    }
  }

  @media (hover: hover) {
    .hover\:border-none:hover {
      --tw-border-style: none;
      border-style: none;
    }
  }

  @media (hover: hover) {
    .hover\:border-blue-600\/20:hover {
      border-color: rgba(21, 93, 252, .2);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:border-blue-600\/20:hover {
        border-color: color-mix(in oklab, var(--color-blue-600) 20%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:border-emerald-600\/20:hover {
      border-color: rgba(0, 151, 103, .2);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:border-emerald-600\/20:hover {
        border-color: color-mix(in oklab, var(--color-emerald-600) 20%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:border-gray-500:hover {
      border-color: var(--color-gray-500);
    }
  }

  @media (hover: hover) {
    .hover\:border-b-\[\#0265D2\]:hover {
      border-bottom-color: #0265d2;
    }
  }

  @media (hover: hover) {
    .hover\:bg-\[\#E8DFFF\]:hover {
      background-color: #e8dfff;
    }
  }

  @media (hover: hover) {
    .hover\:bg-\[\#EFEFEF\]:hover {
      background-color: #efefef;
    }
  }

  @media (hover: hover) {
    .hover\:bg-blue-600\/20:hover {
      background-color: rgba(21, 93, 252, .2);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-blue-600\/20:hover {
        background-color: color-mix(in oklab, var(--color-blue-600) 20%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:bg-blue-700:hover {
      background-color: var(--color-blue-700);
    }
  }

  @media (hover: hover) {
    .hover\:bg-emerald-600\/20:hover {
      background-color: rgba(0, 151, 103, .2);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-emerald-600\/20:hover {
        background-color: color-mix(in oklab, var(--color-emerald-600) 20%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:bg-gray-50:hover {
      background-color: var(--color-gray-50);
    }
  }

  @media (hover: hover) {
    .hover\:bg-gray-100:hover {
      background-color: var(--color-gray-100);
    }
  }

  @media (hover: hover) {
    .hover\:bg-gray-200:hover {
      background-color: var(--color-gray-200);
    }
  }

  @media (hover: hover) {
    .hover\:bg-gray-200\/70:hover {
      background-color: rgba(229, 231, 235, .7);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-gray-200\/70:hover {
        background-color: color-mix(in oklab, var(--color-gray-200) 70%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:bg-gray-500:hover {
      background-color: var(--color-gray-500);
    }
  }

  @media (hover: hover) {
    .hover\:bg-gray-600:hover {
      background-color: var(--color-gray-600);
    }
  }

  @media (hover: hover) {
    .hover\:bg-gray-600\/5:hover {
      background-color: rgba(74, 85, 101, .05);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-gray-600\/5:hover {
        background-color: color-mix(in oklab, var(--color-gray-600) 5%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:bg-neutral-200:hover {
      background-color: var(--color-neutral-200);
    }
  }

  @media (hover: hover) {
    .hover\:bg-neutral-800:hover {
      background-color: var(--color-neutral-800);
    }
  }

  @media (hover: hover) {
    .hover\:bg-purple-700:hover {
      background-color: var(--color-purple-700);
    }
  }

  @media (hover: hover) {
    .hover\:bg-transparent:hover {
      background-color: rgba(0, 0, 0, 0);
    }
  }

  @media (hover: hover) {
    .hover\:bg-white:hover {
      background-color: var(--color-white);
    }
  }

  @media (hover: hover) {
    .hover\:bg-\[position\:100\%_0\%\]:hover {
      background-position: 100% 0;
    }
  }

  @media (hover: hover) {
    .hover\:\!text-gray-600:hover {
      color: var(--color-gray-600) !important;
    }
  }

  @media (hover: hover) {
    .hover\:\!text-green-600:hover {
      color: var(--color-green-600) !important;
    }
  }

  @media (hover: hover) {
    .hover\:\!text-red-600:hover {
      color: var(--color-red-600) !important;
    }
  }

  @media (hover: hover) {
    .hover\:\!text-yellow-600:hover {
      color: var(--color-yellow-600) !important;
    }
  }

  @media (hover: hover) {
    .hover\:text-\[\#0056b3\]:hover {
      color: #0056b3;
    }
  }

  @media (hover: hover) {
    .hover\:text-\[\#212121\]:hover {
      color: #212121;
    }
  }

  @media (hover: hover) {
    .hover\:text-blue-600:hover {
      color: var(--color-blue-600);
    }
  }

  @media (hover: hover) {
    .hover\:text-blue-700:hover {
      color: var(--color-blue-700);
    }
  }

  @media (hover: hover) {
    .hover\:text-gray-600:hover {
      color: var(--color-gray-600);
    }
  }

  @media (hover: hover) {
    .hover\:text-gray-700:hover {
      color: var(--color-gray-700);
    }
  }

  @media (hover: hover) {
    .hover\:text-gray-800:hover {
      color: var(--color-gray-800);
    }
  }

  @media (hover: hover) {
    .hover\:text-gray-900:hover {
      color: var(--color-gray-900);
    }
  }

  @media (hover: hover) {
    .hover\:text-neutral-700:hover {
      color: var(--color-neutral-700);
    }
  }

  @media (hover: hover) {
    .hover\:text-white:hover {
      color: var(--color-white);
    }
  }

  @media (hover: hover) {
    .hover\:text-yellow-500:hover {
      color: var(--color-yellow-500);
    }
  }

  @media (hover: hover) {
    .hover\:no-underline:hover {
      -webkit-text-decoration-line: none;
      text-decoration-line: none;
    }
  }

  @media (hover: hover) {
    .hover\:underline:hover {
      -webkit-text-decoration-line: underline;
      text-decoration-line: underline;
    }
  }

  @media (hover: hover) {
    .hover\:decoration-dashed:hover {
      -webkit-text-decoration-style: dashed;
      text-decoration-style: dashed;
    }
  }

  @media (hover: hover) {
    .hover\:opacity-100:hover {
      opacity: 1;
    }
  }

  @media (hover: hover) {
    .hover\:shadow-\[0_2px_8px_\#0000000a\]:hover {
      --tw-shadow: 0 2px 8px var(--tw-shadow-color, rgba(0, 0, 0, .04));
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }

  @media (hover: hover) {
    .hover\:shadow-md:hover {
      --tw-shadow: 0 4px 6px -1px var(--tw-shadow-color, rgba(0, 0, 0, .1)), 0 2px 4px -2px var(--tw-shadow-color, rgba(0, 0, 0, .1));
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }

  @media (hover: hover) {
    .hover\:shadow-sm:hover {
      --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgba(0, 0, 0, .1)), 0 1px 2px -1px var(--tw-shadow-color, rgba(0, 0, 0, .1));
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }

  @media (hover: hover) {
    .hover\:shadow-xl:hover {
      --tw-shadow: 0 20px 25px -5px var(--tw-shadow-color, rgba(0, 0, 0, .1)), 0 8px 10px -6px var(--tw-shadow-color, rgba(0, 0, 0, .1));
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }

  @media (hover: hover) {
    .hover\:ring-gray-600\/25:hover {
      --tw-ring-color: rgba(74, 85, 101, .25);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:ring-gray-600\/25:hover {
        --tw-ring-color: color-mix(in oklab, var(--color-gray-600) 25%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .focus-within\:hover\:scale-100:focus-within:hover {
      --tw-scale-x: 100%;
      --tw-scale-y: 100%;
      --tw-scale-z: 100%;
      scale: var(--tw-scale-x) var(--tw-scale-y);
    }
  }

  .focus\:border-transparent:focus {
    border-color: rgba(0, 0, 0, 0);
  }

  .focus\:no-underline:focus {
    -webkit-text-decoration-line: none;
    text-decoration-line: none;
  }

  .focus\:ring-2:focus {
    --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .focus\:ring-blue-600:focus {
    --tw-ring-color: var(--color-blue-600);
  }

  .focus\:outline-\[\#6F2CBA\]:focus {
    outline-color: #6f2cba;
  }

  .focus\:outline-\[\#0265D2\]:focus {
    outline-color: #0265d2;
  }

  .focus\:outline-\[\#212121\]:focus {
    outline-color: #212121;
  }

  .focus\:outline-\[\#FF6C37\]:focus {
    outline-color: #ff6c37;
  }

  .focus\:outline-none:focus {
    --tw-outline-style: none;
    outline-style: none;
  }

  .focus-visible\:translate-x-2:focus-visible {
    --tw-translate-x: calc(var(--spacing) * 2);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .focus-visible\:border-blue-600\/20:focus-visible {
    border-color: rgba(21, 93, 252, .2);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .focus-visible\:border-blue-600\/20:focus-visible {
      border-color: color-mix(in oklab, var(--color-blue-600) 20%, transparent);
    }
  }

  .focus-visible\:border-emerald-600\/20:focus-visible {
    border-color: rgba(0, 151, 103, .2);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .focus-visible\:border-emerald-600\/20:focus-visible {
      border-color: color-mix(in oklab, var(--color-emerald-600) 20%, transparent);
    }
  }

  .focus-visible\:bg-blue-600\/20:focus-visible {
    background-color: rgba(21, 93, 252, .2);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .focus-visible\:bg-blue-600\/20:focus-visible {
      background-color: color-mix(in oklab, var(--color-blue-600) 20%, transparent);
    }
  }

  .focus-visible\:bg-emerald-600\/20:focus-visible {
    background-color: rgba(0, 151, 103, .2);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .focus-visible\:bg-emerald-600\/20:focus-visible {
      background-color: color-mix(in oklab, var(--color-emerald-600) 20%, transparent);
    }
  }

  .focus-visible\:ring:focus-visible {
    --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .focus-visible\:ring-2:focus-visible {
    --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .active\:no-underline:active {
    -webkit-text-decoration-line: none;
    text-decoration-line: none;
  }

  .disabled\:cursor-not-allowed:disabled {
    cursor: not-allowed;
  }

  .disabled\:border-transparent:disabled {
    border-color: rgba(0, 0, 0, 0);
  }

  .disabled\:bg-\[var\(--grey-20\)\]:disabled {
    background-color: var(--grey-20);
  }

  .disabled\:opacity-75:disabled {
    opacity: .75;
  }

  .data-\[state\=open\]\:bg-neutral-200[data-state="open"] {
    background-color: var(--color-neutral-200);
  }

  @media (min-width: 1400px) {
    .min-\[1400px\]\:flex-row {
      flex-direction: row;
    }
  }

  @media (min-width: 1400px) {
    .min-\[1400px\]\:items-center {
      align-items: center;
    }
  }

  @media (min-width: 1400px) {
    .min-\[1400px\]\:justify-start {
      justify-content: flex-start;
    }
  }

  @media (min-width: 40rem) {
    .sm\:visible {
      visibility: visible;
    }
  }

  @media (min-width: 40rem) {
    .sm\:sticky {
      position: -webkit-sticky;
      position: sticky;
    }
  }

  @media (min-width: 40rem) {
    .sm\:col-span-6 {
      grid-column: span 6 / span 6;
    }
  }

  @media (min-width: 40rem) {
    .sm\:mx-0 {
      margin-inline: calc(var(--spacing) * 0);
    }
  }

  @media (min-width: 40rem) {
    .sm\:mt-0 {
      margin-top: calc(var(--spacing) * 0);
    }
  }

  @media (min-width: 40rem) {
    .sm\:mt-5 {
      margin-top: calc(var(--spacing) * 5);
    }
  }

  @media (min-width: 40rem) {
    .sm\:mt-6 {
      margin-top: calc(var(--spacing) * 6);
    }
  }

  @media (min-width: 40rem) {
    .sm\:mt-10 {
      margin-top: calc(var(--spacing) * 10);
    }
  }

  @media (min-width: 40rem) {
    .sm\:mt-12 {
      margin-top: calc(var(--spacing) * 12);
    }
  }

  @media (min-width: 40rem) {
    .sm\:mt-\[64px\] {
      margin-top: 64px;
    }
  }

  @media (min-width: 40rem) {
    .sm\:mt-\[104px\] {
      margin-top: 104px;
    }
  }

  @media (min-width: 40rem) {
    .sm\:mt-\[120px\] {
      margin-top: 120px;
    }
  }

  @media (min-width: 40rem) {
    .sm\:mr-auto {
      margin-right: auto;
    }
  }

  @media (min-width: 40rem) {
    .sm\:mb-4 {
      margin-bottom: calc(var(--spacing) * 4);
    }
  }

  @media (min-width: 40rem) {
    .sm\:mb-11 {
      margin-bottom: calc(var(--spacing) * 11);
    }
  }

  @media (min-width: 40rem) {
    .sm\:\!block {
      display: block !important;
    }
  }

  @media (min-width: 40rem) {
    .sm\:block {
      display: block;
    }
  }

  @media (min-width: 40rem) {
    .sm\:flex {
      display: flex;
    }
  }

  @media (min-width: 40rem) {
    .sm\:hidden {
      display: none;
    }
  }

  @media (min-width: 40rem) {
    .sm\:inline-flex {
      display: inline-flex;
    }
  }

  @media (min-width: 40rem) {
    .sm\:size-6 {
      width: calc(var(--spacing) * 6);
      height: calc(var(--spacing) * 6);
    }
  }

  @media (min-width: 40rem) {
    .sm\:h-screen {
      height: 100vh;
    }
  }

  @media (min-width: 40rem) {
    .sm\:w-1\/2 {
      width: 50%;
    }
  }

  @media (min-width: 40rem) {
    .sm\:w-1\/3 {
      width: 33.3333%;
    }
  }

  @media (min-width: 40rem) {
    .sm\:w-80 {
      width: calc(var(--spacing) * 80);
    }
  }

  @media (min-width: 40rem) {
    .sm\:w-auto {
      width: auto;
    }
  }

  @media (min-width: 40rem) {
    .sm\:max-w-3xl {
      max-width: var(--container-3xl);
    }
  }

  @media (min-width: 40rem) {
    .sm\:max-w-\[392px\] {
      max-width: 392px;
    }
  }

  @media (min-width: 40rem) {
    .sm\:max-w-lg {
      max-width: var(--container-lg);
    }
  }

  @media (min-width: 40rem) {
    .sm\:max-w-md {
      max-width: var(--container-md);
    }
  }

  @media (min-width: 40rem) {
    .sm\:flex-shrink-0 {
      flex-shrink: 0;
    }
  }

  @media (min-width: 40rem) {
    .sm\:grid-cols-2 {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
  }

  @media (min-width: 40rem) {
    .sm\:flex-row {
      flex-direction: row;
    }
  }

  @media (min-width: 40rem) {
    .sm\:items-center {
      align-items: center;
    }
  }

  @media (min-width: 40rem) {
    .sm\:gap-7 {
      gap: calc(var(--spacing) * 7);
    }
  }

  @media (min-width: 40rem) {
    .sm\:gap-24 {
      gap: calc(var(--spacing) * 24);
    }
  }

  @media (min-width: 40rem) {
    :where(.sm\:space-x-2 > :not(:last-child)) {
      --tw-space-x-reverse: 0;
    }

    :where(.sm\:space-x-2 > :not(:last-child)):not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
      margin-left: calc(calc(var(--spacing) * 2) * var(--tw-space-x-reverse));
      margin-right: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-x-reverse)));
    }

    :where(.sm\:space-x-2 > :not(:last-child)):not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
      margin-left: calc(calc(var(--spacing) * 2) * var(--tw-space-x-reverse));
      margin-right: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-x-reverse)));
    }

    :where(.sm\:space-x-2 > :not(:last-child)):not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
      margin-left: calc(calc(var(--spacing) * 2) * var(--tw-space-x-reverse));
      margin-right: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-x-reverse)));
    }

    :where(.sm\:space-x-2 > :not(:last-child)):-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
      margin-right: calc(calc(var(--spacing) * 2) * var(--tw-space-x-reverse));
      margin-left: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-x-reverse)));
    }

    :where(.sm\:space-x-2 > :not(:last-child)):-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
      margin-right: calc(calc(var(--spacing) * 2) * var(--tw-space-x-reverse));
      margin-left: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-x-reverse)));
    }

    :where(.sm\:space-x-2 > :not(:last-child)):is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
      margin-right: calc(calc(var(--spacing) * 2) * var(--tw-space-x-reverse));
      margin-left: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-x-reverse)));
    }
  }

  @media (min-width: 40rem) {
    .sm\:rounded-xl {
      border-radius: var(--radius-xl);
    }
  }

  @media (min-width: 40rem) {
    .sm\:border {
      border-style: var(--tw-border-style);
      border-width: 1px;
    }
  }

  @media (min-width: 40rem) {
    .sm\:px-0 {
      padding-inline: calc(var(--spacing) * 0);
    }
  }

  @media (min-width: 40rem) {
    .sm\:px-6 {
      padding-inline: calc(var(--spacing) * 6);
    }
  }

  @media (min-width: 40rem) {
    .sm\:py-20 {
      padding-block: calc(var(--spacing) * 20);
    }
  }

  @media (min-width: 40rem) {
    .sm\:pt-24 {
      padding-top: calc(var(--spacing) * 24);
    }
  }

  @media (min-width: 40rem) {
    .sm\:pr-0 {
      padding-right: calc(var(--spacing) * 0);
    }
  }

  @media (min-width: 40rem) {
    .sm\:pb-0 {
      padding-bottom: calc(var(--spacing) * 0);
    }
  }

  @media (min-width: 40rem) {
    .sm\:pb-20 {
      padding-bottom: calc(var(--spacing) * 20);
    }
  }

  @media (min-width: 40rem) {
    .sm\:align-middle {
      vertical-align: middle;
    }
  }

  @media (min-width: 40rem) {
    .sm\:text-3xl {
      font-size: var(--text-3xl);
      line-height: var(--tw-leading, var(--text-3xl--line-height));
    }
  }

  @media (min-width: 40rem) {
    .sm\:text-4xl {
      font-size: var(--text-4xl);
      line-height: var(--tw-leading, var(--text-4xl--line-height));
    }
  }

  @media (min-width: 40rem) {
    .sm\:text-5xl {
      font-size: var(--text-5xl);
      line-height: var(--tw-leading, var(--text-5xl--line-height));
    }
  }

  @media (min-width: 40rem) {
    .sm\:text-base {
      font-size: var(--text-base);
      line-height: var(--tw-leading, var(--text-base--line-height));
    }
  }

  @media (min-width: 40rem) {
    .sm\:text-lg {
      font-size: var(--text-lg);
      line-height: var(--tw-leading, var(--text-lg--line-height));
    }
  }

  @media (min-width: 40rem) {
    .sm\:text-sm {
      font-size: var(--text-sm);
      line-height: var(--tw-leading, var(--text-sm--line-height));
    }
  }

  @media (min-width: 40rem) {
    .sm\:text-xl {
      font-size: var(--text-xl);
      line-height: var(--tw-leading, var(--text-xl--line-height));
    }
  }

  @media (min-width: 40rem) {
    .sm\:text-\[32px\] {
      font-size: 32px;
    }
  }

  @media (min-width: 40rem) {
    .sm\:text-\[40px\] {
      font-size: 40px;
    }
  }

  @media (min-width: 40rem) {
    .sm\:leading-130 {
      --tw-leading: calc(var(--spacing) * 130);
      line-height: calc(var(--spacing) * 130);
    }
  }

  @media (min-width: 40rem) {
    .sm\:focus-within\:w-\[26rem\]:focus-within {
      width: 26rem;
    }
  }

  @media (min-width: 40rem) {
    @media (hover: hover) {
      .sm\:hover\:scale-105:hover {
        --tw-scale-x: 105%;
        --tw-scale-y: 105%;
        --tw-scale-z: 105%;
        scale: var(--tw-scale-x) var(--tw-scale-y);
      }
    }
  }

  @media (min-width: 48rem) {
    .md\:col-span-4 {
      grid-column: span 4 / span 4;
    }
  }

  @media (min-width: 48rem) {
    .md\:col-span-7 {
      grid-column: span 7 / span 7;
    }
  }

  @media (min-width: 48rem) {
    .md\:col-span-8 {
      grid-column: span 8 / span 8;
    }
  }

  @media (min-width: 48rem) {
    .md\:col-start-9 {
      grid-column-start: 9;
    }
  }

  @media (min-width: 48rem) {
    .md\:mt-0 {
      margin-top: calc(var(--spacing) * 0);
    }
  }

  @media (min-width: 48rem) {
    .md\:mb-0 {
      margin-bottom: calc(var(--spacing) * 0);
    }
  }

  @media (min-width: 48rem) {
    .md\:mb-8 {
      margin-bottom: calc(var(--spacing) * 8);
    }
  }

  @media (min-width: 48rem) {
    .md\:flex {
      display: flex;
    }
  }

  @media (min-width: 48rem) {
    .md\:hidden {
      display: none;
    }
  }

  @media (min-width: 48rem) {
    .md\:w-\[400px\] {
      width: 400px;
    }
  }

  @media (min-width: 48rem) {
    .md\:flex-none {
      flex: none;
    }
  }

  @media (min-width: 48rem) {
    .md\:grid-cols-2 {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
  }

  @media (min-width: 48rem) {
    .md\:grid-cols-3 {
      grid-template-columns: repeat(3, minmax(0, 1fr));
    }
  }

  @media (min-width: 48rem) {
    .md\:flex-col {
      flex-direction: column;
    }
  }

  @media (min-width: 48rem) {
    .md\:flex-row {
      flex-direction: row;
    }
  }

  @media (min-width: 48rem) {
    .md\:justify-end {
      justify-content: flex-end;
    }
  }

  @media (min-width: 48rem) {
    .md\:justify-start {
      justify-content: flex-start;
    }
  }

  @media (min-width: 48rem) {
    .md\:gap-0 {
      gap: calc(var(--spacing) * 0);
    }
  }

  @media (min-width: 48rem) {
    .md\:gap-2 {
      gap: calc(var(--spacing) * 2);
    }
  }

  @media (min-width: 48rem) {
    .md\:gap-8 {
      gap: calc(var(--spacing) * 8);
    }
  }

  @media (min-width: 48rem) {
    .md\:pr-5 {
      padding-right: calc(var(--spacing) * 5);
    }
  }

  @media (min-width: 64rem) {
    .lg\:absolute {
      position: absolute;
    }
  }

  @media (min-width: 64rem) {
    .lg\:sticky {
      position: -webkit-sticky;
      position: sticky;
    }
  }

  @media (min-width: 64rem) {
    .lg\:order-1 {
      order: 1;
    }
  }

  @media (min-width: 64rem) {
    .lg\:order-first {
      order: -9999;
    }
  }

  @media (min-width: 64rem) {
    .lg\:col-span-2 {
      grid-column: span 2 / span 2;
    }
  }

  @media (min-width: 64rem) {
    .lg\:col-span-3 {
      grid-column: span 3 / span 3;
    }
  }

  @media (min-width: 64rem) {
    .lg\:col-span-7 {
      grid-column: span 7 / span 7;
    }
  }

  @media (min-width: 64rem) {
    .lg\:my-0 {
      margin-block: calc(var(--spacing) * 0);
    }
  }

  @media (min-width: 64rem) {
    .lg\:mt-0 {
      margin-top: calc(var(--spacing) * 0);
    }
  }

  @media (min-width: 64rem) {
    .lg\:mt-8 {
      margin-top: calc(var(--spacing) * 8);
    }
  }

  @media (min-width: 64rem) {
    .lg\:mt-32 {
      margin-top: calc(var(--spacing) * 32);
    }
  }

  @media (min-width: 64rem) {
    .lg\:mb-0 {
      margin-bottom: calc(var(--spacing) * 0);
    }
  }

  @media (min-width: 64rem) {
    .lg\:mb-2\.5 {
      margin-bottom: calc(var(--spacing) * 2.5);
    }
  }

  @media (min-width: 64rem) {
    .lg\:-ml-12 {
      margin-left: calc(var(--spacing) * -12);
    }
  }

  @media (min-width: 64rem) {
    .lg\:block {
      display: block;
    }
  }

  @media (min-width: 64rem) {
    .lg\:flex {
      display: flex;
    }
  }

  @media (min-width: 64rem) {
    .lg\:hidden {
      display: none;
    }
  }

  @media (min-width: 64rem) {
    .lg\:inline-block {
      display: inline-block;
    }
  }

  @media (min-width: 64rem) {
    .lg\:h-\[500px\] {
      height: 500px;
    }
  }

  @media (min-width: 64rem) {
    .lg\:h-auto {
      height: auto;
    }
  }

  @media (min-width: 64rem) {
    .lg\:w-3\/4 {
      width: 75%;
    }
  }

  @media (min-width: 64rem) {
    .lg\:w-\[180px\] {
      width: 180px;
    }
  }

  @media (min-width: 64rem) {
    .lg\:w-\[220px\] {
      width: 220px;
    }
  }

  @media (min-width: 64rem) {
    .lg\:w-\[500px\] {
      width: 500px;
    }
  }

  @media (min-width: 64rem) {
    .lg\:w-full {
      width: 100%;
    }
  }

  @media (min-width: 64rem) {
    .lg\:w-max {
      width: max-content;
    }
  }

  @media (min-width: 64rem) {
    .lg\:grid-cols-2 {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
  }

  @media (min-width: 64rem) {
    .lg\:grid-cols-3 {
      grid-template-columns: repeat(3, minmax(0, 1fr));
    }
  }

  @media (min-width: 64rem) {
    .lg\:grid-cols-4 {
      grid-template-columns: repeat(4, minmax(0, 1fr));
    }
  }

  @media (min-width: 64rem) {
    .lg\:grid-cols-6 {
      grid-template-columns: repeat(6, minmax(0, 1fr));
    }
  }

  @media (min-width: 64rem) {
    .lg\:grid-rows-2 {
      grid-template-rows: repeat(2, minmax(0, 1fr));
    }
  }

  @media (min-width: 64rem) {
    .lg\:flex-col {
      flex-direction: column;
    }
  }

  @media (min-width: 64rem) {
    .lg\:flex-row {
      flex-direction: row;
    }
  }

  @media (min-width: 64rem) {
    .lg\:flex-row-reverse {
      flex-direction: row-reverse;
    }
  }

  @media (min-width: 64rem) {
    .lg\:justify-end {
      justify-content: flex-end;
    }
  }

  @media (min-width: 64rem) {
    .lg\:justify-start {
      justify-content: flex-start;
    }
  }

  @media (min-width: 64rem) {
    .lg\:gap-0 {
      gap: calc(var(--spacing) * 0);
    }
  }

  @media (min-width: 64rem) {
    .lg\:gap-4 {
      gap: calc(var(--spacing) * 4);
    }
  }

  @media (min-width: 64rem) {
    .lg\:gap-16 {
      gap: calc(var(--spacing) * 16);
    }
  }

  @media (min-width: 64rem) {
    .lg\:gap-36 {
      gap: calc(var(--spacing) * 36);
    }
  }

  @media (min-width: 64rem) {
    .lg\:gap-\[20px\] {
      gap: 20px;
    }
  }

  @media (min-width: 64rem) {
    .lg\:rounded-\[10px\] {
      border-radius: 10px;
    }
  }

  @media (min-width: 64rem) {
    .lg\:border-\[1px\] {
      border-style: var(--tw-border-style);
      border-width: 1px;
    }
  }

  @media (min-width: 64rem) {
    .lg\:border-r {
      border-right-style: var(--tw-border-style);
      border-right-width: 1px;
    }
  }

  @media (min-width: 64rem) {
    .lg\:border-b-0 {
      border-bottom-style: var(--tw-border-style);
      border-bottom-width: 0;
    }
  }

  @media (min-width: 64rem) {
    .lg\:border-none {
      --tw-border-style: none;
      border-style: none;
    }
  }

  @media (min-width: 64rem) {
    .lg\:p-12 {
      padding: calc(var(--spacing) * 12);
    }
  }

  @media (min-width: 64rem) {
    .lg\:p-\[20px\] {
      padding: 20px;
    }
  }

  @media (min-width: 64rem) {
    .lg\:px-8 {
      padding-inline: calc(var(--spacing) * 8);
    }
  }

  @media (min-width: 64rem) {
    .lg\:px-12 {
      padding-inline: calc(var(--spacing) * 12);
    }
  }

  @media (min-width: 64rem) {
    .lg\:px-\[15px\] {
      padding-left: 15px;
      padding-right: 15px;
    }
  }

  @media (min-width: 64rem) {
    .lg\:py-0 {
      padding-block: calc(var(--spacing) * 0);
    }
  }

  @media (min-width: 64rem) {
    .lg\:py-18 {
      padding-block: calc(var(--spacing) * 18);
    }
  }

  @media (min-width: 64rem) {
    .lg\:py-24 {
      padding-block: calc(var(--spacing) * 24);
    }
  }

  @media (min-width: 64rem) {
    .lg\:pt-3 {
      padding-top: calc(var(--spacing) * 3);
    }
  }

  @media (min-width: 64rem) {
    .lg\:pt-10 {
      padding-top: calc(var(--spacing) * 10);
    }
  }

  @media (min-width: 64rem) {
    .lg\:pt-32 {
      padding-top: calc(var(--spacing) * 32);
    }
  }

  @media (min-width: 64rem) {
    .lg\:pb-0 {
      padding-bottom: calc(var(--spacing) * 0);
    }
  }

  @media (min-width: 64rem) {
    .lg\:pb-28 {
      padding-bottom: calc(var(--spacing) * 28);
    }
  }

  @media (min-width: 64rem) {
    .lg\:pl-0 {
      padding-left: calc(var(--spacing) * 0);
    }
  }

  @media (min-width: 64rem) {
    .lg\:pl-8 {
      padding-left: calc(var(--spacing) * 8);
    }
  }

  @media (min-width: 64rem) {
    .lg\:pl-\[23\.7rem\] {
      padding-left: 23.7rem;
    }
  }

  @media (min-width: 64rem) {
    .lg\:text-left {
      text-align: left;
    }
  }

  @media (min-width: 64rem) {
    .lg\:text-right {
      text-align: right;
    }
  }

  @media (min-width: 64rem) {
    .lg\:text-3xl {
      font-size: var(--text-3xl);
      line-height: var(--tw-leading, var(--text-3xl--line-height));
    }
  }

  @media (min-width: 64rem) {
    .lg\:text-5xl {
      font-size: var(--text-5xl);
      line-height: var(--tw-leading, var(--text-5xl--line-height));
    }
  }

  @media (min-width: 64rem) {
    .lg\:text-6xl {
      font-size: var(--text-6xl);
      line-height: var(--tw-leading, var(--text-6xl--line-height));
    }
  }

  @media (min-width: 64rem) {
    .lg\:text-base {
      font-size: var(--text-base);
      line-height: var(--tw-leading, var(--text-base--line-height));
    }
  }

  @media (min-width: 64rem) {
    .lg\:text-sm {
      font-size: var(--text-sm);
      line-height: var(--tw-leading, var(--text-sm--line-height));
    }
  }

  @media (min-width: 64rem) {
    .lg\:text-\[40px\] {
      font-size: 40px;
    }
  }

  @media (min-width: 64rem) {
    .lg\:text-\[56px\] {
      font-size: 56px;
    }
  }

  @media (min-width: 64rem) {
    .lg\:leading-6 {
      --tw-leading: calc(var(--spacing) * 6);
      line-height: calc(var(--spacing) * 6);
    }
  }

  @media (min-width: 64rem) {
    .lg\:shadow-\[0_2px_8px_0_rgba\(0\,0\,0\,0\.20\)\] {
      --tw-shadow: 0 2px 8px 0 var(--tw-shadow-color, rgba(0, 0, 0, .2));
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }

  @media (min-width: 64rem) {
    .lg\:\[--scroll-mt\:9\.5rem\] {
      --scroll-mt: 9.5rem;
    }
  }

  @media (min-width: 64rem) {
    .lg\:\[--scroll-mt\:12rem\] {
      --scroll-mt: 12rem;
    }
  }

  @media (min-width: 64rem) {
    .peer-\[\.is-not-custom\]\:lg\:px-8:is(:where(.peer).is-not-custom ~ *) {
      padding-inline: calc(var(--spacing) * 8);
    }
  }

  @media (min-width: 80rem) {
    .xl\:relative {
      position: relative;
    }
  }

  @media (min-width: 80rem) {
    .xl\:flex {
      display: flex;
    }
  }

  @media (min-width: 80rem) {
    .xl\:w-\[calc\(100\%-28rem\)\] {
      width: calc(100% - 28rem);
    }
  }

  @media (min-width: 80rem) {
    .xl\:grid-cols-4 {
      grid-template-columns: repeat(4, minmax(0, 1fr));
    }
  }

  @media (min-width: 80rem) {
    .xl\:flex-col {
      flex-direction: column;
    }
  }

  @media (min-width: 80rem) {
    .xl\:justify-between {
      justify-content: space-between;
    }
  }

  @media (min-width: 80rem) {
    .xl\:gap-6 {
      gap: calc(var(--spacing) * 6);
    }
  }

  @media (min-width: 80rem) {
    .xl\:px-0 {
      padding-inline: calc(var(--spacing) * 0);
    }
  }

  @container footer (width >= 390px) {
    .\@\[390px\]\/footer\:flex-row {
      flex-direction: row;
    }
  }

  @container footer (width >= 390px) {
    .\@\[390px\]\/footer\:justify-start {
      justify-content: flex-start;
    }
  }

  @container footer (width >= 460px) {
    .\@\[460px\]\/footer\:flex-row {
      flex-direction: row;
    }
  }

  @container footer (width >= 460px) {
    .\@\[460px\]\/footer\:justify-start {
      justify-content: flex-start;
    }
  }

  @container environment-url (width >= 500px) {
    .\@\[500px\]\/environment-url\:inline-block {
      display: inline-block;
    }
  }

  @container bottom-banner-qr-code (width >= 546px) {
    .\@\[546px\]\/bottom-banner-qr-code\:flex {
      display: flex;
    }
  }

  @container bottom-banner-qr-code (width >= 546px) {
    .\@\[546px\]\/bottom-banner-qr-code\:px-\[88px\] {
      padding-left: 88px;
      padding-right: 88px;
    }
  }

  @container bottom-banner-qr-code (width >= 546px) {
    .\@\[546px\]\/bottom-banner-qr-code\:py-0 {
      padding-block: calc(var(--spacing) * 0);
    }
  }

  @container bottom-banner-qr-code (width >= 800px) {
    .\@\[800px\]\/bottom-banner-qr-code\:text-2xl {
      font-size: var(--text-2xl);
      line-height: var(--tw-leading, var(--text-2xl--line-height));
    }
  }

  @container http-api-content (width >= 850px) {
    .\@\[850px\]\/http-api-content\:fixed {
      position: fixed;
    }
  }

  @container http-api-content (width >= 850px) {
    .\@\[850px\]\/http-api-content\:bottom-0 {
      bottom: calc(var(--spacing) * 0);
    }
  }

  @container http-api-content (width >= 850px) {
    .\@\[850px\]\/http-api-content\:mr-\[-440px\] {
      margin-right: -440px;
    }
  }

  @container http-api-content (width >= 850px) {
    .\@\[850px\]\/http-api-content\:mb-0 {
      margin-bottom: calc(var(--spacing) * 0);
    }
  }

  @container http-api-content (width >= 850px) {
    .\@\[850px\]\/http-api-content\:block {
      display: block;
    }
  }

  @container http-api-content (width >= 850px) {
    .\@\[850px\]\/http-api-content\:flex {
      display: flex;
    }
  }

  @container http-api-content (width >= 850px) {
    .\@\[850px\]\/http-api-content\:hidden {
      display: none;
    }
  }

  @container http-api-content (width >= 850px) {
    .\@\[850px\]\/http-api-content\:h-0 {
      height: calc(var(--spacing) * 0);
    }
  }

  @container http-api-content (width >= 850px) {
    .\@\[850px\]\/http-api-content\:max-h-\[unset\] {
      max-height: unset;
    }
  }

  @container http-api-content (width >= 850px) {
    .\@\[850px\]\/http-api-content\:max-h-full {
      max-height: 100%;
    }
  }

  @container http-api-content (width >= 850px) {
    .\@\[850px\]\/http-api-content\:min-h-\[170px\] {
      min-height: 170px;
    }
  }

  @container http-api-content (width >= 850px) {
    .\@\[850px\]\/http-api-content\:w-\[440px\] {
      width: 440px;
    }
  }

  @container http-api-content (width >= 850px) {
    .\@\[850px\]\/http-api-content\:shrink {
      flex-shrink: 1;
    }
  }

  @container http-api-content (width >= 850px) {
    .\@\[850px\]\/http-api-content\:justify-end {
      justify-content: flex-end;
    }
  }

  @container http-api-content (width >= 850px) {
    .\@\[850px\]\/http-api-content\:pr-\[440px\] {
      padding-right: 440px;
    }
  }

  @container http-api-content (width >= 850px) {
    .\@\[850px\]\/http-api-content\:pb-5 {
      padding-bottom: calc(var(--spacing) * 5);
    }
  }

  @container http-api-content (width >= 850px) {
    .\@\[850px\]\/http-api-content\:pl-5 {
      padding-left: calc(var(--spacing) * 5);
    }
  }

  @container http-api-content (width >= 850px) {
    .\@\[850px\]\/http-api-content\:pl-10 {
      padding-left: calc(var(--spacing) * 10);
    }
  }

  @container http-api-content (width >= 850px) {
    .\@\[850px\]\/http-api-content\:group-\[\.old-safari\]\/root\:mr-0:is(:where(.group\/root).old-safari *) {
      margin-right: calc(var(--spacing) * 0);
    }
  }

  @container http-api-content (width >= 850px) {
    .\@\[850px\]\/http-api-content\:group-\[\.old-safari\]\/root\:hidden:is(:where(.group\/root).old-safari *) {
      display: none;
    }
  }

  @container http-api-content (width >= 850px) {
    .\@\[850px\]\/http-api-content\:group-\[\.old-safari\]\/root\:pr-5:is(:where(.group\/root).old-safari *) {
      padding-right: calc(var(--spacing) * 5);
    }
  }

  @container http-api-content (width >= 850px) {
    .\@\[850px\]\/http-api-content\:group-\[\.old-safari\]\/root\:pl-0:is(:where(.group\/root).old-safari *) {
      padding-left: calc(var(--spacing) * 0);
    }
  }

  @container request-raw-card (width >= 880px) {
    .\@\[880px\]\/request-raw-card\:\!flex {
      display: flex !important;
    }
  }

  @container request-raw-card (width >= 880px) {
    .\@\[880px\]\/request-raw-card\:block {
      display: block;
    }
  }

  @container request-schema-card (width >= 880px) {
    .\@\[880px\]\/request-schema-card\:\!flex {
      display: flex !important;
    }
  }

  @container request-schema-card (width >= 880px) {
    .\@\[880px\]\/request-schema-card\:block {
      display: block;
    }
  }

  @container response-panel (width >= 880px) {
    .\@\[880px\]\/response-panel\:\!flex {
      display: flex !important;
    }
  }

  @container response-panel (width >= 880px) {
    .\@\[880px\]\/response-panel\:block {
      display: block;
    }
  }

  @container request-raw-card (width >= 880px) {
    .\@\[880px\]\/request-raw-card\:border-t-0 {
      border-top-style: var(--tw-border-style);
      border-top-width: 0;
    }
  }

  @container request-schema-card (width >= 880px) {
    .\@\[880px\]\/request-schema-card\:border-t-0 {
      border-top-style: var(--tw-border-style);
      border-top-width: 0;
    }
  }

  @container response-panel (width >= 880px) {
    .\@\[880px\]\/response-panel\:border-t-0 {
      border-top-style: var(--tw-border-style);
      border-top-width: 0;
    }
  }

  @container response-panel (width >= 880px) {
    .\@\[880px\]\/response-panel\:pb-4 {
      padding-bottom: calc(var(--spacing) * 4);
    }
  }

  @container doc-content (width >= 960px) {
    .\@\[960px\]\/doc-content\:block {
      display: block;
    }
  }

  @container environment-url (width >= 998px) {
    .\@\[998px\]\/environment-url\:flex {
      display: flex;
    }
  }

  @container environment-url (width >= 998px) {
    .\@\[998px\]\/environment-url\:h-auto {
      height: auto;
    }
  }

  @container http-api-content (width >= 1000px) {
    .\@\[1000px\]\/http-api-content\:hidden {
      display: none;
    }
  }

  @container http-api-content (width >= 1440px) {
    .\@\[1440px\]\/http-api-content\:hidden {
      display: none;
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:block {
      display: block;
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:hidden {
      display: none;
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:border-gray-300\/\[0\.06\] {
      border-color: rgba(209, 213, 220, .06);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:border-gray-300\/\[0\.06\] {
        border-color: color-mix(in oklab, var(--color-gray-300) 6%, transparent);
      }
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:border-gray-600\/30 {
      border-color: rgba(74, 85, 101, .3);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:border-gray-600\/30 {
        border-color: color-mix(in oklab, var(--color-gray-600) 30%, transparent);
      }
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:border-gray-800\/50 {
      border-color: rgba(30, 41, 57, .5);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:border-gray-800\/50 {
        border-color: color-mix(in oklab, var(--color-gray-800) 50%, transparent);
      }
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:border-white\/20 {
      border-color: rgba(255, 255, 255, .2);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:border-white\/20 {
        border-color: color-mix(in oklab, var(--color-white) 20%, transparent);
      }
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:border-b-\[\#ffffff0f\] {
      border-bottom-color: rgba(255, 255, 255, .06);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:bg-\[\#191a23f2\] {
      background-color: rgba(25, 26, 35, .95);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:bg-gray-400 {
      background-color: var(--color-gray-400);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:bg-gray-500 {
      background-color: var(--color-gray-500);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:bg-transparent {
      background-color: rgba(0, 0, 0, 0);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:fill-gray-400 {
      fill: var(--color-gray-400);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:text-gray-100 {
      color: var(--color-gray-100);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:text-gray-200 {
      color: var(--color-gray-200);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:text-gray-300 {
      color: var(--color-gray-300);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:text-gray-400 {
      color: var(--color-gray-400);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:text-gray-600 {
      color: var(--color-gray-600);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:text-slate-200 {
      color: var(--color-slate-200);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:text-white {
      color: var(--color-white);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:text-white\/50 {
      color: rgba(255, 255, 255, .5);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:text-white\/50 {
        color: color-mix(in oklab, var(--color-white) 50%, transparent);
      }
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:placeholder-gray-400::placeholder {
      color: var(--color-gray-400);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:opacity-100 {
      opacity: 1;
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:ring-1 {
      --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:ring-gray-600\/30 {
      --tw-ring-color: rgba(74, 85, 101, .3);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:ring-gray-600\/30 {
        --tw-ring-color: color-mix(in oklab, var(--color-gray-600) 30%, transparent);
      }
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:ring-gray-700\/40 {
      --tw-ring-color: rgba(54, 65, 83, .4);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:ring-gray-700\/40 {
        --tw-ring-color: color-mix(in oklab, var(--color-gray-700) 40%, transparent);
      }
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:brightness-\[1\.1\] {
      --tw-brightness: brightness(1.1);
      filter: var(--tw-blur, ) var(--tw-brightness, ) var(--tw-contrast, ) var(--tw-grayscale, ) var(--tw-hue-rotate, ) var(--tw-invert, ) var(--tw-saturate, ) var(--tw-sepia, ) var(--tw-drop-shadow, );
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:brightness-\[1\.35\] {
      --tw-brightness: brightness(1.35);
      filter: var(--tw-blur, ) var(--tw-brightness, ) var(--tw-contrast, ) var(--tw-grayscale, ) var(--tw-hue-rotate, ) var(--tw-invert, ) var(--tw-saturate, ) var(--tw-sepia, ) var(--tw-drop-shadow, );
    }
  }

  @media (prefers-color-scheme: dark) {
    @media (hover: hover) {
      .dark\:group-hover\:fill-gray-200:is(:where(.group):hover *) {
        fill: var(--color-gray-200);
      }
    }
  }

  @media (prefers-color-scheme: dark) {
    @media (hover: hover) {
      .dark\:group-hover\:text-gray-300:is(:where(.group):hover *) {
        color: var(--color-gray-300);
      }
    }
  }

  @media (prefers-color-scheme: dark) {
    @media (hover: hover) {
      .dark\:group-hover\:text-gray-400:is(:where(.group):hover *) {
        color: var(--color-gray-400);
      }
    }
  }

  @media (hover: hover) {
    @media (prefers-color-scheme: dark) {
      .hover\:dark\:border-gray-500:hover {
        border-color: var(--color-gray-500);
      }
    }
  }

  @media (prefers-color-scheme: dark) {
    @media (hover: hover) {
      .dark\:hover\:bg-gray-200\/5:hover {
        background-color: rgba(229, 231, 235, .05);
      }

      @supports (color: color-mix(in lab, red, red)) {
        .dark\:hover\:bg-gray-200\/5:hover {
          background-color: color-mix(in oklab, var(--color-gray-200) 5%, transparent);
        }
      }
    }
  }

  @media (prefers-color-scheme: dark) {
    @media (hover: hover) {
      .dark\:hover\:bg-gray-300:hover {
        background-color: var(--color-gray-300);
      }
    }
  }

  @media (prefers-color-scheme: dark) {
    @media (hover: hover) {
      .dark\:hover\:bg-gray-400:hover {
        background-color: var(--color-gray-400);
      }
    }
  }

  @media (prefers-color-scheme: dark) {
    @media (hover: hover) {
      .dark\:hover\:bg-white\/10:hover {
        background-color: rgba(255, 255, 255, .1);
      }

      @supports (color: color-mix(in lab, red, red)) {
        .dark\:hover\:bg-white\/10:hover {
          background-color: color-mix(in oklab, var(--color-white) 10%, transparent);
        }
      }
    }
  }

  @media (prefers-color-scheme: dark) {
    @media (hover: hover) {
      .dark\:hover\:bg-white\/\[0\.07\]:hover {
        background-color: rgba(255, 255, 255, .07);
      }

      @supports (color: color-mix(in lab, red, red)) {
        .dark\:hover\:bg-white\/\[0\.07\]:hover {
          background-color: color-mix(in oklab, var(--color-white) 7.0%, transparent);
        }
      }
    }
  }

  @media (prefers-color-scheme: dark) {
    @media (hover: hover) {
      .dark\:hover\:text-gray-300:hover {
        color: var(--color-gray-300);
      }
    }
  }

  @media (hover: hover) {
    @media (prefers-color-scheme: dark) {
      .hover\:dark\:text-gray-200:hover {
        color: var(--color-gray-200);
      }
    }
  }

  @media (prefers-color-scheme: dark) {
    @media (hover: hover) {
      .dark\:hover\:ring-gray-500\/30:hover {
        --tw-ring-color: rgba(106, 114, 130, .3);
      }

      @supports (color: color-mix(in lab, red, red)) {
        .dark\:hover\:ring-gray-500\/30:hover {
          --tw-ring-color: color-mix(in oklab, var(--color-gray-500) 30%, transparent);
        }
      }
    }
  }

  @media (prefers-color-scheme: dark) {
    @media (hover: hover) {
      .dark\:hover\:brightness-150:hover {
        --tw-brightness: brightness(150%);
        filter: var(--tw-blur, ) var(--tw-brightness, ) var(--tw-contrast, ) var(--tw-grayscale, ) var(--tw-hue-rotate, ) var(--tw-invert, ) var(--tw-saturate, ) var(--tw-sepia, ) var(--tw-drop-shadow, );
      }
    }
  }

  @media (prefers-color-scheme: dark) {
    @media (hover: hover) {
      .dark\:hover\:brightness-\[1\.25\]:hover {
        --tw-brightness: brightness(1.25);
        filter: var(--tw-blur, ) var(--tw-brightness, ) var(--tw-contrast, ) var(--tw-grayscale, ) var(--tw-hue-rotate, ) var(--tw-invert, ) var(--tw-saturate, ) var(--tw-sepia, ) var(--tw-drop-shadow, );
      }
    }
  }

  @media (prefers-color-scheme: dark) {
    .data-\[is-opaque\=false\]\:dark\:bg-transparent[data-is-opaque="false"] {
      background-color: rgba(0, 0, 0, 0);
    }
  }
}

:root {
  --background: #fff;
  --foreground: #171717;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, SimSun, sans-serif;
}

html {
  scroll-behavior: smooth;
}

::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

.gradient-bg {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.gradient-text {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-text-fill-color: transparent;
  -webkit-background-clip: text;
  background-clip: text;
}

.fade-in {
  animation: .6s ease-in-out fadeIn;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-up {
  animation: .8s ease-out slideUp;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(40px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@media (max-width: 768px) {
  .mobile-padding {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .mobile-text-center {
    text-align: center;
  }

  .mobile-hidden {
    display: none;
  }
}

@media (hover: none) and (pointer: coarse) {
  button, a {
    min-width: 44px;
    min-height: 44px;
  }
}

@media (prefers-reduced-motion: reduce) {
  .fade-in, .slide-up {
    animation: none;
  }

  * {
    transition-duration: .01ms !important;
    animation-duration: .01ms !important;
    animation-iteration-count: 1 !important;
  }
}

@property --tw-translate-x {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-translate-y {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-translate-z {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-scale-x {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}

@property --tw-scale-y {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}

@property --tw-scale-z {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}

@property --tw-rotate-x {
  syntax: "*";
  inherits: false
}

@property --tw-rotate-y {
  syntax: "*";
  inherits: false
}

@property --tw-rotate-z {
  syntax: "*";
  inherits: false
}

@property --tw-skew-x {
  syntax: "*";
  inherits: false
}

@property --tw-skew-y {
  syntax: "*";
  inherits: false
}

@property --tw-space-y-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-space-x-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-divide-x-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-border-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}

@property --tw-divide-y-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-gradient-position {
  syntax: "*";
  inherits: false
}

@property --tw-gradient-from {
  syntax: "<color>";
  inherits: false;
  initial-value: rgba(0, 0, 0, 0);
}

@property --tw-gradient-via {
  syntax: "<color>";
  inherits: false;
  initial-value: rgba(0, 0, 0, 0);
}

@property --tw-gradient-to {
  syntax: "<color>";
  inherits: false;
  initial-value: rgba(0, 0, 0, 0);
}

@property --tw-gradient-stops {
  syntax: "*";
  inherits: false
}

@property --tw-gradient-via-stops {
  syntax: "*";
  inherits: false
}

@property --tw-gradient-from-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 0%;
}

@property --tw-gradient-via-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 50%;
}

@property --tw-gradient-to-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 100%;
}

@property --tw-leading {
  syntax: "*";
  inherits: false
}

@property --tw-font-weight {
  syntax: "*";
  inherits: false
}

@property --tw-tracking {
  syntax: "*";
  inherits: false
}

@property --tw-ordinal {
  syntax: "*";
  inherits: false
}

@property --tw-slashed-zero {
  syntax: "*";
  inherits: false
}

@property --tw-numeric-figure {
  syntax: "*";
  inherits: false
}

@property --tw-numeric-spacing {
  syntax: "*";
  inherits: false
}

@property --tw-numeric-fraction {
  syntax: "*";
  inherits: false
}

@property --tw-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 rgba(0, 0, 0, 0);
}

@property --tw-shadow-color {
  syntax: "*";
  inherits: false
}

@property --tw-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}

@property --tw-inset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 rgba(0, 0, 0, 0);
}

@property --tw-inset-shadow-color {
  syntax: "*";
  inherits: false
}

@property --tw-inset-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}

@property --tw-ring-color {
  syntax: "*";
  inherits: false
}

@property --tw-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 rgba(0, 0, 0, 0);
}

@property --tw-inset-ring-color {
  syntax: "*";
  inherits: false
}

@property --tw-inset-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 rgba(0, 0, 0, 0);
}

@property --tw-ring-inset {
  syntax: "*";
  inherits: false
}

@property --tw-ring-offset-width {
  syntax: "<length>";
  inherits: false;
  initial-value: 0;
}

@property --tw-ring-offset-color {
  syntax: "*";
  inherits: false;
  initial-value: #fff;
}

@property --tw-ring-offset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 rgba(0, 0, 0, 0);
}

@property --tw-outline-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}

@property --tw-blur {
  syntax: "*";
  inherits: false
}

@property --tw-brightness {
  syntax: "*";
  inherits: false
}

@property --tw-contrast {
  syntax: "*";
  inherits: false
}

@property --tw-grayscale {
  syntax: "*";
  inherits: false
}

@property --tw-hue-rotate {
  syntax: "*";
  inherits: false
}

@property --tw-invert {
  syntax: "*";
  inherits: false
}

@property --tw-opacity {
  syntax: "*";
  inherits: false
}

@property --tw-saturate {
  syntax: "*";
  inherits: false
}

@property --tw-sepia {
  syntax: "*";
  inherits: false
}

@property --tw-drop-shadow {
  syntax: "*";
  inherits: false
}

@property --tw-drop-shadow-color {
  syntax: "*";
  inherits: false
}

@property --tw-drop-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}

@property --tw-drop-shadow-size {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-blur {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-brightness {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-contrast {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-grayscale {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-hue-rotate {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-invert {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-opacity {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-saturate {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-sepia {
  syntax: "*";
  inherits: false
}

@property --tw-duration {
  syntax: "*";
  inherits: false
}

@property --tw-ease {
  syntax: "*";
  inherits: false
}

@property --tw-content {
  syntax: "*";
  inherits: false;
  initial-value: "";
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

@keyframes pulse {
  50% {
    opacity: .5;
  }
}

/*# sourceMappingURL=%5Broot-of-the-server%5D__805f2d81._.css.map*/