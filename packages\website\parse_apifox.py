import re
from pathlib import Path

text = Path('apifox_help.html').read_text(encoding='utf-8', errors='ignore')
pattern = re.compile(r'textTitle\\":\\"(.*?)\\".*?textDescription\\":\[(.*?)\]', re.DOTALL)
results = []
for title, body in pattern.findall(text):
    body_html = body.replace('\\u003c', '<').replace('\\u003e', '>').replace('\\u0026', '&')
    paragraphs = []
    for match in re.findall(r'<p>(.*?)</p>', body_html):
        clean = re.sub('<.*?>', '', match)
        clean = clean.replace('\\', '').strip()
        if clean:
            paragraphs.append(clean)
    if paragraphs:
        results.append((title, paragraphs))

seen = set()
filtered = []
for title, paragraphs in results:
    key = (title, tuple(paragraphs))
    if key not in seen:
        seen.add(key)
        filtered.append((title, paragraphs))

for title, paragraphs in filtered:
    print(f"TITLE: {title}")
    for p in paragraphs:
        print(f"- {p}")
    print()
