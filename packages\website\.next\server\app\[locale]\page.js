var R=require("../../chunks/ssr/[turbopack]_runtime.js")("server/app/[locale]/page.js")
R.c("server/chunks/ssr/node_modules_5f2d4120._.js")
R.c("server/chunks/ssr/[root-of-the-server]__e8a2741f._.js")
R.c("server/chunks/ssr/packages_website_src_app_bed562f3._.js")
R.c("server/chunks/ssr/[root-of-the-server]__5b6260aa._.js")
R.c("server/chunks/ssr/node_modules_next_dist_client_components_9774470f._.js")
R.c("server/chunks/ssr/node_modules_next_dist_client_components_builtin_forbidden_45780354.js")
R.c("server/chunks/ssr/node_modules_next_dist_client_components_builtin_unauthorized_15817684.js")
R.c("server/chunks/ssr/node_modules_next_dist_client_components_builtin_global-error_ece394eb.js")
R.c("server/chunks/ssr/packages_website_messages_99a873d2._.js")
R.c("server/chunks/ssr/node_modules_447228df._.js")
R.c("server/chunks/ssr/packages_website_src_ada23aba._.js")
R.c("server/chunks/ssr/packages_website_src_app_[locale]_loading_tsx_6cbb64ce._.js")
R.c("server/chunks/ssr/node_modules_609b3c9d._.js")
R.c("server/chunks/ssr/[root-of-the-server]__6156c221._.js")
R.m("[project]/packages/website/.next-internal/server/app/[locale]/page/actions.js [app-rsc] (server actions loader, ecmascript)")
R.m("[project]/node_modules/next/dist/esm/build/templates/app-page.js?page=/[locale]/page { GLOBAL_ERROR_MODULE => \"[project]/node_modules/next/dist/client/components/builtin/global-error.js [app-rsc] (ecmascript, Next.js Server Component)\", METADATA_0 => \"[project]/packages/website/src/app/favicon.ico.mjs { IMAGE => \\\"[project]/packages/website/src/app/favicon.ico (static in ecmascript)\\\" } [app-rsc] (structured image object, ecmascript, Next.js Server Component)\", MODULE_1 => \"[project]/packages/website/src/app/layout.tsx [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_2 => \"[project]/node_modules/next/dist/client/components/builtin/not-found.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_3 => \"[project]/node_modules/next/dist/client/components/builtin/forbidden.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_4 => \"[project]/node_modules/next/dist/client/components/builtin/unauthorized.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_5 => \"[project]/node_modules/next/dist/client/components/builtin/global-error.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_6 => \"[project]/packages/website/src/app/[locale]/layout.tsx [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_7 => \"[project]/packages/website/src/app/[locale]/loading.tsx [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_8 => \"[project]/packages/website/src/app/[locale]/page.tsx [app-rsc] (ecmascript, Next.js Server Component)\" } [app-rsc] (ecmascript)")
module.exports=R.m("[project]/node_modules/next/dist/esm/build/templates/app-page.js?page=/[locale]/page { GLOBAL_ERROR_MODULE => \"[project]/node_modules/next/dist/client/components/builtin/global-error.js [app-rsc] (ecmascript, Next.js Server Component)\", METADATA_0 => \"[project]/packages/website/src/app/favicon.ico.mjs { IMAGE => \\\"[project]/packages/website/src/app/favicon.ico (static in ecmascript)\\\" } [app-rsc] (structured image object, ecmascript, Next.js Server Component)\", MODULE_1 => \"[project]/packages/website/src/app/layout.tsx [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_2 => \"[project]/node_modules/next/dist/client/components/builtin/not-found.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_3 => \"[project]/node_modules/next/dist/client/components/builtin/forbidden.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_4 => \"[project]/node_modules/next/dist/client/components/builtin/unauthorized.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_5 => \"[project]/node_modules/next/dist/client/components/builtin/global-error.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_6 => \"[project]/packages/website/src/app/[locale]/layout.tsx [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_7 => \"[project]/packages/website/src/app/[locale]/loading.tsx [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_8 => \"[project]/packages/website/src/app/[locale]/page.tsx [app-rsc] (ecmascript, Next.js Server Component)\" } [app-rsc] (ecmascript)").exports
