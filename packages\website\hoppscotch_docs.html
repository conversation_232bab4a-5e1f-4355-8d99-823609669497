<!DOCTYPE html><html lang="en" class="__variable_8c6b06 __variable_3bbdad dark" data-banner-state="visible" data-page-mode="none"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="preload" href="/mintlify-assets/_next/static/media/bb3ef058b751a6ad-s.p.woff2" as="font" crossorigin="" type="font/woff2"/><link rel="preload" href="/mintlify-assets/_next/static/media/e4af272ccee01ff0-s.p.woff2" as="font" crossorigin="" type="font/woff2"/><link rel="preload" as="image" href="https://mintcdn.com/hoppscotch/WCaaGbVhL02n1fVh/logo/light.svg?maxW=24&amp;auto=format&amp;n=WCaaGbVhL02n1fVh&amp;q=85&amp;s=16bb5835da6a36c3e15623e498995666"/><link rel="preload" as="image" href="https://mintcdn.com/hoppscotch/WCaaGbVhL02n1fVh/logo/dark.svg?maxW=24&amp;auto=format&amp;n=WCaaGbVhL02n1fVh&amp;q=85&amp;s=0b5b10713c4319ddfb2b4280c62403ce"/><link rel="stylesheet" href="/mintlify-assets/_next/static/css/ac21e023a4c8f195.css" data-precedence="next"/><link rel="stylesheet" href="/mintlify-assets/_next/static/css/d910ce6c26d880b3.css" data-precedence="next"/><link rel="stylesheet" href="/mintlify-assets/_next/static/css/aabfb6890a442064.css" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="/mintlify-assets/_next/static/chunks/webpack-8cb96fb5dcdbae74.js"/><script src="/mintlify-assets/_next/static/chunks/87c73c54-2174f7eb4d0d8b2a.js" async=""></script><script src="/mintlify-assets/_next/static/chunks/1902-9d9e74ce93087a33.js" async=""></script><script src="/mintlify-assets/_next/static/chunks/main-app-a364df815487be02.js" async=""></script><script src="/mintlify-assets/_next/static/chunks/891cff7f-2ca7d0df884db9d0.js" async=""></script><script src="/mintlify-assets/_next/static/chunks/4368-ba814e7a6fbcef8d.js" async=""></script><script src="/mintlify-assets/_next/static/chunks/7261-f7cddf0d79dec697.js" async=""></script><script src="/mintlify-assets/_next/static/chunks/3365-454992a36759b84c.js" async=""></script><script src="/mintlify-assets/_next/static/chunks/1251-9e08ab4fcf4ec130.js" async=""></script><script src="/mintlify-assets/_next/static/chunks/4518-636d4b1b91962e91.js" async=""></script><script src="/mintlify-assets/_next/static/chunks/app/error-4b003575cfba285a.js" async=""></script><script src="/mintlify-assets/_next/static/chunks/d30757c7-6effe791c08262b1.js" async=""></script><script src="/mintlify-assets/_next/static/chunks/cd24890f-549fb4ba2f588ca6.js" async=""></script><script src="/mintlify-assets/_next/static/chunks/7267-4ae27d995fb80e6e.js" async=""></script><script src="/mintlify-assets/_next/static/chunks/9884-7de6993c6eaf8ebe.js" async=""></script><script src="/mintlify-assets/_next/static/chunks/7417-f80268fa1bfb6ac6.js" async=""></script><script src="/mintlify-assets/_next/static/chunks/7694-be5e0baf29ec311f.js" async=""></script><script src="/mintlify-assets/_next/static/chunks/7741-6aae3511c9a349f0.js" async=""></script><script src="/mintlify-assets/_next/static/chunks/6795-e69b9cf0aaf2d4f2.js" async=""></script><script src="/mintlify-assets/_next/static/chunks/3484-d2378bdc4b5e4f39.js" async=""></script><script src="/mintlify-assets/_next/static/chunks/9319-bd4367696c487773.js" async=""></script><script src="/mintlify-assets/_next/static/chunks/1750-1d0d5b4e9ed03a4f.js" async=""></script><script src="/mintlify-assets/_next/static/chunks/3972-a1df414e240d549c.js" async=""></script><script src="/mintlify-assets/_next/static/chunks/409-46ca51541cd3a87b.js" async=""></script><script src="/mintlify-assets/_next/static/chunks/app/%255Fsites/%5Bsubdomain%5D/(multitenant)/layout-59d5e59150e8a4e4.js" async=""></script><script src="/mintlify-assets/_next/static/chunks/app/%255Fsites/%5Bsubdomain%5D/not-found-646f47a67544360a.js" async=""></script><script src="/mintlify-assets/_next/static/chunks/app/global-error-9675be8ddbcc59bd.js" async=""></script><script src="/mintlify-assets/_next/static/chunks/app/%255Fsites/%5Bsubdomain%5D/error-d83b6d8e26d6dd95.js" async=""></script><script src="/mintlify-assets/_next/static/chunks/271c4271-e47f34f62bcfeead.js" async=""></script><script src="/mintlify-assets/_next/static/chunks/4960-ee8b5fbb01e1499b.js" async=""></script><script src="/mintlify-assets/_next/static/chunks/5143-31261db986b6fb7f.js" async=""></script><script src="/mintlify-assets/_next/static/chunks/1398-89a0fd2f8a70761b.js" async=""></script><script src="/mintlify-assets/_next/static/chunks/457-c340c72e118d74a1.js" async=""></script><script src="/mintlify-assets/_next/static/chunks/2544-93a10271db7901cd.js" async=""></script><script src="/mintlify-assets/_next/static/chunks/app/%255Fsites/%5Bsubdomain%5D/(multitenant)/%5B%5B...slug%5D%5D/page-4dde76c33c4e15c1.js" async=""></script><link rel="preload" href="/mintlify-assets/_next/static/chunks/2603.4a3626e375165093.js" as="script" fetchPriority="low"/><link rel="preload" href="https://d4tuoctqmanu0.cloudfront.net/katex.min.css" as="style"/><meta name="next-size-adjust" content=""/><title>Hoppscotch Documentation - Hoppscotch Documentation</title><meta name="description" content="Find user guides, quickstarts, tutorials, use cases, code samples, and more."/><meta name="application-name" content="Hoppscotch Documentation"/><meta name="generator" content="Mintlify"/><meta name="msapplication-config" content="/mintlify-assets/_mintlify/favicons/hoppscotch/-JgjP-ycG3ET4V1_/_generated/favicon/browserconfig.xml"/><meta name="apple-mobile-web-app-title" content="Hoppscotch Documentation"/><meta name="msapplication-TileColor" content="#0D9373"/><meta name="charset" content="utf-8"/><meta name="og:site_name" content="Hoppscotch Documentation"/><link rel="alternate" type="application/xml" href="/sitemap.xml"/><meta property="og:title" content="Hoppscotch Documentation - Hoppscotch Documentation"/><meta property="og:description" content="Find user guides, quickstarts, tutorials, use cases, code samples, and more."/><meta property="og:image" content="https://hoppscotch.mintlify.app/mintlify-assets/_next/image?url=%2F_mintlify%2Fapi%2Fog%3Fdivision%3DDocumentation%26title%3DHoppscotch%2BDocumentation%26description%3DFind%2Buser%2Bguides%252C%2Bquickstarts%252C%2Btutorials%252C%2Buse%2Bcases%252C%2Bcode%2Bsamples%252C%2Band%2Bmore.%26logoLight%3Dhttps%253A%252F%252Fmintcdn.com%252Fhoppscotch%252FWCaaGbVhL02n1fVh%252Flogo%252Flight.svg%253FmaxW%253D24%2526auto%253Dformat%2526n%253DWCaaGbVhL02n1fVh%2526q%253D85%2526s%253D16bb5835da6a36c3e15623e498995666%26logoDark%3Dhttps%253A%252F%252Fmintcdn.com%252Fhoppscotch%252FWCaaGbVhL02n1fVh%252Flogo%252Fdark.svg%253FmaxW%253D24%2526auto%253Dformat%2526n%253DWCaaGbVhL02n1fVh%2526q%253D85%2526s%253D0b5b10713c4319ddfb2b4280c62403ce%26primaryColor%3D%25230D9373%26lightColor%3D%252307C983%26darkColor%3D%25230D9373%26backgroundLight%3D%2523ffffff%26backgroundDark%3D%2523090d0d&amp;w=1200&amp;q=100"/><meta property="og:image:width" content="1200"/><meta property="og:image:height" content="630"/><meta property="og:type" content="website"/><meta name="twitter:card" content="summary_large_image"/><meta name="twitter:title" content="Hoppscotch Documentation - Hoppscotch Documentation"/><meta name="twitter:description" content="Find user guides, quickstarts, tutorials, use cases, code samples, and more."/><meta name="twitter:image" content="https://hoppscotch.mintlify.app/mintlify-assets/_next/image?url=%2F_mintlify%2Fapi%2Fog%3Fdivision%3DDocumentation%26title%3DHoppscotch%2BDocumentation%26description%3DFind%2Buser%2Bguides%252C%2Bquickstarts%252C%2Btutorials%252C%2Buse%2Bcases%252C%2Bcode%2Bsamples%252C%2Band%2Bmore.%26logoLight%3Dhttps%253A%252F%252Fmintcdn.com%252Fhoppscotch%252FWCaaGbVhL02n1fVh%252Flogo%252Flight.svg%253FmaxW%253D24%2526auto%253Dformat%2526n%253DWCaaGbVhL02n1fVh%2526q%253D85%2526s%253D16bb5835da6a36c3e15623e498995666%26logoDark%3Dhttps%253A%252F%252Fmintcdn.com%252Fhoppscotch%252FWCaaGbVhL02n1fVh%252Flogo%252Fdark.svg%253FmaxW%253D24%2526auto%253Dformat%2526n%253DWCaaGbVhL02n1fVh%2526q%253D85%2526s%253D0b5b10713c4319ddfb2b4280c62403ce%26primaryColor%3D%25230D9373%26lightColor%3D%252307C983%26darkColor%3D%25230D9373%26backgroundLight%3D%2523ffffff%26backgroundDark%3D%2523090d0d&amp;w=1200&amp;q=100"/><meta name="twitter:image:width" content="1200"/><meta name="twitter:image:height" content="630"/><link rel="apple-touch-icon" href="/mintlify-assets/_mintlify/favicons/hoppscotch/-JgjP-ycG3ET4V1_/_generated/favicon/apple-touch-icon.png" type="image/png" sizes="180x180"/><link rel="icon" href="/mintlify-assets/_mintlify/favicons/hoppscotch/-JgjP-ycG3ET4V1_/_generated/favicon/favicon-16x16.png" type="image/png" sizes="16x16" media="(prefers-color-scheme: light)"/><link rel="icon" href="/mintlify-assets/_mintlify/favicons/hoppscotch/-JgjP-ycG3ET4V1_/_generated/favicon/favicon-32x32.png" type="image/png" sizes="32x32" media="(prefers-color-scheme: light)"/><link rel="shortcut icon" href="/mintlify-assets/_mintlify/favicons/hoppscotch/-JgjP-ycG3ET4V1_/_generated/favicon/favicon.ico" type="image/x-icon" media="(prefers-color-scheme: light)"/><link rel="icon" href="/mintlify-assets/_mintlify/favicons/hoppscotch/-JgjP-ycG3ET4V1_/_generated/favicon-dark/favicon-16x16.png" type="image/png" sizes="16x16" media="(prefers-color-scheme: dark)"/><link rel="icon" href="/mintlify-assets/_mintlify/favicons/hoppscotch/-JgjP-ycG3ET4V1_/_generated/favicon-dark/favicon-32x32.png" type="image/png" sizes="32x32" media="(prefers-color-scheme: dark)"/><link rel="shortcut icon" href="/mintlify-assets/_mintlify/favicons/hoppscotch/-JgjP-ycG3ET4V1_/_generated/favicon-dark/favicon.ico" type="image/x-icon" media="(prefers-color-scheme: dark)"/><script type="text/javascript">(function(a,b,c){try{let d=localStorage.getItem(a);if(null==d)for(let c=0;c<localStorage.length;c++){let e=localStorage.key(c);if(e?.endsWith(`-${b}`)&&(d=localStorage.getItem(e),null!=d)){localStorage.setItem(a,d),localStorage.setItem(e,d);break}}let e=document.getElementById("banner")?.innerText,f=null==d||!!e&&d!==e;document.documentElement.setAttribute(c,f?"visible":"hidden")}catch(a){console.error(a),document.documentElement.setAttribute(c,"hidden")}})(
  "__mintlify-bannerDismissed",
  "bannerDismissed",
  "data-banner-state",
)</script><script type="text/javascript">
            document.addEventListener('DOMContentLoaded', () => {
              const link = document.querySelector('link[href="https://d4tuoctqmanu0.cloudfront.net/katex.min.css"]');
              link.rel = 'stylesheet';
            });
          </script><script src="/mintlify-assets/_next/static/chunks/polyfills-42372ed130431b0a.js" noModule=""></script></head><body><div hidden=""><!--$--><!--/$--></div><script>((a,b,c,d,e,f,g,h)=>{let i=document.documentElement,j=["light","dark"];function k(b){var c;(Array.isArray(a)?a:[a]).forEach(a=>{let c="class"===a,d=c&&f?e.map(a=>f[a]||a):e;c?(i.classList.remove(...d),i.classList.add(f&&f[b]?f[b]:b)):i.setAttribute(a,b)}),c=b,h&&j.includes(c)&&(i.style.colorScheme=c)}if(d)k(d);else try{let a=localStorage.getItem(b)||c,d=g&&"system"===a?window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light":a;k(d)}catch(a){}})("class","isDarkMode","system",null,["dark","light","true","false","system"],{"true":"dark","false":"light","dark":"dark","light":"light"},true,true)</script><script>(self.__next_s=self.__next_s||[]).push([0,{"children":"(function m(a,b,c,d){try{let e=document.getElementById(\"banner\"),f=e?.innerText;if(!f)return void document.documentElement.setAttribute(d,\"hidden\");let g=localStorage.getItem(a),h=g!==f&&g!==b;null!=g&&(h?(localStorage.removeItem(c),localStorage.removeItem(a)):(localStorage.setItem(c,b),localStorage.setItem(a,b))),document.documentElement.setAttribute(d,!g||h?\"visible\":\"hidden\")}catch(a){console.error(a),document.documentElement.setAttribute(d,\"hidden\")}})(\n  \"hoppscotch-bannerDismissed\",\n  undefined,\n  \"__mintlify-bannerDismissed\",\n  \"data-banner-state\",\n)","id":"_mintlify-banner-script"}])</script><style>:root {
    --primary: 13 147 115;
    --primary-light: 7 201 131;
    --primary-dark: 13 147 115;
    --background-light: 255 255 255;
    --background-dark: 9 13 13;
    --gray-50: 243 247 246;
    --gray-100: 238 242 241;
    --gray-200: 222 226 226;
    --gray-300: 206 210 209;
    --gray-400: 158 163 162;
    --gray-500: 112 116 115;
    --gray-600: 80 84 83;
    --gray-700: 62 67 66;
    --gray-800: 37 41 40;
    --gray-900: 23 27 26;
    --gray-950: 10 14 13;
  }</style><div class="relative antialiased text-gray-500 dark:text-gray-400"><script>(self.__next_s=self.__next_s||[]).push([0,{"suppressHydrationWarning":true,"children":"(function(a,b,c,d){var e;let f,g=\"mint\"===d||\"linden\"===d?\"sidebar\":\"sidebar-content\",h=(e=d,f=\"navbar-transition\",\"maple\"===e&&(f+=\"-maple\"),\"willow\"===e&&(f+=\"-willow\"),f);function i(){document.documentElement.classList.add(\"lg:[--scroll-mt:9.5rem]\")}function j(a){document.getElementById(g)?.style.setProperty(\"top\",`${a}rem`)}function k(a){document.getElementById(g)?.style.setProperty(\"height\",`calc(100vh - ${a}rem)`)}function l(a,b){!a&&b||a&&!b?(i(),document.documentElement.classList.remove(\"lg:[--scroll-mt:12rem]\")):a&&b&&(document.documentElement.classList.add(\"lg:[--scroll-mt:12rem]\"),document.documentElement.classList.remove(\"lg:[--scroll-mt:9.5rem]\"))}let m=document.documentElement.getAttribute(\"data-banner-state\"),n=null!=m?\"visible\"===m:b;switch(d){case\"mint\":j(c),l(a,n);break;case\"palm\":case\"aspen\":j(c),k(c),l(a,n);break;case\"linden\":j(c),n&&i();break;case\"almond\":document.documentElement.style.setProperty(\"--scroll-mt\",\"2.5rem\"),j(c),k(c)}let o=function(){let a=document.createElement(\"style\");return a.appendChild(document.createTextNode(\"*,*::before,*::after{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}\")),document.head.appendChild(a),function(){window.getComputedStyle(document.body),setTimeout(()=>{document.head.removeChild(a)},1)}}();(\"requestAnimationFrame\"in globalThis?requestAnimationFrame:setTimeout)(()=>{let a;a=!1,a=window.scrollY>50,document.getElementById(h)?.setAttribute(\"data-is-opaque\",`${!!a}`),o()})})(\n  false,\n  false,\n  (function l(a,b,c){let d=document.documentElement.getAttribute(\"data-banner-state\"),e=2.5*!!(null!=d?\"visible\"===d:b),f=3*!!a,g=4,h=e+g+f;switch(c){case\"mint\":case\"palm\":break;case\"aspen\":f=2.5*!!a,g=3.5,h=e+f+g;break;case\"linden\":g=4,h=e+g;break;case\"almond\":g=3.5,h=e+g}return h})(false, false, \"mint\"),\n  \"mint\",\n)","id":"_mintlify-scroll-top-script"}])</script><div id="navbar" class="z-30 fixed lg:sticky top-0 w-full peer is-not-custom peer is-not-center peer is-not-wide peer is-not-frame"><div id="navbar-transition" class="absolute w-full h-full backdrop-blur flex-none transition-colors duration-500 border-b border-gray-500/5 dark:border-gray-300/[0.06] data-[is-opaque=true]:bg-background-light data-[is-opaque=true]:supports-backdrop-blur:bg-background-light/95 data-[is-opaque=true]:dark:bg-background-dark/75 data-[is-opaque=false]:supports-backdrop-blur:bg-background-light/60 data-[is-opaque=false]:dark:bg-transparent" data-is-opaque="false"></div><div class="max-w-8xl mx-auto relative"><div><div class="relative"><div class="flex items-center lg:px-12 h-16 min-w-0 px-4"><div class="h-full relative flex-1 flex items-center gap-x-4 min-w-0 border-b border-gray-500/5 dark:border-gray-300/[0.06] lg:border-none"><div class="flex-1 flex items-center gap-x-4"><a href="/"><span class="sr-only">Hoppscotch Documentation<!-- --> home page</span><img class="nav-logo w-auto h-7 relative object-contain block dark:hidden" src="https://mintcdn.com/hoppscotch/WCaaGbVhL02n1fVh/logo/light.svg?maxW=24&amp;auto=format&amp;n=WCaaGbVhL02n1fVh&amp;q=85&amp;s=16bb5835da6a36c3e15623e498995666" alt="light logo"/><img class="nav-logo w-auto h-7 relative object-contain hidden dark:block" src="https://mintcdn.com/hoppscotch/WCaaGbVhL02n1fVh/logo/dark.svg?maxW=24&amp;auto=format&amp;n=WCaaGbVhL02n1fVh&amp;q=85&amp;s=0b5b10713c4319ddfb2b4280c62403ce" alt="dark logo"/></a><div class="hidden lg:flex items-center gap-x-2"></div></div><div class="relative hidden lg:flex items-center flex-1 justify-center"><button type="button" class="flex pointer-events-auto rounded-xl w-full items-center text-sm leading-6 h-9 pl-3.5 pr-3 shadow-sm text-gray-500 dark:text-white/50 bg-background-light dark:bg-background-dark dark:brightness-[1.1] dark:ring-1 dark:hover:brightness-[1.25] ring-1 ring-gray-400/20 hover:ring-gray-600/25 dark:ring-gray-600/30 dark:hover:ring-gray-500/30 focus:outline-primary justify-between truncate gap-2 min-w-[43px]" id="search-bar-entry"><div class="flex items-center gap-2 min-w-[42px]"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-search min-w-4 flex-none text-gray-700 hover:text-gray-800 dark:text-gray-400 hover:dark:text-gray-200"><circle cx="11" cy="11" r="8"></circle><path d="m21 21-4.3-4.3"></path></svg><div class="truncate min-w-0">Search</div></div><span class="flex-none text-xs font-semibold">鈱?!-- -->K</span></button></div><div class="flex-1 relative hidden lg:flex items-center ml-auto justify-end space-x-4"><nav class="text-sm"><ul class="flex space-x-6 items-center"><li class="cursor-pointer flex lg:hidden"><a href="https://github.com/hoppscotch/hoppscotch" target="_blank" rel="noreferrer" class="group flex items-center rounded-md hover:text-primary dark:hover:text-primary-light"><div class="flex items-center gap-1.5 h-8"><div class="flex items-center gap-2"><svg xmlns="http://www.w3.org/2000/svg" width="1024" height="1024" viewBox="0 0 1024 1024" fill="currentColor" class="size-4"><path fill-rule="evenodd" clip-rule="evenodd" d="M8 0C3.58 0 0 3.58 0 8C0 11.54 2.29 14.53 5.47 15.59C5.87 15.66 6.02 15.42 6.02 15.21C6.02 15.02 6.01 14.39 6.01 13.72C4 14.09 3.48 13.23 3.32 12.78C3.23 12.55 2.84 11.84 2.5 11.65C2.22 11.5 1.82 11.13 2.49 11.12C3.12 11.11 3.57 11.7 3.72 11.94C4.44 13.15 5.59 12.81 6.05 12.6C6.12 12.08 6.33 11.73 6.56 11.53C4.78 11.33 2.92 10.64 2.92 7.58C2.92 6.71 3.23 5.99 3.74 5.43C3.66 5.23 3.38 4.41 3.82 3.31C3.82 3.31 4.49 3.1 6.02 4.13C6.66 3.95 7.34 3.86 8.02 3.86C8.7 3.86 9.38 3.95 10.02 4.13C11.55 3.09 12.22 3.31 12.22 3.31C12.66 4.41 12.38 5.23 12.3 5.43C12.81 5.99 13.12 6.7 13.12 7.58C13.12 10.65 11.25 11.33 9.47 11.53C9.76 11.78 10.01 12.26 10.01 13.01C10.01 14.08 10 14.94 10 15.21C10 15.42 10.15 15.67 10.55 15.59C13.71 14.53 16 11.53 16 8C16 3.58 12.42 0 8 0Z" transform="scale(64)"></path></svg><span class="text-sm font-medium text-gray-700 dark:text-gray-300 group-hover:text-primary dark:group-hover:text-primary-light">hoppscotch<!-- -->/<!-- -->hoppscotch</span></div></div></a></li><li class="cursor-pointer hidden lg:flex"><a href="https://github.com/hoppscotch/hoppscotch" target="_blank" rel="noreferrer" class="group flex items-center rounded-md hover:text-primary dark:hover:text-primary-light"><div class="flex items-center gap-1.5 h-8"><div class="flex items-center gap-2"><svg xmlns="http://www.w3.org/2000/svg" width="1024" height="1024" viewBox="0 0 1024 1024" fill="currentColor" class="size-4"><path fill-rule="evenodd" clip-rule="evenodd" d="M8 0C3.58 0 0 3.58 0 8C0 11.54 2.29 14.53 5.47 15.59C5.87 15.66 6.02 15.42 6.02 15.21C6.02 15.02 6.01 14.39 6.01 13.72C4 14.09 3.48 13.23 3.32 12.78C3.23 12.55 2.84 11.84 2.5 11.65C2.22 11.5 1.82 11.13 2.49 11.12C3.12 11.11 3.57 11.7 3.72 11.94C4.44 13.15 5.59 12.81 6.05 12.6C6.12 12.08 6.33 11.73 6.56 11.53C4.78 11.33 2.92 10.64 2.92 7.58C2.92 6.71 3.23 5.99 3.74 5.43C3.66 5.23 3.38 4.41 3.82 3.31C3.82 3.31 4.49 3.1 6.02 4.13C6.66 3.95 7.34 3.86 8.02 3.86C8.7 3.86 9.38 3.95 10.02 4.13C11.55 3.09 12.22 3.31 12.22 3.31C12.66 4.41 12.38 5.23 12.3 5.43C12.81 5.99 13.12 6.7 13.12 7.58C13.12 10.65 11.25 11.33 9.47 11.53C9.76 11.78 10.01 12.26 10.01 13.01C10.01 14.08 10 14.94 10 15.21C10 15.42 10.15 15.67 10.55 15.59C13.71 14.53 16 11.53 16 8C16 3.58 12.42 0 8 0Z" transform="scale(64)"></path></svg><span class="text-sm font-medium text-gray-700 dark:text-gray-300 group-hover:text-primary dark:group-hover:text-primary-light">hoppscotch<!-- -->/<!-- -->hoppscotch</span></div></div></a></li></ul></nav><div class="flex items-center"><button class="group p-2 flex items-center justify-center" aria-label="Toggle dark mode"><svg width="16" height="16" viewBox="0 0 16 16" fill="none" stroke="currentColor" xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 block text-gray-400 dark:hidden group-hover:text-gray-600"><g clip-path="url(#clip0_2880_7340)"><path d="M8 1.11133V2.00022" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path><path d="M12.8711 3.12891L12.2427 3.75735" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path><path d="M14.8889 8H14" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path><path d="M12.8711 12.8711L12.2427 12.2427" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path><path d="M8 14.8889V14" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path><path d="M3.12891 12.8711L3.75735 12.2427" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path><path d="M1.11133 8H2.00022" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path><path d="M3.12891 3.12891L3.75735 3.75735" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path><path d="M8.00043 11.7782C10.0868 11.7782 11.7782 10.0868 11.7782 8.00043C11.7782 5.91402 10.0868 4.22266 8.00043 4.22266C5.91402 4.22266 4.22266 5.91402 4.22266 8.00043C4.22266 10.0868 5.91402 11.7782 8.00043 11.7782Z" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path></g><defs><clipPath id="clip0_2880_7340"><rect width="16" height="16" fill="white"></rect></clipPath></defs></svg><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-moon h-4 w-4 hidden dark:block text-gray-500 dark:group-hover:text-gray-300"><path d="M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z"></path></svg></button></div></div><div class="flex lg:hidden items-center gap-3"><button type="button" class="text-gray-500 w-8 h-8 flex items-center justify-center hover:text-gray-600 dark:text-gray-400 dark:hover:text-gray-300" id="search-bar-entry-mobile"><span class="sr-only">Search...</span><svg class="h-4 w-4 bg-gray-500 dark:bg-gray-400 hover:bg-gray-600 dark:hover:bg-gray-300" style="-webkit-mask-image:url(https://d3gk2c5xim1je2.cloudfront.net/v6.6.0/solid/magnifying-glass.svg);-webkit-mask-repeat:no-repeat;-webkit-mask-position:center;mask-image:url(https://d3gk2c5xim1je2.cloudfront.net/v6.6.0/solid/magnifying-glass.svg);mask-repeat:no-repeat;mask-position:center"></svg></button><button aria-label="More actions" class="h-7 w-5 flex items-center justify-end"><svg class="h-4 w-4 bg-gray-500 dark:bg-gray-400 hover:bg-gray-600 dark:hover:bg-gray-300" style="-webkit-mask-image:url(https://d3gk2c5xim1je2.cloudfront.net/v6.6.0/solid/ellipsis-vertical.svg);-webkit-mask-repeat:no-repeat;-webkit-mask-position:center;mask-image:url(https://d3gk2c5xim1je2.cloudfront.net/v6.6.0/solid/ellipsis-vertical.svg);mask-repeat:no-repeat;mask-position:center"></svg></button></div></div></div><button type="button" class="flex items-center h-14 py-4 px-5 lg:hidden focus:outline-none w-full text-left"><div class="text-gray-500 hover:text-gray-600 dark:text-gray-400 dark:hover:text-gray-300"><span class="sr-only">Navigation</span><svg class="h-4" fill="currentColor" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><path d="M0 96C0 78.3 14.3 64 32 64H416c17.7 0 32 14.3 32 32s-14.3 32-32 32H32C14.3 128 0 113.7 0 96zM0 256c0-17.7 14.3-32 32-32H416c17.7 0 32 14.3 32 32s-14.3 32-32 32H32c-17.7 0-32-14.3-32-32zM448 416c0 17.7-14.3 32-32 32H32c-17.7 0-32-14.3-32-32s14.3-32 32-32H416c17.7 0 32 14.3 32 32z"></path></svg></div><div class="ml-4 flex text-sm leading-6 whitespace-nowrap min-w-0 space-x-3 overflow-hidden"><div class="font-semibold text-gray-900 truncate dark:text-gray-200 min-w-0 flex-1">Hoppscotch Documentation</div></div></button></div></div></div><span hidden="" style="position:fixed;top:1px;left:1px;width:1px;height:0;padding:0;margin:-1px;overflow:hidden;clip:rect(0, 0, 0, 0);white-space:nowrap;border-width:0;display:none"></span></div><div class="peer-[.is-not-center]:max-w-8xl peer-[.is-center]:max-w-3xl peer-[.is-not-custom]:px-4 peer-[.is-not-custom]:mx-auto peer-[.is-not-custom]:lg:px-8 peer-[.is-wide]:[&amp;&gt;div:last-child]:max-w-6xl peer-[.is-custom]:contents peer-[.is-custom]:[&amp;&gt;div:first-child]:!hidden peer-[.is-custom]:[&amp;&gt;div:first-child]:sm:!hidden peer-[.is-custom]:[&amp;&gt;div:first-child]:md:!hidden peer-[.is-custom]:[&amp;&gt;div:first-child]:lg:!hidden peer-[.is-custom]:[&amp;&gt;div:first-child]:xl:!hidden peer-[.is-center]:[&amp;&gt;div:first-child]:!hidden peer-[.is-center]:[&amp;&gt;div:first-child]:sm:!hidden peer-[.is-center]:[&amp;&gt;div:first-child]:md:!hidden peer-[.is-center]:[&amp;&gt;div:first-child]:lg:!hidden peer-[.is-center]:[&amp;&gt;div:first-child]:xl:!hidden"><div class="z-20 hidden lg:block fixed bottom-0 right-auto w-[18rem]" id="sidebar" style="top:4rem"><div class="absolute inset-0 z-10 stable-scrollbar-gutter overflow-auto pr-8 pb-10" id="sidebar-content"><div class="relative lg:text-sm lg:leading-6"><div class="sticky top-0 h-8 z-10 bg-gradient-to-b from-background-light dark:from-background-dark"></div><div id="navigation-items"><li class="list-none"><a class="link nav-anchor pl-4 group flex items-center lg:text-sm lg:leading-6 mb-5 sm:mb-4 font-semibold text-primary dark:text-primary-light" href="/documentation/getting-started/introduction"><div style="background:#0D9373" class="mr-4 rounded-md p-1"><svg class="h-4 w-4 secondary-opacity group-hover:fill-primary-dark group-hover:bg-white bg-white" style="-webkit-mask-image:url(https://d3gk2c5xim1je2.cloudfront.net/v6.6.0/duotone/lines-leaning.svg);-webkit-mask-repeat:no-repeat;-webkit-mask-position:center;mask-image:url(https://d3gk2c5xim1je2.cloudfront.net/v6.6.0/duotone/lines-leaning.svg);mask-repeat:no-repeat;mask-position:center"></svg></div>Documentation</a></li><li class="list-none"><a class="link nav-anchor pl-4 group flex items-center lg:text-sm lg:leading-6 mb-5 sm:mb-4 font-medium text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300" href="/guides/getting-started/introduction"><div class="mr-4 rounded-md p-1 shadow-sm text-gray-400 dark:text-white/50 dark:bg-background-dark dark:brightness-[1.35] dark:ring-1 dark:hover:brightness-150 group-hover:brightness-100 group-hover:ring-0 ring-1 ring-gray-950/5 dark:ring-gray-700/40"><svg class="h-4 w-4 secondary-opacity group-hover:fill-primary-dark group-hover:bg-white bg-gray-400 dark:bg-gray-500" style="-webkit-mask-image:url(https://d3gk2c5xim1je2.cloudfront.net/v6.6.0/duotone/check-double.svg);-webkit-mask-repeat:no-repeat;-webkit-mask-position:center;mask-image:url(https://d3gk2c5xim1je2.cloudfront.net/v6.6.0/duotone/check-double.svg);mask-repeat:no-repeat;mask-position:center"></svg></div>Guides</a></li><li class="list-none"><a class="link nav-anchor pl-4 group flex items-center lg:text-sm lg:leading-6 mb-5 sm:mb-4 font-medium text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300" href="/support/getting-started/introduction"><div class="mr-4 rounded-md p-1 shadow-sm text-gray-400 dark:text-white/50 dark:bg-background-dark dark:brightness-[1.35] dark:ring-1 dark:hover:brightness-150 group-hover:brightness-100 group-hover:ring-0 ring-1 ring-gray-950/5 dark:ring-gray-700/40"><svg class="h-4 w-4 secondary-opacity group-hover:fill-primary-dark group-hover:bg-white bg-gray-400 dark:bg-gray-500" style="-webkit-mask-image:url(https://d3gk2c5xim1je2.cloudfront.net/v6.6.0/duotone/question.svg);-webkit-mask-repeat:no-repeat;-webkit-mask-position:center;mask-image:url(https://d3gk2c5xim1je2.cloudfront.net/v6.6.0/duotone/question.svg);mask-repeat:no-repeat;mask-position:center"></svg></div>Support</a></li><li class="list-none"><a href="https://hoppscotch.com/download" target="_blank" rel="noreferrer" class="link nav-anchor pl-4 group flex items-center lg:text-sm lg:leading-6 mb-5 sm:mb-4 font-medium text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300"><div class="mr-4 rounded-md p-1 shadow-sm text-gray-400 dark:text-white/50 dark:bg-background-dark dark:brightness-[1.35] dark:ring-1 dark:hover:brightness-150 group-hover:brightness-100 group-hover:ring-0 ring-1 ring-gray-950/5 dark:ring-gray-700/40"><svg class="h-4 w-4 secondary-opacity group-hover:fill-primary-dark group-hover:bg-white bg-gray-400 dark:bg-gray-500" style="-webkit-mask-image:url(https://d3gk2c5xim1je2.cloudfront.net/v6.6.0/duotone/angles-down.svg);-webkit-mask-repeat:no-repeat;-webkit-mask-position:center;mask-image:url(https://d3gk2c5xim1je2.cloudfront.net/v6.6.0/duotone/angles-down.svg);mask-repeat:no-repeat;mask-position:center"></svg></div>Download</a></li><li class="list-none"><a href="https://hoppscotch.com/blog" target="_blank" rel="noreferrer" class="link nav-anchor pl-4 group flex items-center lg:text-sm lg:leading-6 mb-5 sm:mb-4 font-medium text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300"><div class="mr-4 rounded-md p-1 shadow-sm text-gray-400 dark:text-white/50 dark:bg-background-dark dark:brightness-[1.35] dark:ring-1 dark:hover:brightness-150 group-hover:brightness-100 group-hover:ring-0 ring-1 ring-gray-950/5 dark:ring-gray-700/40"><svg class="h-4 w-4 secondary-opacity group-hover:fill-primary-dark group-hover:bg-white bg-gray-400 dark:bg-gray-500" style="-webkit-mask-image:url(https://d3gk2c5xim1je2.cloudfront.net/v6.6.0/duotone/quote-left.svg);-webkit-mask-repeat:no-repeat;-webkit-mask-position:center;mask-image:url(https://d3gk2c5xim1je2.cloudfront.net/v6.6.0/duotone/quote-left.svg);mask-repeat:no-repeat;mask-position:center"></svg></div>Blog</a></li><li class="list-none"><a href="https://github.com/hoppscotch/hoppscotch/releases" target="_blank" rel="noreferrer" class="link nav-anchor pl-4 group flex items-center lg:text-sm lg:leading-6 mb-5 sm:mb-4 font-medium text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300"><div class="mr-4 rounded-md p-1 shadow-sm text-gray-400 dark:text-white/50 dark:bg-background-dark dark:brightness-[1.35] dark:ring-1 dark:hover:brightness-150 group-hover:brightness-100 group-hover:ring-0 ring-1 ring-gray-950/5 dark:ring-gray-700/40"><svg class="h-4 w-4 secondary-opacity group-hover:fill-primary-dark group-hover:bg-white bg-gray-400 dark:bg-gray-500" style="-webkit-mask-image:url(https://d3gk2c5xim1je2.cloudfront.net/v6.6.0/duotone/shapes.svg);-webkit-mask-repeat:no-repeat;-webkit-mask-position:center;mask-image:url(https://d3gk2c5xim1je2.cloudfront.net/v6.6.0/duotone/shapes.svg);mask-repeat:no-repeat;mask-position:center"></svg></div>Changelog</a></li><div class="mt-6 lg:mt-8"><div class="sidebar-group-header flex items-center gap-2.5 pl-4 mb-3.5 lg:mb-2.5 font-semibold text-gray-900 dark:text-gray-200"><h5 id="sidebar-title">Getting started</h5></div><ul id="sidebar-group"><li id="/documentation/getting-started/introduction" class="relative scroll-m-4 first:scroll-m-20" data-title="Introduction"><a class="group flex items-center pr-3 py-1.5 cursor-pointer focus:outline-primary dark:focus:outline-primary-light gap-x-3 rounded-xl hover:bg-gray-600/5 dark:hover:bg-gray-200/5 text-gray-700 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300" style="padding-left:1rem" href="/documentation/getting-started/introduction"><div class="flex-1 flex items-center space-x-2.5"><div class="">Introduction</div></div></a></li><li id="/documentation/getting-started/quick-start" class="relative scroll-m-4 first:scroll-m-20" data-title="Quick start"><a class="group flex items-center pr-3 py-1.5 cursor-pointer focus:outline-primary dark:focus:outline-primary-light gap-x-3 rounded-xl hover:bg-gray-600/5 dark:hover:bg-gray-200/5 text-gray-700 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300" style="padding-left:1rem" href="/documentation/getting-started/quick-start"><div class="flex-1 flex items-center space-x-2.5"><div class="">Quick start</div></div></a></li><li id="/documentation/getting-started/clients" class="relative scroll-m-4 first:scroll-m-20" data-title="Clients"><a class="group flex items-center pr-3 py-1.5 cursor-pointer focus:outline-primary dark:focus:outline-primary-light gap-x-3 rounded-xl hover:bg-gray-600/5 dark:hover:bg-gray-200/5 text-gray-700 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300" style="padding-left:1rem" href="/documentation/getting-started/clients"><div class="flex-1 flex items-center space-x-2.5"><div class="">Clients</div></div></a></li><li id="/documentation/getting-started/setup" class="relative scroll-m-4 first:scroll-m-20" data-title="Setup"><a class="group flex items-center pr-3 py-1.5 cursor-pointer focus:outline-primary dark:focus:outline-primary-light gap-x-3 rounded-xl hover:bg-gray-600/5 dark:hover:bg-gray-200/5 text-gray-700 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300" style="padding-left:1rem" href="/documentation/getting-started/setup"><div class="flex-1 flex items-center space-x-2.5"><div class="">Setup</div></div></a></li><li id="/documentation/getting-started/troubleshooting" class="relative scroll-m-4 first:scroll-m-20" data-title="Troubleshooting"><a class="group flex items-center pr-3 py-1.5 cursor-pointer focus:outline-primary dark:focus:outline-primary-light gap-x-3 rounded-xl hover:bg-gray-600/5 dark:hover:bg-gray-200/5 text-gray-700 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300" style="padding-left:1rem" href="/documentation/getting-started/troubleshooting"><div class="flex-1 flex items-center space-x-2.5"><div class="">Troubleshooting</div></div></a></li><li data-title="RESTful API" data-group-tag=""><div class="group flex items-center pr-3 py-1.5 cursor-pointer focus:outline-primary dark:focus:outline-primary-light gap-x-3 rounded-xl hover:bg-gray-600/5 dark:hover:bg-gray-200/5 text-gray-700 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300" style="padding-left:1rem"><div class="">RESTful API</div><svg width="8" height="24" viewBox="0 -9 3 24" class="transition-transform text-gray-400 overflow-visible group-hover:text-gray-600 dark:text-gray-600 dark:group-hover:text-gray-400 -mr-0.5"><path d="M0 0L3 3L0 6" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"></path></svg></div></li><li data-title="GraphQL API" data-group-tag=""><div class="group flex items-center pr-3 py-1.5 cursor-pointer focus:outline-primary dark:focus:outline-primary-light gap-x-3 rounded-xl hover:bg-gray-600/5 dark:hover:bg-gray-200/5 text-gray-700 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300" style="padding-left:1rem"><div class="">GraphQL API</div><svg width="8" height="24" viewBox="0 -9 3 24" class="transition-transform text-gray-400 overflow-visible group-hover:text-gray-600 dark:text-gray-600 dark:group-hover:text-gray-400 -mr-0.5"><path d="M0 0L3 3L0 6" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"></path></svg></div></li><li data-title="Realtime API" data-group-tag=""><div class="group flex items-center pr-3 py-1.5 cursor-pointer focus:outline-primary dark:focus:outline-primary-light gap-x-3 rounded-xl hover:bg-gray-600/5 dark:hover:bg-gray-200/5 text-gray-700 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300" style="padding-left:1rem"><div class="">Realtime API</div><svg width="8" height="24" viewBox="0 -9 3 24" class="transition-transform text-gray-400 overflow-visible group-hover:text-gray-600 dark:text-gray-600 dark:group-hover:text-gray-400 -mr-0.5"><path d="M0 0L3 3L0 6" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"></path></svg></div></li></ul></div><div class="mt-6 lg:mt-8"><div class="sidebar-group-header flex items-center gap-2.5 pl-4 mb-3.5 lg:mb-2.5 font-semibold text-gray-900 dark:text-gray-200"><h5 id="sidebar-title">Clients</h5></div><ul id="sidebar-group"><li id="/documentation/clients/web" class="relative scroll-m-4 first:scroll-m-20" data-title="Web"><a class="group flex items-center pr-3 py-1.5 cursor-pointer focus:outline-primary dark:focus:outline-primary-light gap-x-3 rounded-xl hover:bg-gray-600/5 dark:hover:bg-gray-200/5 text-gray-700 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300" style="padding-left:1rem" href="/documentation/clients/web"><div class="flex-1 flex items-center space-x-2.5"><div class="">Web</div></div></a></li><li id="/documentation/clients/desktop" class="relative scroll-m-4 first:scroll-m-20" data-title="Desktop"><a class="group flex items-center pr-3 py-1.5 cursor-pointer focus:outline-primary dark:focus:outline-primary-light gap-x-3 rounded-xl hover:bg-gray-600/5 dark:hover:bg-gray-200/5 text-gray-700 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300" style="padding-left:1rem" href="/documentation/clients/desktop"><div class="flex-1 flex items-center space-x-2.5"><div class="">Desktop</div></div></a></li><li data-title="CLI" data-group-tag=""><div class="group flex items-center pr-3 py-1.5 cursor-pointer focus:outline-primary dark:focus:outline-primary-light gap-x-3 rounded-xl hover:bg-gray-600/5 dark:hover:bg-gray-200/5 text-gray-700 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300" style="padding-left:1rem"><div class="">CLI</div><svg width="8" height="24" viewBox="0 -9 3 24" class="transition-transform text-gray-400 overflow-visible group-hover:text-gray-600 dark:text-gray-600 dark:group-hover:text-gray-400 -mr-0.5"><path d="M0 0L3 3L0 6" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"></path></svg></div></li></ul></div><div class="mt-6 lg:mt-8"><div class="sidebar-group-header flex items-center gap-2.5 pl-4 mb-3.5 lg:mb-2.5 font-semibold text-gray-900 dark:text-gray-200"><h5 id="sidebar-title">Protocols</h5></div><ul id="sidebar-group"><li id="/documentation/protocols/rest" class="relative scroll-m-4 first:scroll-m-20" data-title="RESTful"><a class="group flex items-center pr-3 py-1.5 cursor-pointer focus:outline-primary dark:focus:outline-primary-light gap-x-3 rounded-xl hover:bg-gray-600/5 dark:hover:bg-gray-200/5 text-gray-700 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300" style="padding-left:1rem" href="/documentation/protocols/rest"><div class="flex-1 flex items-center space-x-2.5"><div class="">RESTful</div></div></a></li><li id="/documentation/protocols/graphql" class="relative scroll-m-4 first:scroll-m-20" data-title="GraphQL"><a class="group flex items-center pr-3 py-1.5 cursor-pointer focus:outline-primary dark:focus:outline-primary-light gap-x-3 rounded-xl hover:bg-gray-600/5 dark:hover:bg-gray-200/5 text-gray-700 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300" style="padding-left:1rem" href="/documentation/protocols/graphql"><div class="flex-1 flex items-center space-x-2.5"><div class="">GraphQL</div></div></a></li><li id="/documentation/protocols/realtime" class="relative scroll-m-4 first:scroll-m-20" data-title="Realtime"><a class="group flex items-center pr-3 py-1.5 cursor-pointer focus:outline-primary dark:focus:outline-primary-light gap-x-3 rounded-xl hover:bg-gray-600/5 dark:hover:bg-gray-200/5 text-gray-700 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300" style="padding-left:1rem" href="/documentation/protocols/realtime"><div class="flex-1 flex items-center space-x-2.5"><div class="">Realtime</div></div></a></li></ul></div><div class="mt-6 lg:mt-8"><div class="sidebar-group-header flex items-center gap-2.5 pl-4 mb-3.5 lg:mb-2.5 font-semibold text-gray-900 dark:text-gray-200"><h5 id="sidebar-title">Features</h5></div><ul id="sidebar-group"><li id="/documentation/features/workspaces" class="relative scroll-m-4 first:scroll-m-20" data-title="Workspaces"><a class="group flex items-center pr-3 py-1.5 cursor-pointer focus:outline-primary dark:focus:outline-primary-light gap-x-3 rounded-xl hover:bg-gray-600/5 dark:hover:bg-gray-200/5 text-gray-700 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300" style="padding-left:1rem" href="/documentation/features/workspaces"><div class="flex-1 flex items-center space-x-2.5"><div class="">Workspaces</div></div></a></li><li id="/documentation/features/collections" class="relative scroll-m-4 first:scroll-m-20" data-title="Collections"><a class="group flex items-center pr-3 py-1.5 cursor-pointer focus:outline-primary dark:focus:outline-primary-light gap-x-3 rounded-xl hover:bg-gray-600/5 dark:hover:bg-gray-200/5 text-gray-700 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300" style="padding-left:1rem" href="/documentation/features/collections"><div class="flex-1 flex items-center space-x-2.5"><div class="">Collections</div></div></a></li><li id="/documentation/features/runner" class="relative scroll-m-4 first:scroll-m-20" data-title="Runner"><a class="group flex items-center pr-3 py-1.5 cursor-pointer focus:outline-primary dark:focus:outline-primary-light gap-x-3 rounded-xl hover:bg-gray-600/5 dark:hover:bg-gray-200/5 text-gray-700 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300" style="padding-left:1rem" href="/documentation/features/runner"><div class="flex-1 flex items-center space-x-2.5"><div class="">Runner</div></div></a></li><li id="/documentation/features/variables" class="relative scroll-m-4 first:scroll-m-20" data-title="Variables"><a class="group flex items-center pr-3 py-1.5 cursor-pointer focus:outline-primary dark:focus:outline-primary-light gap-x-3 rounded-xl hover:bg-gray-600/5 dark:hover:bg-gray-200/5 text-gray-700 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300" style="padding-left:1rem" href="/documentation/features/variables"><div class="flex-1 flex items-center space-x-2.5"><div class="">Variables</div></div></a></li><li id="/documentation/features/environments" class="relative scroll-m-4 first:scroll-m-20" data-title="Environments"><a class="group flex items-center pr-3 py-1.5 cursor-pointer focus:outline-primary dark:focus:outline-primary-light gap-x-3 rounded-xl hover:bg-gray-600/5 dark:hover:bg-gray-200/5 text-gray-700 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300" style="padding-left:1rem" href="/documentation/features/environments"><div class="flex-1 flex items-center space-x-2.5"><div class="">Environments</div></div></a></li><li id="/documentation/features/pat" class="relative scroll-m-4 first:scroll-m-20" data-title="Personal Access Token"><a class="group flex items-center pr-3 py-1.5 cursor-pointer focus:outline-primary dark:focus:outline-primary-light gap-x-3 rounded-xl hover:bg-gray-600/5 dark:hover:bg-gray-200/5 text-gray-700 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300" style="padding-left:1rem" href="/documentation/features/pat"><div class="flex-1 flex items-center space-x-2.5"><div class="">Personal Access Token</div></div></a></li><li id="/documentation/features/history" class="relative scroll-m-4 first:scroll-m-20" data-title="History"><a class="group flex items-center pr-3 py-1.5 cursor-pointer focus:outline-primary dark:focus:outline-primary-light gap-x-3 rounded-xl hover:bg-gray-600/5 dark:hover:bg-gray-200/5 text-gray-700 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300" style="padding-left:1rem" href="/documentation/features/history"><div class="flex-1 flex items-center space-x-2.5"><div class="">History</div></div></a></li><li id="/documentation/features/spotlight" class="relative scroll-m-4 first:scroll-m-20" data-title="Spotlight"><a class="group flex items-center pr-3 py-1.5 cursor-pointer focus:outline-primary dark:focus:outline-primary-light gap-x-3 rounded-xl hover:bg-gray-600/5 dark:hover:bg-gray-200/5 text-gray-700 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300" style="padding-left:1rem" href="/documentation/features/spotlight"><div class="flex-1 flex items-center space-x-2.5"><div class="">Spotlight</div></div></a></li><li id="/documentation/features/shortcuts" class="relative scroll-m-4 first:scroll-m-20" data-title="Shortcuts"><a class="group flex items-center pr-3 py-1.5 cursor-pointer focus:outline-primary dark:focus:outline-primary-light gap-x-3 rounded-xl hover:bg-gray-600/5 dark:hover:bg-gray-200/5 text-gray-700 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300" style="padding-left:1rem" href="/documentation/features/shortcuts"><div class="flex-1 flex items-center space-x-2.5"><div class="">Shortcuts</div></div></a></li><li id="/documentation/features/scripts" class="relative scroll-m-4 first:scroll-m-20" data-title="Scripts"><a class="group flex items-center pr-3 py-1.5 cursor-pointer focus:outline-primary dark:focus:outline-primary-light gap-x-3 rounded-xl hover:bg-gray-600/5 dark:hover:bg-gray-200/5 text-gray-700 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300" style="padding-left:1rem" href="/documentation/features/scripts"><div class="flex-1 flex items-center space-x-2.5"><div class="">Scripts</div></div></a></li><li id="/documentation/features/importer" class="relative scroll-m-4 first:scroll-m-20" data-title="Importer"><a class="group flex items-center pr-3 py-1.5 cursor-pointer focus:outline-primary dark:focus:outline-primary-light gap-x-3 rounded-xl hover:bg-gray-600/5 dark:hover:bg-gray-200/5 text-gray-700 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300" style="padding-left:1rem" href="/documentation/features/importer"><div class="flex-1 flex items-center space-x-2.5"><div class="">Importer</div></div></a></li><li id="/documentation/features/customization" class="relative scroll-m-4 first:scroll-m-20" data-title="Customization"><a class="group flex items-center pr-3 py-1.5 cursor-pointer focus:outline-primary dark:focus:outline-primary-light gap-x-3 rounded-xl hover:bg-gray-600/5 dark:hover:bg-gray-200/5 text-gray-700 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300" style="padding-left:1rem" href="/documentation/features/customization"><div class="flex-1 flex items-center space-x-2.5"><div class="">Customization</div></div></a></li><li id="/documentation/features/inspections" class="relative scroll-m-4 first:scroll-m-20" data-title="Inspections"><a class="group flex items-center pr-3 py-1.5 cursor-pointer focus:outline-primary dark:focus:outline-primary-light gap-x-3 rounded-xl hover:bg-gray-600/5 dark:hover:bg-gray-200/5 text-gray-700 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300" style="padding-left:1rem" href="/documentation/features/inspections"><div class="flex-1 flex items-center space-x-2.5"><div class="">Inspections</div></div></a></li><li id="/documentation/features/cookies" class="relative scroll-m-4 first:scroll-m-20" data-title="Cookies"><a class="group flex items-center pr-3 py-1.5 cursor-pointer focus:outline-primary dark:focus:outline-primary-light gap-x-3 rounded-xl hover:bg-gray-600/5 dark:hover:bg-gray-200/5 text-gray-700 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300" style="padding-left:1rem" href="/documentation/features/cookies"><div class="flex-1 flex items-center space-x-2.5"><div class="">Cookies</div></div></a></li><li id="/documentation/features/client-certificate" class="relative scroll-m-4 first:scroll-m-20" data-title="Client Certificate"><a class="group flex items-center pr-3 py-1.5 cursor-pointer focus:outline-primary dark:focus:outline-primary-light gap-x-3 rounded-xl hover:bg-gray-600/5 dark:hover:bg-gray-200/5 text-gray-700 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300" style="padding-left:1rem" href="/documentation/features/client-certificate"><div class="flex-1 flex items-center space-x-2.5"><div class="">Client Certificate</div></div></a></li><li id="/documentation/features/snippets" class="relative scroll-m-4 first:scroll-m-20" data-title="Code Snippets"><a class="group flex items-center pr-3 py-1.5 cursor-pointer focus:outline-primary dark:focus:outline-primary-light gap-x-3 rounded-xl hover:bg-gray-600/5 dark:hover:bg-gray-200/5 text-gray-700 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300" style="padding-left:1rem" href="/documentation/features/snippets"><div class="flex-1 flex items-center space-x-2.5"><div class="">Code Snippets</div></div></a></li><li id="/documentation/features/interceptor" class="relative scroll-m-4 first:scroll-m-20" data-title="Interceptor"><a class="group flex items-center pr-3 py-1.5 cursor-pointer focus:outline-primary dark:focus:outline-primary-light gap-x-3 rounded-xl hover:bg-gray-600/5 dark:hover:bg-gray-200/5 text-gray-700 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300" style="padding-left:1rem" href="/documentation/features/interceptor"><div class="flex-1 flex items-center space-x-2.5"><div class="">Interceptor</div></div></a></li><li id="/documentation/features/context-menu" class="relative scroll-m-4 first:scroll-m-20" data-title="Context Menu"><a class="group flex items-center pr-3 py-1.5 cursor-pointer focus:outline-primary dark:focus:outline-primary-light gap-x-3 rounded-xl hover:bg-gray-600/5 dark:hover:bg-gray-200/5 text-gray-700 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300" style="padding-left:1rem" href="/documentation/features/context-menu"><div class="flex-1 flex items-center space-x-2.5"><div class="">Context Menu</div></div></a></li><li id="/documentation/features/widgets" class="relative scroll-m-4 first:scroll-m-20" data-title="Widgets"><a class="group flex items-center pr-3 py-1.5 cursor-pointer focus:outline-primary dark:focus:outline-primary-light gap-x-3 rounded-xl hover:bg-gray-600/5 dark:hover:bg-gray-200/5 text-gray-700 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300" style="padding-left:1rem" href="/documentation/features/widgets"><div class="flex-1 flex items-center space-x-2.5"><div class="">Widgets</div></div></a></li><li id="/documentation/features/authorization" class="relative scroll-m-4 first:scroll-m-20" data-title="Authorization"><a class="group flex items-center pr-3 py-1.5 cursor-pointer focus:outline-primary dark:focus:outline-primary-light gap-x-3 rounded-xl hover:bg-gray-600/5 dark:hover:bg-gray-200/5 text-gray-700 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300" style="padding-left:1rem" href="/documentation/features/authorization"><div class="flex-1 flex items-center space-x-2.5"><div class="">Authorization</div></div></a></li><li id="/documentation/features/ai-features" class="relative scroll-m-4 first:scroll-m-20" data-title="AI Features"><a class="group flex items-center pr-3 py-1.5 cursor-pointer focus:outline-primary dark:focus:outline-primary-light gap-x-3 rounded-xl hover:bg-gray-600/5 dark:hover:bg-gray-200/5 text-gray-700 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300" style="padding-left:1rem" href="/documentation/features/ai-features"><div class="flex-1 flex items-center space-x-2.5"><div class="">AI Features</div></div></a></li><li id="/documentation/features/rest-api-testing" class="relative scroll-m-4 first:scroll-m-20" data-title="RESTful API"><a class="group flex items-center pr-3 py-1.5 cursor-pointer focus:outline-primary dark:focus:outline-primary-light gap-x-3 rounded-xl hover:bg-gray-600/5 dark:hover:bg-gray-200/5 text-gray-700 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300" style="padding-left:1rem" href="/documentation/features/rest-api-testing"><div class="flex-1 flex items-center space-x-2.5"><div class="">RESTful API</div></div></a></li><li id="/documentation/features/graphql-api-testing" class="relative scroll-m-4 first:scroll-m-20" data-title="GraphQL API"><a class="group flex items-center pr-3 py-1.5 cursor-pointer focus:outline-primary dark:focus:outline-primary-light gap-x-3 rounded-xl hover:bg-gray-600/5 dark:hover:bg-gray-200/5 text-gray-700 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300" style="padding-left:1rem" href="/documentation/features/graphql-api-testing"><div class="flex-1 flex items-center space-x-2.5"><div class="">GraphQL API</div></div></a></li><li id="/documentation/features/realtime-api-testing" class="relative scroll-m-4 first:scroll-m-20" data-title="Realtime API"><a class="group flex items-center pr-3 py-1.5 cursor-pointer focus:outline-primary dark:focus:outline-primary-light gap-x-3 rounded-xl hover:bg-gray-600/5 dark:hover:bg-gray-200/5 text-gray-700 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300" style="padding-left:1rem" href="/documentation/features/realtime-api-testing"><div class="flex-1 flex items-center space-x-2.5"><div class="">Realtime API</div></div></a></li></ul></div><div class="mt-6 lg:mt-8"><div class="sidebar-group-header flex items-center gap-2.5 pl-4 mb-3.5 lg:mb-2.5 font-semibold text-gray-900 dark:text-gray-200"><h5 id="sidebar-title">Self-Host</h5></div><ul id="sidebar-group"><li id="/documentation/self-host/getting-started" class="relative scroll-m-4 first:scroll-m-20" data-title="Getting started"><a class="group flex items-center pr-3 py-1.5 cursor-pointer focus:outline-primary dark:focus:outline-primary-light gap-x-3 rounded-xl hover:bg-gray-600/5 dark:hover:bg-gray-200/5 text-gray-700 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300" style="padding-left:1rem" href="/documentation/self-host/getting-started"><div class="flex-1 flex items-center space-x-2.5"><div class="">Getting started</div></div></a></li><li data-title="Community Edition" data-group-tag=""><div class="group flex items-center pr-3 py-1.5 cursor-pointer focus:outline-primary dark:focus:outline-primary-light gap-x-3 rounded-xl hover:bg-gray-600/5 dark:hover:bg-gray-200/5 text-gray-700 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300" style="padding-left:1rem"><div class="">Community Edition</div><svg width="8" height="24" viewBox="0 -9 3 24" class="transition-transform text-gray-400 overflow-visible group-hover:text-gray-600 dark:text-gray-600 dark:group-hover:text-gray-400 -mr-0.5"><path d="M0 0L3 3L0 6" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"></path></svg></div></li><li data-title="Enterprise Edition" data-group-tag=""><div class="group flex items-center pr-3 py-1.5 cursor-pointer focus:outline-primary dark:focus:outline-primary-light gap-x-3 rounded-xl hover:bg-gray-600/5 dark:hover:bg-gray-200/5 text-gray-700 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300" style="padding-left:1rem"><div class="">Enterprise Edition</div><svg width="8" height="24" viewBox="0 -9 3 24" class="transition-transform text-gray-400 overflow-visible group-hover:text-gray-600 dark:text-gray-600 dark:group-hover:text-gray-400 -mr-0.5"><path d="M0 0L3 3L0 6" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"></path></svg></div></li></ul></div><div class="mt-6 lg:mt-8"><div class="sidebar-group-header flex items-center gap-2.5 pl-4 mb-3.5 lg:mb-2.5 font-semibold text-gray-900 dark:text-gray-200"><h5 id="sidebar-title">Resources</h5></div><ul id="sidebar-group"><li id="/documentation/community" class="relative scroll-m-4 first:scroll-m-20" data-title="Community"><a class="group flex items-center pr-3 py-1.5 cursor-pointer focus:outline-primary dark:focus:outline-primary-light gap-x-3 rounded-xl hover:bg-gray-600/5 dark:hover:bg-gray-200/5 text-gray-700 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300" style="padding-left:1rem" href="/documentation/community"><div class="flex-1 flex items-center space-x-2.5"><div class="">Community</div></div></a></li><li id="/documentation/contributors" class="relative scroll-m-4 first:scroll-m-20" data-title="Contributors"><a class="group flex items-center pr-3 py-1.5 cursor-pointer focus:outline-primary dark:focus:outline-primary-light gap-x-3 rounded-xl hover:bg-gray-600/5 dark:hover:bg-gray-200/5 text-gray-700 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300" style="padding-left:1rem" href="/documentation/contributors"><div class="flex-1 flex items-center space-x-2.5"><div class="">Contributors</div></div></a></li><li id="/documentation/changelog" class="relative scroll-m-4 first:scroll-m-20" data-title="Changelog"><a class="group flex items-center pr-3 py-1.5 cursor-pointer focus:outline-primary dark:focus:outline-primary-light gap-x-3 rounded-xl hover:bg-gray-600/5 dark:hover:bg-gray-200/5 text-gray-700 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300" style="padding-left:1rem" href="/documentation/changelog"><div class="flex-1 flex items-center space-x-2.5"><div class="">Changelog</div></div></a></li><li id="/documentation/develop" class="relative scroll-m-4 first:scroll-m-20" data-title="Develop"><a class="group flex items-center pr-3 py-1.5 cursor-pointer focus:outline-primary dark:focus:outline-primary-light gap-x-3 rounded-xl hover:bg-gray-600/5 dark:hover:bg-gray-200/5 text-gray-700 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300" style="padding-left:1rem" href="/documentation/develop"><div class="flex-1 flex items-center space-x-2.5"><div class="">Develop</div></div></a></li><li id="/documentation/i18n" class="relative scroll-m-4 first:scroll-m-20" data-title="i18n"><a class="group flex items-center pr-3 py-1.5 cursor-pointer focus:outline-primary dark:focus:outline-primary-light gap-x-3 rounded-xl hover:bg-gray-600/5 dark:hover:bg-gray-200/5 text-gray-700 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300" style="padding-left:1rem" href="/documentation/i18n"><div class="flex-1 flex items-center space-x-2.5"><div class="">i18n</div></div></a></li></ul></div></div></div></div></div><div id="content-container"><script>(self.__next_s=self.__next_s||[]).push([0,{"children":"document.documentElement.setAttribute('data-page-mode', 'none');","id":"_mintlify-page-mode-script"}])</script><script>(self.__next_s=self.__next_s||[]).push([0,{"suppressHydrationWarning":true,"children":"(function m(a,b){if(!document.getElementById(\"footer\")?.classList.contains(\"advanced-footer\")||\"maple\"===b||\"willow\"===b||\"almond\"===b)return;let c=document.documentElement.getAttribute(\"data-page-mode\"),d=document.getElementById(\"navbar\"),e=document.getElementById(\"sidebar\"),f=document.getElementById(\"footer\"),g=document.getElementById(\"table-of-contents-content\");if(!f||\"center\"===c)return;let h=f.getBoundingClientRect().top,i=window.innerHeight-h;e&&(i>0?(e.style.top=`-${i}px`,e.style.height=`${window.innerHeight}px`):(e.style.top=`${a}rem`,e.style.height=\"auto\")),g&&d&&(i>0?g.style.top=\"custom\"===c?`${d.clientHeight-i}px`:`${40+d.clientHeight-i}px`:g.style.top=\"\")})(\n  (function l(a,b,c){let d=document.documentElement.getAttribute(\"data-banner-state\"),e=2.5*!!(null!=d?\"visible\"===d:b),f=3*!!a,g=4,h=e+g+f;switch(c){case\"mint\":case\"palm\":break;case\"aspen\":f=2.5*!!a,g=3.5,h=e+f+g;break;case\"linden\":g=4,h=e+g;break;case\"almond\":g=3.5,h=e+g}return h})(false, false, \"mint\"),\n  \"mint\",\n)","id":"_mintlify-footer-and-sidebar-scroll-script"}])</script><span class="fixed inset-0 bg-background-light dark:bg-background-dark -z-10 pointer-events-none"></span><style>.mint-pointer-events-none {
    pointer-events: none
}
.mint-mt-1 {
    margin-top: 0.25rem
}
.mint-mt-4 {
    margin-top: 1rem
}
.mint-block {
    display: block
}
.mint-flex {
    display: flex
}
.mint-hidden {
    display: none
}
.mint-w-full {
    width: 100%
}
.mint-cursor-pointer {
    cursor: pointer
}
.mint-items-center {
    align-items: center
}
.mint-space-x-1 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(0.25rem * var(--tw-space-x-reverse));
    margin-left: calc(0.25rem * calc(1 - var(--tw-space-x-reverse)))
}
.mint-rounded-md {
    border-radius: 0.375rem
}
.mint-border-b {
    border-bottom-width: 1px
}
.mint-border-gray-500 {
    --tw-border-opacity: 1;
    border-color: rgb(107 114 128 / var(--tw-border-opacity))
}
.mint-px-4 {
    padding-left: 1rem;
    padding-right: 1rem
}
.mint-py-2 {
    padding-top: 0.5rem;
    padding-bottom: 0.5rem
}
.mint-pb-8 {
    padding-bottom: 2rem
}
.mint-text-sm {
    font-size: 0.875rem;
    line-height: 1.25rem
}
.mint-font-bold {
    font-weight: 700
}
.mint-font-semibold {
    font-weight: 600
}
.mint-leading-6 {
    line-height: 1.5rem
}
.mint-text-gray-600 {
    --tw-text-opacity: 1;
    color: rgb(75 85 99 / var(--tw-text-opacity))
}
.mint-text-gray-900 {
    --tw-text-opacity: 1;
    color: rgb(17 24 39 / var(--tw-text-opacity))
}
.mint-text-white {
    --tw-text-opacity: 1;
    color: rgb(255 255 255 / var(--tw-text-opacity))
}
.mint-group:hover .group-hover\:mint-opacity-\[0\.9\] {
    opacity: 0.9
}
.dark\:mint-block:is(.dark *) {
    display: block
}
.dark\:mint-hidden:is(.dark *) {
    display: none
}
.dark\:mint-border-gray-800:is(.dark *) {
    --tw-border-opacity: 1;
    border-color: rgb(31 41 55 / var(--tw-border-opacity))
}
.dark\:mint-text-gray-400:is(.dark *) {
    --tw-text-opacity: 1;
    color: rgb(156 163 175 / var(--tw-text-opacity))
}
.dark\:mint-text-white:is(.dark *) {
    --tw-text-opacity: 1;
    color: rgb(255 255 255 / var(--tw-text-opacity))
}</style><div class="flex flex-row-reverse gap-12 box-border w-full pt-40 lg:pt-10"><div class="hidden xl:flex self-start sticky xl:flex-col max-w-[28rem] h-[calc(100vh-8rem)] top-[calc(6.5rem-var(--sidenav-move-up,0px))]" id="content-side-layout"><div class="z-10 hidden xl:flex pl-10 box-border w-[19rem] max-h-full" id="table-of-contents-layout"></div></div><div class="relative grow box-border flex-col w-full mx-auto px-1 lg:pl-[23.7rem] lg:-ml-12 xl:w-[calc(100%-28rem)]" id="content-area"><header id="header" class="relative"><div class="mt-0.5 space-y-2.5"><div class="flex flex-col sm:flex-row items-start sm:items-center relative gap-2"><h1 id="page-title" class="inline-block text-2xl sm:text-3xl font-bold text-gray-900 tracking-tight dark:text-gray-200">Hoppscotch Documentation</h1></div></div><div class="mt-2 text-lg prose prose-gray dark:prose-invert"><p>Find user guides, quickstarts, tutorials, use cases, code samples, and more.</p></div></header><div class="flex flex-col gap-8"></div><div class="mdx-content relative mt-8 mb-14 prose prose-gray dark:prose-invert" data-page-title="Hoppscotch Documentation" data-page-href="/index" id="content"><span aria-owns="rmiz-modal-" data-rmiz=""><span data-rmiz-content="not-found" style="visibility:visible"><picture><img data-path="images/hero-light.png" src="https://mintcdn.com/hoppscotch/WCaaGbVhL02n1fVh/images/hero-light.png?maxW=2100&amp;auto=format&amp;n=WCaaGbVhL02n1fVh&amp;q=85&amp;s=a8c4a375f790bd9e03f525d1fbf00c25" class="object-contain block dark:hidden" width="2100" height="960" decoding="async" sizes="(max-width: 840px) 100vw, (max-width: 1100px) 50vw, 33vw" srcSet="https://mintcdn.com/hoppscotch/WCaaGbVhL02n1fVh/images/hero-light.png?w=280&amp;maxW=2100&amp;auto=format&amp;n=WCaaGbVhL02n1fVh&amp;q=85&amp;s=a6689b4b445b11fae5dd26854cd55608 280w, https://mintcdn.com/hoppscotch/WCaaGbVhL02n1fVh/images/hero-light.png?w=560&amp;maxW=2100&amp;auto=format&amp;n=WCaaGbVhL02n1fVh&amp;q=85&amp;s=3cd42d5e76ed432173cf5f6614743a2d 560w, https://mintcdn.com/hoppscotch/WCaaGbVhL02n1fVh/images/hero-light.png?w=840&amp;maxW=2100&amp;auto=format&amp;n=WCaaGbVhL02n1fVh&amp;q=85&amp;s=ca9dba458ebddd4b0615b614470a397c 840w, https://mintcdn.com/hoppscotch/WCaaGbVhL02n1fVh/images/hero-light.png?w=1100&amp;maxW=2100&amp;auto=format&amp;n=WCaaGbVhL02n1fVh&amp;q=85&amp;s=cb7bc3d0182202634296ae5d35568913 1100w, https://mintcdn.com/hoppscotch/WCaaGbVhL02n1fVh/images/hero-light.png?w=1650&amp;maxW=2100&amp;auto=format&amp;n=WCaaGbVhL02n1fVh&amp;q=85&amp;s=2aca29bf3b9c9146198e4d654b86dec3 1650w, https://mintcdn.com/hoppscotch/WCaaGbVhL02n1fVh/images/hero-light.png?w=2500&amp;maxW=2100&amp;auto=format&amp;n=WCaaGbVhL02n1fVh&amp;q=85&amp;s=c3574e34dacd6adbbe4507cfaac404af 2500w" data-optimize="true" style="aspect-ratio:2100 / 960"/></picture></span></span>
<span aria-owns="rmiz-modal-" data-rmiz=""><span data-rmiz-content="not-found" style="visibility:visible"><picture><img data-path="images/hero-dark.png" src="https://mintcdn.com/hoppscotch/WCaaGbVhL02n1fVh/images/hero-dark.png?maxW=2100&amp;auto=format&amp;n=WCaaGbVhL02n1fVh&amp;q=85&amp;s=93ecd6b9755f93e6cc996629e8263d0c" class="object-contain hidden dark:block" width="2100" height="960" decoding="async" sizes="(max-width: 840px) 100vw, (max-width: 1100px) 50vw, 33vw" srcSet="https://mintcdn.com/hoppscotch/WCaaGbVhL02n1fVh/images/hero-dark.png?w=280&amp;maxW=2100&amp;auto=format&amp;n=WCaaGbVhL02n1fVh&amp;q=85&amp;s=13a3b5e7c4f7a8007d8d0a0c507da96f 280w, https://mintcdn.com/hoppscotch/WCaaGbVhL02n1fVh/images/hero-dark.png?w=560&amp;maxW=2100&amp;auto=format&amp;n=WCaaGbVhL02n1fVh&amp;q=85&amp;s=d50179abaadf70ab59c2441d7cc91d72 560w, https://mintcdn.com/hoppscotch/WCaaGbVhL02n1fVh/images/hero-dark.png?w=840&amp;maxW=2100&amp;auto=format&amp;n=WCaaGbVhL02n1fVh&amp;q=85&amp;s=88432f60bc3258b697a793dd391d0417 840w, https://mintcdn.com/hoppscotch/WCaaGbVhL02n1fVh/images/hero-dark.png?w=1100&amp;maxW=2100&amp;auto=format&amp;n=WCaaGbVhL02n1fVh&amp;q=85&amp;s=fbb5234184b8c5b41898374e3b6fce5e 1100w, https://mintcdn.com/hoppscotch/WCaaGbVhL02n1fVh/images/hero-dark.png?w=1650&amp;maxW=2100&amp;auto=format&amp;n=WCaaGbVhL02n1fVh&amp;q=85&amp;s=423205c2e845dc42ca338f784a26a569 1650w, https://mintcdn.com/hoppscotch/WCaaGbVhL02n1fVh/images/hero-dark.png?w=2500&amp;maxW=2100&amp;auto=format&amp;n=WCaaGbVhL02n1fVh&amp;q=85&amp;s=4cd0b1ac44dd0c350c78a6ca0dc24121 2500w" data-optimize="true" style="aspect-ratio:2100 / 960"/></picture></span></span>
<span data-as="p"><strong>Hoppscotch is a lightweight, web-based API development suite.</strong></span>
<span data-as="p">It was built from the ground up with ease of use and accessibility in mind providing all the functionality needed for API developers with minimalist, unobtrusive UI.</span>
<a class="link mint-group" href="/documentation/getting-started/introduction"><button class="mint-flex mint-items-center mint-space-x-1 mint-font-bold mint-px-4 mint-py-2 bg-primary-dark mint-rounded-md group-hover:mint-opacity-[0.9] mint-text-white group-hover:mint-opacity-[0.9]"><span>Get started</span></button></a>
<br/>
<br/>
<span data-as="p"><strong>Quick Start</strong></span>
<div class="card-group not-prose grid gap-x-4 sm:grid-cols-2"><a href="https://hoppscotch.io" target="_blank" rel="noreferrer" class="link mint-border-b mint-pb-8 mint-cursor-pointer mint-border-gray-500 dark:mint-border-gray-800 hover:!border-primary dark:hover:!border-primary-light"><span aria-owns="rmiz-modal-" data-rmiz=""><span data-rmiz-content="not-found" style="visibility:visible"><img class="rounded mint-block dark:mint-hidden mint-w-full mint-pointer-events-none"/></span></span><span aria-owns="rmiz-modal-" data-rmiz=""><span data-rmiz-content="not-found" style="visibility:visible"><img class="rounded mint-hidden dark:mint-block mint-w-full mint-pointer-events-none"/></span></span><h1 class="mint-mt-4 mint-font-semibold mint-text-gray-900 dark:mint-text-white">Hoppscotch Cloud</h1><h2 class="mint-mt-1 mint-text-gray-600 dark:mint-text-gray-400 mint-text-sm mint-leading-6">Everything you need to get started with Hoppscotch, hosted by us.</h2></a><a class="link mint-border-b mint-pb-8 mint-cursor-pointer mint-border-gray-500 dark:mint-border-gray-800 hover:!border-primary dark:hover:!border-primary-light" href="/documentation/self-host/getting-started"><span aria-owns="rmiz-modal-" data-rmiz=""><span data-rmiz-content="not-found" style="visibility:visible"><img class="rounded mint-block dark:mint-hidden mint-w-full mint-pointer-events-none"/></span></span><span aria-owns="rmiz-modal-" data-rmiz=""><span data-rmiz-content="not-found" style="visibility:visible"><img class="rounded mint-hidden dark:mint-block mint-w-full mint-pointer-events-none"/></span></span><h1 class="mint-mt-4 mint-font-semibold mint-text-gray-900 dark:mint-text-white">Hoppscotch Self-Host</h1><h2 class="mint-mt-1 mint-text-gray-600 dark:mint-text-gray-400 mint-text-sm mint-leading-6">Deploy your own instance of Hoppscotch on your own infrastructure.</h2></a></div></div><div class="feedback-toolbar pb-16 w-full flex flex-col gap-y-8"><div class="flex flex-col gap-4 xl:flex-col xl:gap-6 min-[1400px]:flex-row min-[1400px]:items-center md:flex-row md:justify-end"><div class="w-full flex flex-col gap-y-6"><div class="flex flex-row gap-5 items-center grow justify-between md:justify-start xl:justify-between min-[1400px]:justify-start"><p class="text-sm text-gray-600 dark:text-gray-400">Was this page helpful?</p><div class="flex flex-row gap-3 items-center"><button class="px-3.5 py-2 flex flex-row gap-3 items-center border-standard rounded-xl text-gray-600 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 bg-white/50 dark:bg-codeblock/50 hover:border-gray-500 hover:dark:border-gray-500"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" class="fill-current"><path d="M10.1187 1.08741C8.925 0.746789 7.67813 1.43741 7.3375 2.63116L7.15938 3.25616C7.04375 3.66241 6.83438 4.03741 6.55 4.34991L4.94688 6.11241C4.66875 6.41866 4.69062 6.89366 4.99687 7.17179C5.30312 7.44991 5.77813 7.42804 6.05625 7.12179L7.65938 5.35929C8.1 4.87491 8.42188 4.29679 8.6 3.66866L8.77812 3.04366C8.89062 2.64679 9.30625 2.41554 9.70625 2.52804C10.1063 2.64054 10.3344 3.05616 10.2219 3.45616L10.0437 4.08116C9.86562 4.70304 9.58437 5.29054 9.2125 5.81554C9.05 6.04366 9.03125 6.34366 9.15938 6.59366C9.2875 6.84366 9.54375 6.99991 9.825 6.99991H14C14.275 6.99991 14.5 7.22491 14.5 7.49991C14.5 7.71241 14.3656 7.89679 14.175 7.96866C13.9438 8.05616 13.7688 8.24992 13.7094 8.49054C13.65 8.73117 13.7125 8.98429 13.875 9.16866C13.9531 9.25616 14 9.37179 14 9.49991C14 9.74366 13.825 9.94679 13.5938 9.99054C13.3375 10.0405 13.1219 10.2187 13.0312 10.4624C12.9406 10.7062 12.9813 10.9843 13.1438 11.1905C13.2094 11.2749 13.25 11.3812 13.25 11.4999C13.25 11.7093 13.1187 11.8937 12.9312 11.9655C12.5719 12.1062 12.3781 12.4937 12.4812 12.8655C12.4937 12.9062 12.5 12.953 12.5 12.9999C12.5 13.2749 12.275 13.4999 12 13.4999H8.95312C8.55937 13.4999 8.17188 13.3843 7.84375 13.1655L5.91563 11.8812C5.57188 11.6499 5.10625 11.7437 4.875 12.0905C4.64375 12.4374 4.7375 12.8999 5.08437 13.1312L7.0125 14.4155C7.5875 14.7999 8.2625 15.003 8.95312 15.003H12C13.0844 15.003 13.9656 14.1405 14 13.0655C14.4563 12.6999 14.75 12.1374 14.75 11.503C14.75 11.3624 14.7344 11.228 14.7094 11.0968C15.1906 10.7312 15.5 10.153 15.5 9.50304C15.5 9.29991 15.4688 9.10304 15.4125 8.91866C15.775 8.55304 16 8.05304 16 7.49991C16 6.39679 15.1063 5.49991 14 5.49991H11.1156C11.2625 5.17491 11.3875 4.83741 11.4844 4.49366L11.6625 3.86866C12.0031 2.67491 11.3125 1.42804 10.1187 1.08741ZM1 5.99991C0.446875 5.99991 0 6.44679 0 6.99991V13.9999C0 14.553 0.446875 14.9999 1 14.9999H3C3.55313 14.9999 4 14.553 4 13.9999V6.99991C4 6.44679 3.55313 5.99991 3 5.99991H1Z"></path></svg><small class="text-sm font-normal leading-4 ">Yes</small></button><button class="px-3.5 py-2 flex flex-row gap-3 items-center border-standard rounded-xl text-gray-600 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 bg-white/50 dark:bg-codeblock/50 hover:border-gray-500 hover:dark:border-gray-500"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" class="fill-current"><path d="M10.1187 14.9124C8.925 15.253 7.67813 14.5624 7.3375 13.3687L7.15938 12.7437C7.04375 12.3374 6.83438 11.9624 6.55 11.6499L4.94688 9.8874C4.66875 9.58115 4.69062 9.10615 4.99687 8.82803C5.30312 8.5499 5.77813 8.57178 6.05625 8.87803L7.65938 10.6405C8.1 11.1249 8.42188 11.703 8.6 12.3312L8.77812 12.9562C8.89062 13.353 9.30625 13.5843 9.70625 13.4718C10.1063 13.3593 10.3344 12.9437 10.2219 12.5437L10.0437 11.9187C9.86562 11.2968 9.58437 10.7093 9.2125 10.1843C9.05 9.95615 9.03125 9.65615 9.15938 9.40615C9.2875 9.15615 9.54375 8.9999 9.825 8.9999H14C14.275 8.9999 14.5 8.7749 14.5 8.4999C14.5 8.2874 14.3656 8.10303 14.175 8.03115C13.9438 7.94365 13.7688 7.7499 13.7094 7.50928C13.65 7.26865 13.7125 7.01553 13.875 6.83115C13.9531 6.74365 14 6.62803 14 6.4999C14 6.25615 13.825 6.05303 13.5938 6.00928C13.3375 5.95928 13.1219 5.78115 13.0312 5.53428C12.9406 5.2874 12.9813 5.0124 13.1438 4.80615C13.2094 4.72178 13.25 4.61553 13.25 4.49678C13.25 4.2874 13.1187 4.10303 12.9312 4.03115C12.5719 3.89053 12.3781 3.50303 12.4812 3.13115C12.4937 3.09053 12.5 3.04365 12.5 2.99678C12.5 2.72178 12.275 2.49678 12 2.49678H8.95312C8.55937 2.49678 8.17188 2.6124 7.84375 2.83115L5.91563 4.11553C5.57188 4.34678 5.10625 4.25303 4.875 3.90615C4.64375 3.55928 4.7375 3.09678 5.08437 2.86553L7.0125 1.58115C7.5875 1.19678 8.2625 0.993652 8.95312 0.993652H12C13.0844 0.993652 13.9656 1.85615 14 2.93115C14.4563 3.29678 14.75 3.85928 14.75 4.49365C14.75 4.63428 14.7344 4.76865 14.7094 4.8999C15.1906 5.26553 15.5 5.84365 15.5 6.49365C15.5 6.69678 15.4688 6.89365 15.4125 7.07803C15.775 7.44678 16 7.94678 16 8.4999C16 9.60303 15.1063 10.4999 14 10.4999H11.1156C11.2625 10.8249 11.3875 11.1624 11.4844 11.5062L11.6625 12.1312C12.0031 13.3249 11.3125 14.5718 10.1187 14.9124ZM1 11.9999C0.446875 11.9999 0 11.553 0 10.9999V3.9999C0 3.44678 0.446875 2.9999 1 2.9999H3C3.55313 2.9999 4 3.44678 4 3.9999V10.9999C4 11.553 3.55313 11.9999 3 11.9999H1Z"></path></svg><small class="text-sm font-normal leading-4 ">No</small></button></div></div></div><div class="flex flex-row gap-3 justify-end"><a class="h-fit whitespace-nowrap px-3.5 py-2 flex flex-row gap-3 items-center border-standard rounded-xl text-gray-600 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 bg-white/50 dark:bg-codeblock/50 hover:border-gray-500 hover:dark:border-gray-500" target="_blank" rel="noopener noreferrer" href="https://github.com/hoppscotch/docs/edit/main/.mdx"><svg class="h-3.5 w-3.5 block fill-current" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path d="M58.57 323.5L362.7 19.32C387.7-5.678 428.3-5.678 453.3 19.32L492.7 58.75C495.8 61.87 498.5 65.24 500.9 68.79C517.3 93.63 514.6 127.4 492.7 149.3L188.5 453.4C187.2 454.7 185.9 455.1 184.5 457.2C174.9 465.7 163.5 471.1 151.1 475.6L30.77 511C22.35 513.5 13.24 511.2 7.03 504.1C.8198 498.8-1.502 489.7 .976 481.2L36.37 360.9C40.53 346.8 48.16 333.9 58.57 323.5L58.57 323.5zM82.42 374.4L59.44 452.6L137.6 429.6C143.1 427.7 149.8 424.2 154.6 419.5L383 191L320.1 128.1L92.51 357.4C91.92 358 91.35 358.6 90.8 359.3C86.94 363.6 84.07 368.8 82.42 374.4L82.42 374.4z"></path></svg><svg class="h-3.5 w-3.5 hidden group-hover:block fill-gray-500 dark:fill-gray-400 group-hover:fill-gray-700 dark:group-hover:fill-gray-200" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path d="M362.7 19.3L314.3 67.7 444.3 197.7l48.4-48.4c25-25 25-65.5 0-90.5L453.3 19.3c-25-25-65.5-25-90.5 0zm-71 71L58.6 323.5c-10.4 10.4-18 23.3-22.2 37.4L1 481.2C-1.5 489.7 .8 498.8 7 505s15.3 8.5 23.7 6.1l120.3-35.4c14.1-4.2 27-11.8 37.4-22.2L421.7 220.3 291.7 90.3z"></path></svg><small class="text-sm leading-4">Suggest edits</small></a><a class="h-fit whitespace-nowrap px-3.5 py-2 flex flex-row gap-3 items-center border-standard rounded-xl text-gray-600 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 bg-white/50 dark:bg-codeblock/50 hover:border-gray-500 hover:dark:border-gray-500" target="_blank" rel="noopener noreferrer" href="https://github.com/hoppscotch/docs/issues/new?title=Issue on docs&amp;body=Path: /"><svg class="h-3.5 w-3.5 block fill-current" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path d="M506.3 417l-213.3-364C284.8 39 270.4 32 256 32C241.6 32 227.2 39 218.1 53l-213.2 364C-10.59 444.9 9.851 480 42.74 480h426.6C502.1 480 522.6 445 506.3 417zM52.58 432L255.1 84.8L459.4 432H52.58zM256 337.1c-17.36 0-31.44 14.08-31.44 31.44c0 17.36 14.11 31.44 31.48 31.44s31.4-14.08 31.4-31.44C287.4 351.2 273.4 337.1 256 337.1zM232 184v96C232 293.3 242.8 304 256 304s24-10.75 24-24v-96C280 170.8 269.3 160 256 160S232 170.8 232 184z"></path></svg><svg class="h-3.5 w-3.5 hidden group-hover:block fill-gray-500 dark:fill-gray-400 group-hover:fill-gray-700 dark:group-hover:fill-gray-200" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path d="M256 32c14.2 0 27.3 7.5 34.5 19.8l216 368c7.3 12.4 7.3 27.7 .2 40.1S486.3 480 472 480H40c-14.3 0-27.6-7.7-34.7-20.1s-7-27.8 .2-40.1l216-368C228.7 39.5 241.8 32 256 32zm0 128c-13.3 0-24 10.7-24 24V296c0 13.3 10.7 24 24 24s24-10.7 24-24V184c0-13.3-10.7-24-24-24zm32 224c0-17.7-14.3-32-32-32s-32 14.3-32 32s14.3 32 32 32s32-14.3 32-32z"></path></svg><small class="text-sm leading-4">Raise issue</small></a></div></div></div><div class="fixed left-0 right-0 sm:sticky px-4 pb-6 bottom-0 pt-1 flex flex-col items-center w-full overflow-hidden z-20"><div class="chat-assistant-floating-input z-10 w-full sm:w-80 duration-300 transition [transition:transform_500ms,opacity_200ms,left_200ms,width_400ms] focus-within:w-full sm:focus-within:w-[26rem] hover:scale-100 sm:hover:scale-105 focus-within:hover:scale-100 translate-y-[100px] opacity-0"><div class="pl-5 pr-3 border border-gray-950/20 dark:border-white/20 rounded-2xl bg-background-light/90 dark:bg-background-dark/90 backdrop-blur-xl shadow-2xl shadow-gray-900/5 flex items-center justify-between focus-within:border-primary dark:focus-within:border-primary-light transition-colors duration-400"><input type="text" placeholder="Ask a question..." class="py-3 flex-1 bg-transparent text-gray-800 dark:text-gray-200 placeholder-gray-600 dark:placeholder-gray-400 outline-none text-base sm:text-sm" value=""/><button class="chat-assistant-send-button flex justify-center items-center p-1 size-7 rounded-full bg-primary/30 dark:bg-primary-dark/30" aria-label="Send message" disabled=""><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-arrow-up size-5 text-white dark:text-white"><path d="m5 12 7-7 7 7"></path><path d="M12 19V5"></path></svg></button></div></div></div><footer id="footer" class="flex gap-12 justify-between pt-10 border-t border-gray-100 sm:flex dark:border-gray-800/50 pb-28 mt-10 sm:mt-0"><div class="flex gap-6 flex-wrap"><a href="https://hoppscotch.io/discord" target="_blank" class="h-fit"><span class="sr-only">discord</span><svg class="w-5 h-5 bg-gray-400 dark:bg-gray-500 hover:bg-gray-500 dark:hover:bg-gray-400" style="-webkit-mask-image:url(https://d3gk2c5xim1je2.cloudfront.net/v6.6.0/brands/discord.svg);-webkit-mask-repeat:no-repeat;-webkit-mask-position:center;mask-image:url(https://d3gk2c5xim1je2.cloudfront.net/v6.6.0/brands/discord.svg);mask-repeat:no-repeat;mask-position:center"></svg></a><a href="https://hoppscotch.io/twitter" target="_blank" class="h-fit"><span class="sr-only">x</span><svg class="w-5 h-5 bg-gray-400 dark:bg-gray-500 hover:bg-gray-500 dark:hover:bg-gray-400" style="-webkit-mask-image:url(https://d3gk2c5xim1je2.cloudfront.net/v6.6.0/brands/x-twitter.svg);-webkit-mask-repeat:no-repeat;-webkit-mask-position:center;mask-image:url(https://d3gk2c5xim1je2.cloudfront.net/v6.6.0/brands/x-twitter.svg);mask-repeat:no-repeat;mask-position:center"></svg></a><a href="https://www.linkedin.com/company/hoppscotch/" target="_blank" class="h-fit"><span class="sr-only">linkedin</span><svg class="w-5 h-5 bg-gray-400 dark:bg-gray-500 hover:bg-gray-500 dark:hover:bg-gray-400" style="-webkit-mask-image:url(https://d3gk2c5xim1je2.cloudfront.net/v6.6.0/brands/linkedin.svg);-webkit-mask-repeat:no-repeat;-webkit-mask-position:center;mask-image:url(https://d3gk2c5xim1je2.cloudfront.net/v6.6.0/brands/linkedin.svg);mask-repeat:no-repeat;mask-position:center"></svg></a></div><div class="flex items-center justify-between"><div class="sm:flex"><a href="https://mintlify.com?utm_campaign=poweredBy&amp;utm_medium=referral&amp;utm_source=hoppscotch" target="_blank" rel="noreferrer" class="text-sm text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 text-nowrap">Powered by Mintlify</a></div></div></footer></div></div><!--$--><!--/$--><div class="z-10 fixed right-0 w-[23rem] border-l border-gray-500/5 dark:border-gray-300/[0.06] bg-background-light dark:bg-background-dark h-[calc(100vh-4rem)] top-[4em] transition-[width] duration-300 ease-in-out invisible" style="width:368px;min-width:368px;max-width:576px"><div class="absolute -left-1 top-0 bottom-0 w-1 cursor-col-resize hover:bg-gray-200/70 dark:hover:bg-white/[0.07] z-10" style="cursor:col-resize"></div><div id="chat-assistant-sheet" class="absolute inset-0 -top-px min-h-full flex flex-col overflow-hidden shrink-0 chat-assistant-sheet" aria-hidden="true"><div class="w-full flex flex-col pb-4 h-full lg:pt-3"><div class="chat-assistant-sheet-header flex items-center justify-between pb-3 px-4"><div class="flex items-center gap-2"><svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 18 18" class="size-5 text-primary dark:text-primary-light"><g fill="currentColor"><path d="M5.658,2.99l-1.263-.421-.421-1.263c-.137-.408-.812-.408-.949,0l-.421,1.263-1.263,.421c-.204,.068-.342,.259-.342,.474s.138,.406,.342,.474l1.263,.421,.421,1.263c.068,.204,.26,.342,.475,.342s.406-.138,.475-.342l.421-1.263,1.263-.421c.204-.068,.342-.259,.342-.474s-.138-.406-.342-.474Z" fill="currentColor" data-stroke="none" stroke="none"></path><polygon points="9.5 2.75 11.412 7.587 16.25 9.5 11.412 11.413 9.5 16.25 7.587 11.413 2.75 9.5 7.587 7.587 9.5 2.75" fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"></polygon></g></svg><span class="font-semibold text-gray-900 dark:text-gray-100">Assistant</span></div><div class="flex items-center gap-1"><button class="group hover:bg-gray-100 dark:hover:bg-white/10 p-1.5 rounded-lg"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-maximize2 size-[13px] text-gray-500 group-hover:text-gray-700 dark:group-hover:text-gray-300"><polyline points="15 3 21 3 21 9"></polyline><polyline points="9 21 3 21 3 15"></polyline><line x1="21" x2="14" y1="3" y2="10"></line><line x1="3" x2="10" y1="21" y2="14"></line></svg></button><button class="group hover:bg-gray-100 dark:hover:bg-white/10 p-1.5 rounded-lg"><svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg" class="size-3.5 text-gray-500 group-hover:text-gray-700 dark:group-hover:text-gray-300"><path d="M12.4444 3.55566L3.55554 12.4446" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path><path d="M3.55554 3.55566L12.4444 12.4446" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path></svg></button></div></div><div id="chat-content" class="chat-assistant-sheet-content flex-1 overflow-y-auto relative px-4"><div class="mt-4 flex flex-col items-center text-sm justify-between"><div class="mx-8 text-center text-gray-400 dark:text-gray-600 text-xs chat-assistant-disclaimer-text">Responses are generated using AI and may contain mistakes.</div><div class="flex flex-col gap-4 text-gray-800 dark:text-gray-200"></div></div></div><div class="px-4"><div class=""><div class="flex items-end gap-2 relative"><textarea id="chat-assistant-textarea" autoComplete="off" placeholder="Ask a question..." class="grow w-full px-3.5 pr-10 py-2.5 bg-background-light dark:bg-background-dark border border-gray-200 dark:border-gray-600/30 rounded-2xl focus:outline-none focus:border-primary dark:focus:border-primary-light text-gray-900 dark:text-gray-100 text-sm chat-assistant-input" style="resize:none"></textarea><button class="absolute right-2.5 bottom-[9px] flex justify-center items-center p-1 size-7 sm:size-6 rounded-full bg-primary/30 dark:bg-primary-dark/30 chat-assistant-send-button" aria-label="Send message" disabled=""><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-arrow-up size-3.5 text-white dark:text-white"><path d="m5 12 7-7 7 7"></path><path d="M12 19V5"></path></svg></button></div></div></div></div></div></div></div></div></div><script src="/mintlify-assets/_next/static/chunks/webpack-8cb96fb5dcdbae74.js" id="_R_" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0])</script><script>self.__next_f.push([1,"1:\"$Sreact.fragment\"\n2:I[47132,[],\"\"]\n3:I[55983,[\"3473\",\"static/chunks/891cff7f-2ca7d0df884db9d0.js\",\"4368\",\"static/chunks/4368-ba814e7a6fbcef8d.js\",\"7261\",\"static/chunks/7261-f7cddf0d79dec697.js\",\"3365\",\"static/chunks/3365-454992a36759b84c.js\",\"1251\",\"static/chunks/1251-9e08ab4fcf4ec130.js\",\"4518\",\"static/chunks/4518-636d4b1b91962e91.js\",\"8039\",\"static/chunks/app/error-4b003575cfba285a.js\"],\"default\",1]\n4:I[75082,[],\"\"]\n"])</script><script>self.__next_f.push([1,"5:I[85506,[\"3473\",\"static/chunks/891cff7f-2ca7d0df884db9d0.js\",\"1725\",\"static/chunks/d30757c7-6effe791c08262b1.js\",\"803\",\"static/chunks/cd24890f-549fb4ba2f588ca6.js\",\"4368\",\"static/chunks/4368-ba814e7a6fbcef8d.js\",\"7261\",\"static/chunks/7261-f7cddf0d79dec697.js\",\"7267\",\"static/chunks/7267-4ae27d995fb80e6e.js\",\"9884\",\"static/chunks/9884-7de6993c6eaf8ebe.js\",\"7417\",\"static/chunks/7417-f80268fa1bfb6ac6.js\",\"7694\",\"static/chunks/7694-be5e0baf29ec311f.js\",\"7741\",\"static/chunks/7741-6aae3511c9a349f0.js\",\"1251\",\"static/chunks/1251-9e08ab4fcf4ec130.js\",\"6795\",\"static/chunks/6795-e69b9cf0aaf2d4f2.js\",\"3484\",\"static/chunks/3484-d2378bdc4b5e4f39.js\",\"9319\",\"static/chunks/9319-bd4367696c487773.js\",\"1750\",\"static/chunks/1750-1d0d5b4e9ed03a4f.js\",\"3972\",\"static/chunks/3972-a1df414e240d549c.js\",\"409\",\"static/chunks/409-46ca51541cd3a87b.js\",\"5456\",\"static/chunks/app/%255Fsites/%5Bsubdomain%5D/(multitenant)/layout-59d5e59150e8a4e4.js\"],\"ThemeProvider\"]\n"])</script><script>self.__next_f.push([1,"6:I[89481,[\"3473\",\"static/chunks/891cff7f-2ca7d0df884db9d0.js\",\"4368\",\"static/chunks/4368-ba814e7a6fbcef8d.js\",\"7261\",\"static/chunks/7261-f7cddf0d79dec697.js\",\"1251\",\"static/chunks/1251-9e08ab4fcf4ec130.js\",\"2967\",\"static/chunks/app/%255Fsites/%5Bsubdomain%5D/not-found-646f47a67544360a.js\"],\"RecommendedPagesList\"]\nf:I[87563,[\"3473\",\"static/chunks/891cff7f-2ca7d0df884db9d0.js\",\"4368\",\"static/chunks/4368-ba814e7a6fbcef8d.js\",\"7261\",\"static/chunks/7261-f7cddf0d79dec697.js\",\"3365\",\"static/chunks/3365-454992a36759b84c.js\",\"1251\",\"static/chunks/1251-9e08ab4fcf4ec130.js\",\"4518\",\"static/chunks/4518-636d4b1b91962e91.js\",\"4219\",\"static/chunks/app/global-error-9675be8ddbcc59bd.js\"],\"default\",1]\n:HL[\"/mintlify-assets/_next/static/media/bb3ef058b751a6ad-s.p.woff2\",\"font\",{\"crossOrigin\":\"\",\"type\":\"font/woff2\"}]\n:HL[\"/mintlify-assets/_next/static/media/e4af272ccee01ff0-s.p.woff2\",\"font\",{\"crossOrigin\":\"\",\"type\":\"font/woff2\"}]\n:HL[\"/mintlify-assets/_next/static/css/ac21e023a4c8f195.css\",\"style\"]\n:HL[\"/mintlify-assets/_next/static/css/d910ce6c26d880b3.css\",\"style\"]\n:HL[\"/mintlify-assets/_next/static/css/aabfb6890a442064.css\",\"style\"]\n"])</script><script>self.__next_f.push([1,"0:{\"P\":null,\"b\":\"8cd_4nc9JJH6YJT8tTh1g\",\"p\":\"/mintlify-assets\",\"c\":[\"\",\"_sites\",\"docs.hoppscotch.io\",\"\"],\"i\":false,\"f\":[[[\"\",{\"children\":[\"%5Fsites\",{\"children\":[[\"subdomain\",\"docs.hoppscotch.io\",\"d\"],{\"children\":[\"(multitenant)\",{\"topbar\":[\"children\",{\"children\":[[\"slug\",\"\",\"oc\"],{\"children\":[\"__PAGE__\",{}]}]}],\"children\":[[\"slug\",\"\",\"oc\"],{\"children\":[\"__PAGE__\",{}]}]}]}]}]},\"$undefined\",\"$undefined\",true],[\"\",[\"$\",\"$1\",\"c\",{\"children\":[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/mintlify-assets/_next/static/css/ac21e023a4c8f195.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}],[\"$\",\"link\",\"1\",{\"rel\":\"stylesheet\",\"href\":\"/mintlify-assets/_next/static/css/d910ce6c26d880b3.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}]],[\"$\",\"html\",null,{\"suppressHydrationWarning\":true,\"lang\":\"en\",\"className\":\"__variable_8c6b06 __variable_3bbdad dark\",\"data-banner-state\":\"visible\",\"data-page-mode\":\"none\",\"children\":[[\"$\",\"head\",null,{\"children\":[[\"$\",\"script\",null,{\"type\":\"text/javascript\",\"dangerouslySetInnerHTML\":{\"__html\":\"(function(a,b,c){try{let d=localStorage.getItem(a);if(null==d)for(let c=0;c\u003clocalStorage.length;c++){let e=localStorage.key(c);if(e?.endsWith(`-${b}`)\u0026\u0026(d=localStorage.getItem(e),null!=d)){localStorage.setItem(a,d),localStorage.setItem(e,d);break}}let e=document.getElementById(\\\"banner\\\")?.innerText,f=null==d||!!e\u0026\u0026d!==e;document.documentElement.setAttribute(c,f?\\\"visible\\\":\\\"hidden\\\")}catch(a){console.error(a),document.documentElement.setAttribute(c,\\\"hidden\\\")}})(\\n  \\\"__mintlify-bannerDismissed\\\",\\n  \\\"bannerDismissed\\\",\\n  \\\"data-banner-state\\\",\\n)\"}}],[\"$\",\"link\",null,{\"rel\":\"preload\",\"href\":\"https://d4tuoctqmanu0.cloudfront.net/katex.min.css\",\"as\":\"style\"}],[\"$\",\"script\",null,{\"type\":\"text/javascript\",\"children\":\"\\n            document.addEventListener('DOMContentLoaded', () =\u003e {\\n              const link = document.querySelector('link[href=\\\"https://d4tuoctqmanu0.cloudfront.net/katex.min.css\\\"]');\\n              link.rel = 'stylesheet';\\n            });\\n          \"}]]}],[\"$\",\"body\",null,{\"children\":[[\"$\",\"$L2\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$3\",\"errorStyles\":[],\"errorScripts\":[],\"template\":[\"$\",\"$L4\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[\"$\",\"$L5\",null,{\"children\":[[\"$\",\"style\",null,{\"children\":\":root {\\n    --primary: 22 163 74;\\n    --primary-light: 74 222 128;\\n    --primary-dark: 22 101 52;\\n    --background-light: 255 255 255;\\n    --background-dark: 10 13 13;\\n    --gray-50: 243 247 245;\\n    --gray-100: 238 242 240;\\n    --gray-200: 223 227 224;\\n    --gray-300: 206 211 208;\\n    --gray-400: 159 163 160;\\n    --gray-500: 112 116 114;\\n    --gray-600: 80 84 82;\\n    --gray-700: 63 67 64;\\n    --gray-800: 38 42 39;\\n    --gray-900: 23 27 25;\\n    --gray-950: 10 15 12;\\n  }\"}],null,null,[\"$\",\"style\",null,{\"children\":\":root {\\n  --primary: 17 120 102;\\n  --primary-light: 74 222 128;\\n  --primary-dark: 22 101 52;\\n  --background-light: 255 255 255;\\n  --background-dark: 15 17 23;\\n}\"}],[\"$\",\"main\",null,{\"className\":\"h-screen bg-background-light dark:bg-background-dark text-left\",\"children\":[\"$\",\"article\",null,{\"className\":\"bg-custom bg-fixed bg-center bg-cover relative flex flex-col items-center justify-center h-full\",\"children\":[\"$\",\"div\",null,{\"className\":\"w-full max-w-xl px-10\",\"children\":[[\"$\",\"span\",null,{\"className\":\"inline-flex mb-6 rounded-full px-3 py-1 text-sm font-semibold mr-4 text-white p-1 bg-primary\",\"children\":[\"Error \",404]}],[\"$\",\"h1\",null,{\"className\":\"font-semibold mb-3 text-3xl\",\"children\":\"Page not found!\"}],[\"$\",\"p\",null,{\"className\":\"text-lg text-gray-600 dark:text-gray-400 mb-6\",\"children\":\"We couldn't find the page you were looking for\"}],[\"$\",\"$L6\",null,{}]]}]}]}]]}],[]],\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}],null]}]]}]]}],{\"children\":[\"%5Fsites\",[\"$\",\"$1\",\"c\",{\"children\":[null,[\"$\",\"$L2\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L4\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]]}],{\"children\":[[\"subdomain\",\"docs.hoppscotch.io\",\"d\"],\"$L7\",{\"children\":[\"(multitenant)\",\"$L8\",{\"topbar\":[\"children\",\"$L9\",{\"children\":[[\"slug\",\"\",\"oc\"],\"$La\",{\"children\":[\"__PAGE__\",\"$Lb\",{},null,false]},null,false]},null,false],\"children\":[[\"slug\",\"\",\"oc\"],\"$Lc\",{\"children\":[\"__PAGE__\",\"$Ld\",{},null,false]},null,false]},null,false]},null,false]},null,false]},null,false],\"$Le\",false]],\"m\":\"$undefined\",\"G\":[\"$f\",[\"$L10\",\"$L11\"]],\"s\":false,\"S\":true}\n"])</script><script>self.__next_f.push([1,"12:I[81925,[\"3473\",\"static/chunks/891cff7f-2ca7d0df884db9d0.js\",\"4368\",\"static/chunks/4368-ba814e7a6fbcef8d.js\",\"7261\",\"static/chunks/7261-f7cddf0d79dec697.js\",\"3365\",\"static/chunks/3365-454992a36759b84c.js\",\"1251\",\"static/chunks/1251-9e08ab4fcf4ec130.js\",\"4518\",\"static/chunks/4518-636d4b1b91962e91.js\",\"9249\",\"static/chunks/app/%255Fsites/%5Bsubdomain%5D/error-d83b6d8e26d6dd95.js\"],\"default\",1]\n15:I[50700,[],\"OutletBoundary\"]\n1a:I[87748,[],\"AsyncMetadataOutlet\"]\n1c:I[50700,[],\"ViewportBoundary\"]\n1e:I[50700,[],\"MetadataBoundary\"]\n1f:\"$Sreact.suspense\"\n"])</script><script>self.__next_f.push([1,"7:[\"$\",\"$1\",\"c\",{\"children\":[null,[\"$\",\"$L2\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$12\",\"errorStyles\":[],\"errorScripts\":[],\"template\":[\"$\",\"$L4\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[\"$\",\"$L5\",null,{\"children\":[[\"$\",\"style\",null,{\"children\":\":root {\\n    --primary: 22 163 74;\\n    --primary-light: 74 222 128;\\n    --primary-dark: 22 101 52;\\n    --background-light: 255 255 255;\\n    --background-dark: 10 13 13;\\n    --gray-50: 243 247 245;\\n    --gray-100: 238 242 240;\\n    --gray-200: 223 227 224;\\n    --gray-300: 206 211 208;\\n    --gray-400: 159 163 160;\\n    --gray-500: 112 116 114;\\n    --gray-600: 80 84 82;\\n    --gray-700: 63 67 64;\\n    --gray-800: 38 42 39;\\n    --gray-900: 23 27 25;\\n    --gray-950: 10 15 12;\\n  }\"}],null,null,[\"$\",\"style\",null,{\"children\":\":root {\\n  --primary: 17 120 102;\\n  --primary-light: 74 222 128;\\n  --primary-dark: 22 101 52;\\n  --background-light: 255 255 255;\\n  --background-dark: 15 17 23;\\n}\"}],[\"$\",\"main\",null,{\"className\":\"h-screen bg-background-light dark:bg-background-dark text-left\",\"children\":[\"$\",\"article\",null,{\"className\":\"bg-custom bg-fixed bg-center bg-cover relative flex flex-col items-center justify-center h-full\",\"children\":[\"$\",\"div\",null,{\"className\":\"w-full max-w-xl px-10\",\"children\":[[\"$\",\"span\",null,{\"className\":\"inline-flex mb-6 rounded-full px-3 py-1 text-sm font-semibold mr-4 text-white p-1 bg-primary\",\"children\":[\"Error \",404]}],[\"$\",\"h1\",null,{\"className\":\"font-semibold mb-3 text-3xl\",\"children\":\"Page not found!\"}],[\"$\",\"p\",null,{\"className\":\"text-lg text-gray-600 dark:text-gray-400 mb-6\",\"children\":\"We couldn't find the page you were looking for\"}],[\"$\",\"$L6\",null,{}]]}]}]}]]}],[]],\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]]}]\n"])</script><script>self.__next_f.push([1,"8:[\"$\",\"$1\",\"c\",{\"children\":[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/mintlify-assets/_next/static/css/aabfb6890a442064.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}]],\"$L13\"]}]\n9:[\"$\",\"$1\",\"c\",{\"children\":[null,[\"$\",\"$L2\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L4\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]]}]\na:[\"$\",\"$1\",\"c\",{\"children\":[null,[\"$\",\"$L2\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L4\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]]}]\nb:[\"$\",\"$1\",\"c\",{\"children\":[\"$L14\",null,[\"$\",\"$L15\",null,{\"children\":[\"$L16\",\"$L17\"]}]]}]\nc:[\"$\",\"$1\",\"c\",{\"children\":[null,[\"$\",\"$L2\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L4\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]]}]\nd:[\"$\",\"$1\",\"c\",{\"children\":[\"$L18\",null,[\"$\",\"$L15\",null,{\"children\":[\"$L19\",[\"$\",\"$L1a\",null,{\"promise\":\"$@1b\"}]]}]]}]\ne:[\"$\",\"$1\",\"h\",{\"children\":[null,[[\"$\",\"$L1c\",null,{\"children\":\"$L1d\"}],[\"$\",\"meta\",null,{\"name\":\"next-size-adjust\",\"content\":\"\"}]],[\"$\",\"$L1e\",null,{\"children\":[\"$\",\"div\",null,{\"hidden\":true,\"children\":[\"$\",\"$1f\",null,{\"fallback\":null,\"children\":\"$L20\"}]}]}]]}]\n10:[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/mintlify-assets/_next/static/css/d910ce6c26d880b3.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}]\n11:[\"$\",\"link\",\"1\",{\"rel\":\"stylesheet\",\"href\":\"/mintlify-assets/_next/static/css/ac21e023a4c8f195.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}]\n"])</script><script>self.__next_f.push([1,"16:null\n17:null\n"])</script><script>self.__next_f.push([1,"1d:[[\"$\",\"meta\",\"0\",{\"charSet\":\"utf-8\"}],[\"$\",\"meta\",\"1\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}]]\n19:null\n"])</script><script>self.__next_f.push([1,"21:I[44760,[\"3473\",\"static/chunks/891cff7f-2ca7d0df884db9d0.js\",\"1725\",\"static/chunks/d30757c7-6effe791c08262b1.js\",\"8788\",\"static/chunks/271c4271-e47f34f62bcfeead.js\",\"4368\",\"static/chunks/4368-ba814e7a6fbcef8d.js\",\"7261\",\"static/chunks/7261-f7cddf0d79dec697.js\",\"7267\",\"static/chunks/7267-4ae27d995fb80e6e.js\",\"9884\",\"static/chunks/9884-7de6993c6eaf8ebe.js\",\"7417\",\"static/chunks/7417-f80268fa1bfb6ac6.js\",\"3365\",\"static/chunks/3365-454992a36759b84c.js\",\"7694\",\"static/chunks/7694-be5e0baf29ec311f.js\",\"4960\",\"static/chunks/4960-ee8b5fbb01e1499b.js\",\"1251\",\"static/chunks/1251-9e08ab4fcf4ec130.js\",\"6795\",\"static/chunks/6795-e69b9cf0aaf2d4f2.js\",\"3484\",\"static/chunks/3484-d2378bdc4b5e4f39.js\",\"9319\",\"static/chunks/9319-bd4367696c487773.js\",\"1750\",\"static/chunks/1750-1d0d5b4e9ed03a4f.js\",\"5143\",\"static/chunks/5143-31261db986b6fb7f.js\",\"1398\",\"static/chunks/1398-89a0fd2f8a70761b.js\",\"3972\",\"static/chunks/3972-a1df414e240d549c.js\",\"457\",\"static/chunks/457-c340c72e118d74a1.js\",\"2544\",\"static/chunks/2544-93a10271db7901cd.js\",\"9841\",\"static/chunks/app/%255Fsites/%5Bsubdomain%5D/(multitenant)/%5B%5B...slug%5D%5D/page-4dde76c33c4e15c1.js\"],\"\"]\n"])</script><script>self.__next_f.push([1,"22:I[63792,[\"3473\",\"static/chunks/891cff7f-2ca7d0df884db9d0.js\",\"1725\",\"static/chunks/d30757c7-6effe791c08262b1.js\",\"803\",\"static/chunks/cd24890f-549fb4ba2f588ca6.js\",\"4368\",\"static/chunks/4368-ba814e7a6fbcef8d.js\",\"7261\",\"static/chunks/7261-f7cddf0d79dec697.js\",\"7267\",\"static/chunks/7267-4ae27d995fb80e6e.js\",\"9884\",\"static/chunks/9884-7de6993c6eaf8ebe.js\",\"7417\",\"static/chunks/7417-f80268fa1bfb6ac6.js\",\"7694\",\"static/chunks/7694-be5e0baf29ec311f.js\",\"7741\",\"static/chunks/7741-6aae3511c9a349f0.js\",\"1251\",\"static/chunks/1251-9e08ab4fcf4ec130.js\",\"6795\",\"static/chunks/6795-e69b9cf0aaf2d4f2.js\",\"3484\",\"static/chunks/3484-d2378bdc4b5e4f39.js\",\"9319\",\"static/chunks/9319-bd4367696c487773.js\",\"1750\",\"static/chunks/1750-1d0d5b4e9ed03a4f.js\",\"3972\",\"static/chunks/3972-a1df414e240d549c.js\",\"409\",\"static/chunks/409-46ca51541cd3a87b.js\",\"5456\",\"static/chunks/app/%255Fsites/%5Bsubdomain%5D/(multitenant)/layout-59d5e59150e8a4e4.js\"],\"default\"]\n"])</script><script>self.__next_f.push([1,"23:I[71197,[\"3473\",\"static/chunks/891cff7f-2ca7d0df884db9d0.js\",\"1725\",\"static/chunks/d30757c7-6effe791c08262b1.js\",\"8788\",\"static/chunks/271c4271-e47f34f62bcfeead.js\",\"4368\",\"static/chunks/4368-ba814e7a6fbcef8d.js\",\"7261\",\"static/chunks/7261-f7cddf0d79dec697.js\",\"7267\",\"static/chunks/7267-4ae27d995fb80e6e.js\",\"9884\",\"static/chunks/9884-7de6993c6eaf8ebe.js\",\"7417\",\"static/chunks/7417-f80268fa1bfb6ac6.js\",\"3365\",\"static/chunks/3365-454992a36759b84c.js\",\"7694\",\"static/chunks/7694-be5e0baf29ec311f.js\",\"4960\",\"static/chunks/4960-ee8b5fbb01e1499b.js\",\"1251\",\"static/chunks/1251-9e08ab4fcf4ec130.js\",\"6795\",\"static/chunks/6795-e69b9cf0aaf2d4f2.js\",\"3484\",\"static/chunks/3484-d2378bdc4b5e4f39.js\",\"9319\",\"static/chunks/9319-bd4367696c487773.js\",\"1750\",\"static/chunks/1750-1d0d5b4e9ed03a4f.js\",\"5143\",\"static/chunks/5143-31261db986b6fb7f.js\",\"1398\",\"static/chunks/1398-89a0fd2f8a70761b.js\",\"3972\",\"static/chunks/3972-a1df414e240d549c.js\",\"457\",\"static/chunks/457-c340c72e118d74a1.js\",\"2544\",\"static/chunks/2544-93a10271db7901cd.js\",\"9841\",\"static/chunks/app/%255Fsites/%5Bsubdomain%5D/(multitenant)/%5B%5B...slug%5D%5D/page-4dde76c33c4e15c1.js\"],\"AuthProvider\",1]\n"])</script><script>self.__next_f.push([1,"24:I[71197,[\"3473\",\"static/chunks/891cff7f-2ca7d0df884db9d0.js\",\"1725\",\"static/chunks/d30757c7-6effe791c08262b1.js\",\"8788\",\"static/chunks/271c4271-e47f34f62bcfeead.js\",\"4368\",\"static/chunks/4368-ba814e7a6fbcef8d.js\",\"7261\",\"static/chunks/7261-f7cddf0d79dec697.js\",\"7267\",\"static/chunks/7267-4ae27d995fb80e6e.js\",\"9884\",\"static/chunks/9884-7de6993c6eaf8ebe.js\",\"7417\",\"static/chunks/7417-f80268fa1bfb6ac6.js\",\"3365\",\"static/chunks/3365-454992a36759b84c.js\",\"7694\",\"static/chunks/7694-be5e0baf29ec311f.js\",\"4960\",\"static/chunks/4960-ee8b5fbb01e1499b.js\",\"1251\",\"static/chunks/1251-9e08ab4fcf4ec130.js\",\"6795\",\"static/chunks/6795-e69b9cf0aaf2d4f2.js\",\"3484\",\"static/chunks/3484-d2378bdc4b5e4f39.js\",\"9319\",\"static/chunks/9319-bd4367696c487773.js\",\"1750\",\"static/chunks/1750-1d0d5b4e9ed03a4f.js\",\"5143\",\"static/chunks/5143-31261db986b6fb7f.js\",\"1398\",\"static/chunks/1398-89a0fd2f8a70761b.js\",\"3972\",\"static/chunks/3972-a1df414e240d549c.js\",\"457\",\"static/chunks/457-c340c72e118d74a1.js\",\"2544\",\"static/chunks/2544-93a10271db7901cd.js\",\"9841\",\"static/chunks/app/%255Fsites/%5Bsubdomain%5D/(multitenant)/%5B%5B...slug%5D%5D/page-4dde76c33c4e15c1.js\"],\"DeploymentMetadataProvider\",1]\n"])</script><script>self.__next_f.push([1,"25:I[71197,[\"3473\",\"static/chunks/891cff7f-2ca7d0df884db9d0.js\",\"1725\",\"static/chunks/d30757c7-6effe791c08262b1.js\",\"8788\",\"static/chunks/271c4271-e47f34f62bcfeead.js\",\"4368\",\"static/chunks/4368-ba814e7a6fbcef8d.js\",\"7261\",\"static/chunks/7261-f7cddf0d79dec697.js\",\"7267\",\"static/chunks/7267-4ae27d995fb80e6e.js\",\"9884\",\"static/chunks/9884-7de6993c6eaf8ebe.js\",\"7417\",\"static/chunks/7417-f80268fa1bfb6ac6.js\",\"3365\",\"static/chunks/3365-454992a36759b84c.js\",\"7694\",\"static/chunks/7694-be5e0baf29ec311f.js\",\"4960\",\"static/chunks/4960-ee8b5fbb01e1499b.js\",\"1251\",\"static/chunks/1251-9e08ab4fcf4ec130.js\",\"6795\",\"static/chunks/6795-e69b9cf0aaf2d4f2.js\",\"3484\",\"static/chunks/3484-d2378bdc4b5e4f39.js\",\"9319\",\"static/chunks/9319-bd4367696c487773.js\",\"1750\",\"static/chunks/1750-1d0d5b4e9ed03a4f.js\",\"5143\",\"static/chunks/5143-31261db986b6fb7f.js\",\"1398\",\"static/chunks/1398-89a0fd2f8a70761b.js\",\"3972\",\"static/chunks/3972-a1df414e240d549c.js\",\"457\",\"static/chunks/457-c340c72e118d74a1.js\",\"2544\",\"static/chunks/2544-93a10271db7901cd.js\",\"9841\",\"static/chunks/app/%255Fsites/%5Bsubdomain%5D/(multitenant)/%5B%5B...slug%5D%5D/page-4dde76c33c4e15c1.js\"],\"DocsConfigProvider\",1]\n"])</script><script>self.__next_f.push([1,"13:[\"$\",\"$L5\",null,{\"appearance\":\"$undefined\",\"children\":[false,[\"$\",\"$L21\",null,{\"id\":\"_mintlify-banner-script\",\"strategy\":\"beforeInteractive\",\"dangerouslySetInnerHTML\":{\"__html\":\"(function m(a,b,c,d){try{let e=document.getElementById(\\\"banner\\\"),f=e?.innerText;if(!f)return void document.documentElement.setAttribute(d,\\\"hidden\\\");let g=localStorage.getItem(a),h=g!==f\u0026\u0026g!==b;null!=g\u0026\u0026(h?(localStorage.removeItem(c),localStorage.removeItem(a)):(localStorage.setItem(c,b),localStorage.setItem(a,b))),document.documentElement.setAttribute(d,!g||h?\\\"visible\\\":\\\"hidden\\\")}catch(a){console.error(a),document.documentElement.setAttribute(d,\\\"hidden\\\")}})(\\n  \\\"hoppscotch-bannerDismissed\\\",\\n  undefined,\\n  \\\"__mintlify-bannerDismissed\\\",\\n  \\\"data-banner-state\\\",\\n)\"}}],[\"$\",\"$L22\",null,{\"appId\":\"$undefined\",\"autoBoot\":true,\"children\":[\"$\",\"$L23\",null,{\"value\":{\"auth\":\"$undefined\",\"userAuth\":\"$undefined\"},\"children\":[\"$\",\"$L24\",null,{\"value\":{\"subdomain\":\"hoppscotch\",\"actualSubdomain\":\"hoppscotch\",\"gitSource\":{\"type\":\"github\",\"owner\":\"hoppscotch\",\"repo\":\"docs\",\"deployBranch\":\"main\",\"contentDirectory\":\"\",\"isPrivate\":false},\"inkeep\":{\"integrationApiKey\":\"98b7e422ac72cb30e477b23051dda47d077bc24a10461981\"},\"trieve\":{\"datasetId\":\"952c2826-d1aa-45e6-95af-67051ac04571\"},\"feedback\":{\"thumbs\":true,\"edits\":true,\"issues\":true},\"entitlements\":\"$undefined\",\"buildId\":\"68b0374e3703a9d0efb2ef73:success\",\"clientVersion\":\"0.0.1733\",\"preview\":\"$undefined\"},\"children\":[\"$\",\"$L25\",null,{\"value\":{\"mintConfig\":\"$undefined\",\"docsConfig\":{\"theme\":\"mint\",\"$schema\":\"https://mintlify.com/docs.json\",\"name\":\"Hoppscotch Documentation\",\"description\":\"Find user guides, quickstarts, tutorials, use cases, code samples, and more.\",\"colors\":{\"primary\":\"#0D9373\",\"light\":\"#07C983\",\"dark\":\"#0D9373\"},\"logo\":{\"light\":\"https://mintcdn.com/hoppscotch/WCaaGbVhL02n1fVh/logo/light.svg?maxW=24\u0026auto=format\u0026n=WCaaGbVhL02n1fVh\u0026q=85\u0026s=16bb5835da6a36c3e15623e498995666\",\"dark\":\"https://mintcdn.com/hoppscotch/WCaaGbVhL02n1fVh/logo/dark.svg?maxW=24\u0026auto=format\u0026n=WCaaGbVhL02n1fVh\u0026q=85\u0026s=0b5b10713c4319ddfb2b4280c62403ce\"},\"favicon\":\"/favicon.svg\",\"navbar\":{\"primary\":{\"type\":\"github\",\"href\":\"https://github.com/hoppscotch/hoppscotch\"}},\"navigation\":{\"anchors\":[{\"anchor\":\"Documentation\",\"icon\":\"lines-leaning\",\"groups\":[{\"group\":\"Getting started\",\"pages\":[\"documentation/getting-started/introduction\",\"documentation/getting-started/quick-start\",\"documentation/getting-started/clients\",\"documentation/getting-started/setup\",\"documentation/getting-started/troubleshooting\",{\"group\":\"RESTful API\",\"pages\":[\"documentation/getting-started/rest/creating-a-request\",\"documentation/getting-started/rest/response-handling\",\"documentation/getting-started/rest/request-parameters\",\"documentation/getting-started/rest/request-headers\",\"documentation/getting-started/rest/organizing-requests\",\"documentation/getting-started/rest/environment-variables\",\"documentation/getting-started/rest/auth-tokens\",\"documentation/getting-started/rest/uploading-data\",\"documentation/getting-started/rest/pre-request-scripts\",\"documentation/getting-started/rest/tests\"]},{\"group\":\"GraphQL API\",\"pages\":[\"documentation/getting-started/graphql/creating-a-query\",\"documentation/getting-started/graphql/using-variables\"]},{\"group\":\"Realtime API\",\"pages\":[\"documentation/getting-started/realtime/websocket\",\"documentation/getting-started/realtime/socket-io\",\"documentation/getting-started/realtime/sse\",\"documentation/getting-started/realtime/mqtt\"]}]},{\"group\":\"Clients\",\"pages\":[\"documentation/clients/web\",\"documentation/clients/desktop\",{\"group\":\"CLI\",\"pages\":[\"documentation/clients/cli/overview\",\"documentation/clients/cli/troubleshooting\"]}]},{\"group\":\"Protocols\",\"pages\":[\"documentation/protocols/rest\",\"documentation/protocols/graphql\",\"documentation/protocols/realtime\"]},{\"group\":\"Features\",\"pages\":[\"documentation/features/workspaces\",\"documentation/features/collections\",\"documentation/features/runner\",\"documentation/features/variables\",\"documentation/features/environments\",\"documentation/features/pat\",\"documentation/features/history\",\"documentation/features/spotlight\",\"documentation/features/shortcuts\",\"documentation/features/scripts\",\"documentation/features/importer\",\"documentation/features/customization\",\"documentation/features/inspections\",\"documentation/features/cookies\",\"documentation/features/client-certificate\",\"documentation/features/snippets\",\"documentation/features/interceptor\",\"documentation/features/context-menu\",\"documentation/features/widgets\",\"documentation/features/authorization\",\"documentation/features/ai-features\",\"documentation/features/rest-api-testing\",\"documentation/features/graphql-api-testing\",\"documentation/features/realtime-api-testing\"]},{\"group\":\"Self-Host\",\"pages\":[\"documentation/self-host/getting-started\",{\"group\":\"Community Edition\",\"pages\":[\"documentation/self-host/community-edition/getting-started\",\"documentation/self-host/community-edition/prerequisites\",\"documentation/self-host/community-edition/install-and-build\",\"documentation/self-host/community-edition/setup-and-access\",\"documentation/self-host/community-edition/admin-dashboard\",\"documentation/self-host/community-edition/deploy-and-upgrade\",\"documentation/self-host/community-edition/telemetry\"]},{\"group\":\"Enterprise Edition\",\"pages\":[\"documentation/self-host/enterprise-edition/getting-started\",\"documentation/self-host/enterprise-edition/prerequisites\",\"documentation/self-host/enterprise-edition/install-and-build\",\"documentation/self-host/enterprise-edition/setup-and-access\",\"documentation/self-host/enterprise-edition/admin-dashboard\",\"documentation/self-host/enterprise-edition/user-provisioning\",\"documentation/self-host/enterprise-edition/activity-logs\",\"documentation/self-host/enterprise-edition/user-groups\",\"documentation/self-host/enterprise-edition/deploy-and-upgrade\",\"documentation/self-host/enterprise-edition/telemetry\"]}]},{\"group\":\"Resources\",\"pages\":[\"documentation/community\",\"documentation/contributors\",\"documentation/changelog\",\"documentation/develop\",\"documentation/i18n\"]}]},{\"anchor\":\"Guides\",\"icon\":\"check-double\",\"groups\":[{\"group\":\"Getting started\",\"pages\":[\"guides/getting-started/introduction\"]},{\"group\":\"Articles\",\"pages\":[\"guides/articles\",\"guides/articles/set-up-opentelemetry-stack-with-docker\",\"guides/articles/audit-logs\",\"guides/articles/restful-api-testing-with-hoppscotch\",\"guides/articles/understanding-graphql\",\"guides/articles/improving-your-api-workflow\",\"guides/articles/uplifting-web-experience-with-hoppscotch-widgets\",\"guides/articles/self-host-hoppscotch-on-your-own-servers\",\"guides/articles/manage-an-enterprise-license-key\"]}]},{\"anchor\":\"Support\",\"icon\":\"question\",\"groups\":[{\"group\":\"Getting started\",\"pages\":[\"support/getting-started/introduction\",\"support/getting-started/faq\",\"support/getting-started/contact\"]},{\"group\":\"Support\",\"pages\":[\"support/solutions/community\",\"support/solutions/experts\"]},{\"group\":\"Resources\",\"pages\":[\"support/license\",\"support/code-of-conduct\",\"support/security-policy\",\"support/privacy\",\"support/terms\"]}]},{\"anchor\":\"Download\",\"icon\":\"angles-down\",\"href\":\"https://hoppscotch.com/download\"},{\"anchor\":\"Blog\",\"icon\":\"quote-left\",\"href\":\"https://hoppscotch.com/blog\"},{\"anchor\":\"Changelog\",\"icon\":\"shapes\",\"href\":\"https://github.com/hoppscotch/hoppscotch/releases\"}]},\"footer\":{\"socials\":{\"discord\":\"https://hoppscotch.io/discord\",\"x\":\"https://hoppscotch.io/twitter\",\"linkedin\":\"https://www.linkedin.com/company/hoppscotch/\"}},\"search\":{\"prompt\":\"Search\"},\"styling\":{\"eyebrows\":\"breadcrumbs\",\"codeblocks\":\"system\"},\"redirects\":[{\"source\":\"/source/path\",\"destination\":\"/destination/path\"}]},\"docsNavWithMetadata\":{\"global\":null,\"anchors\":[{\"anchor\":\"Documentation\",\"icon\":\"lines-leaning\",\"groups\":[{\"group\":\"Getting started\",\"pages\":[{\"sidebarTitle\":\"Introduction\",\"title\":\"Introduction to Hoppscotch\",\"description\":\"Open-source API development ecosystem.\",\"href\":\"/documentation/getting-started/introduction\"},{\"sidebarTitle\":\"Quick start\",\"title\":\"Quick start\",\"description\":\"Get started with Hoppscotch.\",\"href\":\"/documentation/getting-started/quick-start\"},{\"sidebarTitle\":\"Clients\",\"title\":\"Hoppscotch Clients\",\"description\":\"Access Hoppscotch from anywhere.\",\"href\":\"/documentation/getting-started/clients\"},{\"sidebarTitle\":\"Setup\",\"title\":\"Setup Hoppscotch\",\"description\":\"Learn how to setup Hoppscotch.\",\"href\":\"/documentation/getting-started/setup\"},{\"sidebarTitle\":\"Troubleshooting\",\"title\":\"Troubleshooting\",\"description\":\"Facing issues with Hoppscotch? Check out this page to resolve them.\",\"href\":\"/documentation/getting-started/troubleshooting\"},{\"group\":\"RESTful API\",\"pages\":[{\"sidebarTitle\":\"Creating a request\",\"title\":\"Creating a request\",\"description\":\"Learn how to create a request in Hoppscotch.\",\"href\":\"/documentation/getting-started/rest/creating-a-request\"},{\"sidebarTitle\":\"Response handling\",\"title\":\"Response handling\",\"description\":\"View, interpret and manage responses from your API requests.\",\"href\":\"/documentation/getting-started/rest/response-handling\"},{\"sidebarTitle\":\"Request parameters\",\"title\":\"Request parameters\",\"description\":\"Request parameters help you to filter and request specific data from an API endpoint.\",\"href\":\"/documentation/getting-started/rest/request-parameters\"},{\"sidebarTitle\":\"Request headers\",\"title\":\"Request headers\",\"description\":\"Request Headers allow you to pass additional information with requests, helping to control how the server responds.\",\"href\":\"/documentation/getting-started/rest/request-headers\"},{\"sidebarTitle\":\"Organizing requests\",\"title\":\"Organizing requests\",\"description\":\"Organize your requests categorically for future reference or collaboration with your team using collections.\",\"href\":\"/documentation/getting-started/rest/organizing-requests\"},{\"sidebarTitle\":\"Environment variables\",\"title\":\"Environment variables\",\"description\":\"Learn how to use environment variables in your requests and scripts.\",\"href\":\"/documentation/getting-started/rest/environment-variables\"},{\"sidebarTitle\":\"Using auth tokens\",\"title\":\"Using auth tokens\",\"description\":\"Learn how to use authentication tokens in Hoppscotch.\",\"href\":\"/documentation/getting-started/rest/auth-tokens\"},{\"sidebarTitle\":\"Uploading data\",\"title\":\"Uploading data\",\"description\":\"Learn how to upload data to an API using Hoppscotch.\",\"href\":\"/documentation/getting-started/rest/uploading-data\"},{\"sidebarTitle\":\"Pre-request scripts\",\"title\":\"Pre-request scripts\",\"description\":\"Learn how to use pre-request scripts in Hoppscotch.\",\"href\":\"/documentation/getting-started/rest/pre-request-scripts\"},{\"sidebarTitle\":\"Tests\",\"title\":\"Tests\",\"description\":\"Tests are executed after a response is received from the server. You can add multiple tests to a request. You can add tests to both requests saved and not saved in a collection.\",\"href\":\"/documentation/getting-started/rest/tests\"}]},{\"group\":\"GraphQL API\",\"pages\":[{\"sidebarTitle\":\"Running a simple query\",\"title\":\"Running a simple query\",\"description\":\"Learn how to run a simple query using Hoppscotch.\",\"href\":\"/documentation/getting-started/graphql/creating-a-query\"},{\"sidebarTitle\":\"Using variables\",\"title\":\"Using variables in a GraphQL query\",\"description\":\"Learn how to use variables in a GraphQL query.\",\"href\":\"/documentation/getting-started/graphql/using-variables\"}]},{\"group\":\"Realtime API\",\"pages\":[{\"sidebarTitle\":\"Websocket\",\"title\":\"Websocket\",\"description\":\"Connect to a WebSocket and send messages.\",\"href\":\"/documentation/getting-started/realtime/websocket\"},{\"sidebarTitle\":\"Socket.IO\",\"title\":\"Socket.IO\",\"description\":\"Learn how to use the Socket.IO testing tool in Hoppscotch.\",\"href\":\"/documentation/getting-started/realtime/socket-io\"},{\"sidebarTitle\":\"SSE\",\"title\":\"Server-Sent Events\",\"description\":\"Server-Sent Events (SSE) is a server push technology enabling a client to receive automatic updates from a server via HTTP connection.\",\"href\":\"/documentation/getting-started/realtime/sse\"},{\"sidebarTitle\":\"MQTT\",\"title\":\"MQTT\",\"description\":\"MQTT is a lightweight publish-subscribe messaging protocol.\",\"href\":\"/documentation/getting-started/realtime/mqtt\"}]}]},{\"group\":\"Clients\",\"pages\":[{\"sidebarTitle\":\"Web\",\"title\":\"Hoppscotch Web App\",\"description\":\"Build, test, and share your APIs directly in your browser.\",\"href\":\"/documentation/clients/web\"},{\"sidebarTitle\":\"Desktop\",\"title\":\"Hoppscotch Desktop App\",\"description\":\"Cross-platform desktop application that runs on macOS, Windows, and Linux.\",\"href\":\"/documentation/clients/desktop\"},{\"group\":\"CLI\",\"pages\":[{\"sidebarTitle\":\"Overview\",\"title\":\"Hoppscotch CLI\",\"description\":\"Run tests, manage automated API monitoring, and more.\",\"href\":\"/documentation/clients/cli/overview\"},{\"sidebarTitle\":\"Troubleshooting\",\"title\":\"Hoppscotch CLI Troubleshooting\",\"description\":\"Troubleshoot the CLI errors by understanding their meanings and possible causes.\",\"href\":\"/documentation/clients/cli/troubleshooting\"}]}]},{\"group\":\"Protocols\",\"pages\":[{\"sidebarTitle\":\"RESTful\",\"title\":\"RESTful\",\"description\":\"RESTful API testing with Hoppscotch.\",\"href\":\"/documentation/protocols/rest\"},{\"sidebarTitle\":\"GraphQL\",\"title\":\"GraphQL\",\"description\":\"GraphQL is a query language for APIs that queries the server and provides the client only the data that is requested by the client. GraphQL enables you to fetch data from multiple APIs in a single query thus helping you build better-performing applications.\",\"href\":\"/documentation/protocols/graphql\"},{\"sidebarTitle\":\"Realtime\",\"title\":\"Realtime\",\"description\":\"Hoppscotch's Realtime API platform helps you test your real-time APIs easily.\",\"href\":\"/documentation/protocols/realtime\"}]},{\"group\":\"Features\",\"pages\":[{\"sidebarTitle\":\"Workspaces\",\"title\":\"Workspaces\",\"description\":\"Organize your requests, collections, and environments into different workspaces. You can also invite other users to your workspace to collaborate.\",\"href\":\"/documentation/features/workspaces\"},{\"sidebarTitle\":\"Collections\",\"title\":\"Collections\",\"description\":\"Save, organize, and share your API requests with collections.\",\"href\":\"/documentation/features/collections\"},{\"sidebarTitle\":\"Runner\",\"title\":\"Runner\",\"description\":\"Iterate and execute your requests in a collection.\",\"href\":\"/documentation/features/runner\"},{\"sidebarTitle\":\"Variables\",\"title\":\"Variables\",\"description\":\"Dynamic placeholders for your API interactions.\",\"href\":\"/documentation/features/variables\"},{\"sidebarTitle\":\"Environments\",\"title\":\"Environments\",\"description\":\"Environments help you create variables that you can reuse in requests and scripts.\",\"href\":\"/documentation/features/environments\"},{\"sidebarTitle\":\"Personal Access Token\",\"title\":\"Personal Access Token\",\"description\":\"Securely connect your Hoppscotch account with other services within Hoppscotch ecosystem.\",\"href\":\"/documentation/features/pat\"},{\"sidebarTitle\":\"History\",\"title\":\"History\",\"description\":\"Store and access your previous requests and responses.\",\"href\":\"/documentation/features/history\"},{\"sidebarTitle\":\"Spotlight\",\"title\":\"Spotlight\",\"description\":\"Powerful search and command palette.\",\"href\":\"/documentation/features/spotlight\"},{\"sidebarTitle\":\"Shortcuts\",\"title\":\"Shortcuts\",\"description\":\"Keyboard shortcuts for Hoppscotch.\",\"href\":\"/documentation/features/shortcuts\"},{\"sidebarTitle\":\"Scripts\",\"title\":\"Scripts\",\"description\":\"Write pre-request scripts and build tests.\",\"href\":\"/documentation/features/scripts\"},{\"sidebarTitle\":\"Importer\",\"title\":\"Importer\",\"description\":\"Migrate your data from other tools into Hoppscotch.\",\"href\":\"/documentation/features/importer\"},{\"sidebarTitle\":\"Customization\",\"title\":\"Customization\",\"description\":\"Customize your Hoppscotch experience.\",\"href\":\"/documentation/features/customization\"},{\"sidebarTitle\":\"Inspections\",\"title\":\"Inspections\",\"description\":\"Detect and resolve configuration errors in your API requests.\",\"href\":\"/documentation/features/inspections\"},{\"sidebarTitle\":\"Cookies\",\"title\":\"Cookies\",\"description\":\"Create and modify cookies associated with a domain and send cookies with requests.\",\"href\":\"/documentation/features/cookies\"},{\"sidebarTitle\":\"Client Certificate\",\"title\":\"Client Certificate\",\"description\":\"Configure client certificates for SSL authentication in Hoppscotch.\",\"href\":\"/documentation/features/client-certificate\"},{\"sidebarTitle\":\"Code Snippets\",\"title\":\"Code Snippets\",\"description\":\"Generate code snippets for your API in a variety of languages and frameworks.\",\"href\":\"/documentation/features/snippets\"},{\"sidebarTitle\":\"Interceptor\",\"title\":\"Interceptor\",\"description\":\"Intercept and modify requests and responses.\",\"href\":\"/documentation/features/interceptor\"},{\"sidebarTitle\":\"Context Menu\",\"title\":\"Context Menu\",\"description\":\"Use context-aware menus to assist with your actions and improve your workflow.\",\"href\":\"/documentation/features/context-menu\"},{\"sidebarTitle\":\"Widgets\",\"title\":\"Widgets\",\"description\":\"Embed Hoppscotch in your website.\",\"href\":\"/documentation/features/widgets\"},{\"sidebarTitle\":\"Authorization\",\"title\":\"Authorization\",\"description\":\"Authorization is the process of verifying that a client has permission to access a resource.\",\"href\":\"/documentation/features/authorization\"},{\"sidebarTitle\":\"AI Features\",\"title\":\"AI Features\",\"description\":\"Optimize API Development and Testing Workflows with Hoppscotch using AI.\",\"href\":\"/documentation/features/ai-features\"},{\"sidebarTitle\":\"RESTful API\",\"title\":\"RESTful API Testing\",\"description\":\"Test and play around with your RESTful API endpoints.\",\"href\":\"/documentation/features/rest-api-testing\"},{\"sidebarTitle\":\"GraphQL API\",\"title\":\"GraphQL API Testing\",\"description\":\"Test and play around with GraphQL APIs.\",\"href\":\"/documentation/features/graphql-api-testing\"},{\"sidebarTitle\":\"Realtime API\",\"title\":\"Realtime API Testing\",\"description\":\"Welcome to the home of your new documentation\",\"href\":\"/documentation/features/realtime-api-testing\"}]},{\"group\":\"Self-Host\",\"pages\":[{\"sidebarTitle\":\"Getting started\",\"title\":\"Getting started\",\"description\":\"Welcome Hoppscotch Self-Host.\",\"href\":\"/documentation/self-host/getting-started\"},{\"group\":\"Community Edition\",\"pages\":[{\"sidebarTitle\":\"Getting started\",\"title\":\"Getting started\",\"description\":\"Learn how to get started with Hoppscotch Community Edition.\",\"href\":\"/documentation/self-host/community-edition/getting-started\"},{\"sidebarTitle\":\"Prerequisites\",\"title\":\"Prerequisites\",\"description\":\"Prerequisites for installing Hoppscotch on your own infrastructure.\",\"href\":\"/documentation/self-host/community-edition/prerequisites\"},{\"sidebarTitle\":\"Install and build\",\"title\":\"Install and build\",\"description\":\"Learn how to install and build Hoppscotch Community Edition.\",\"href\":\"/documentation/self-host/community-edition/install-and-build\"},{\"sidebarTitle\":\"Setup and access\",\"title\":\"Setup and access\",\"description\":\"Learn how to set up and access Hoppscotch Community Edition.\",\"href\":\"/documentation/self-host/community-edition/setup-and-access\"},{\"sidebarTitle\":\"Admin dashboard\",\"title\":\"Admin dashboard\",\"description\":\"Get started with the Hoppscotch Admin Dashboard.\",\"href\":\"/documentation/self-host/community-edition/admin-dashboard\"},{\"sidebarTitle\":\"Deploy and upgrade\",\"title\":\"Deploy and upgrade\",\"description\":\"Deploy and upgrade Hoppscotch Community Edition on your infrastructure.\",\"href\":\"/documentation/self-host/community-edition/deploy-and-upgrade\"},{\"sidebarTitle\":\"Telemetry\",\"title\":\"Telemetry\",\"description\":\"Telemetry and data sharing in Hoppscotch Community Edition\",\"href\":\"/documentation/self-host/community-edition/telemetry\"}]},{\"group\":\"Enterprise Edition\",\"pages\":[{\"sidebarTitle\":\"Getting started\",\"title\":\"Getting started\",\"description\":\"Learn how to get started with Hoppscotch Enterprise Edition.\",\"href\":\"/documentation/self-host/enterprise-edition/getting-started\"},{\"sidebarTitle\":\"Prerequisites\",\"title\":\"Prerequisites\",\"description\":\"Prerequisites for installing Hoppscotch on your own infrastructure.\",\"href\":\"/documentation/self-host/enterprise-edition/prerequisites\"},{\"sidebarTitle\":\"Install and build\",\"title\":\"Install and build\",\"description\":\"Learn how to install and build Hoppscotch Enterprise Edition.\",\"href\":\"/documentation/self-host/enterprise-edition/install-and-build\"},{\"sidebarTitle\":\"Setup and access\",\"title\":\"Setup and access\",\"description\":\"Learn how to set up and access Hoppscotch Enterprise Edition.\",\"href\":\"/documentation/self-host/enterprise-edition/setup-and-access\"},{\"sidebarTitle\":\"Admin dashboard\",\"title\":\"Admin dashboard\",\"description\":\"Get started with the Hoppscotch Admin Dashboard.\",\"href\":\"/documentation/self-host/enterprise-edition/admin-dashboard\"},{\"sidebarTitle\":\"User provisioning\",\"title\":\"SCIM Integration for User Provisioning\",\"description\":\"Manage users efficiently with SCIM provisioning in Hoppscotch.\",\"href\":\"/documentation/self-host/enterprise-edition/user-provisioning\"},{\"sidebarTitle\":\"Activity logs\",\"title\":\"Activity logs\",\"description\":\"Track all changes made to collections and requests within a workspace, and monitor user interactions through Activity Logs.\",\"href\":\"/documentation/self-host/enterprise-edition/activity-logs\"},{\"sidebarTitle\":\"User groups\",\"title\":\"User groups\",\"description\":\"User groups allow admins to manage user permissions and roles in a shared workspace, providing a structured way to control access and actions within the workspace.\",\"href\":\"/documentation/self-host/enterprise-edition/user-groups\"},{\"sidebarTitle\":\"Deploy and upgrade\",\"title\":\"Deploy and upgrade\",\"description\":\"Deploy and upgrade Hoppscotch Enterprise Edition on your infrastructure.\",\"href\":\"/documentation/self-host/enterprise-edition/deploy-and-upgrade\"},{\"sidebarTitle\":\"Telemetry\",\"title\":\"Telemetry\",\"description\":\"Telemetry and data sharing in Hoppscotch Enterprise Edition\",\"href\":\"/documentation/self-host/enterprise-edition/telemetry\"}]}]},{\"group\":\"Resources\",\"pages\":[{\"sidebarTitle\":\"Community\",\"title\":\"Community\",\"description\":\"Join our open communities and forums.\",\"href\":\"/documentation/community\"},{\"sidebarTitle\":\"Contributors\",\"title\":\"Contributors\",\"description\":\"Hoppscotch exists thanks to the awesome people who contribute to it.\",\"href\":\"/documentation/contributors\"},{\"sidebarTitle\":\"Changelog\",\"title\":\"Changelog\",\"description\":\"New updates and improvements to Hoppscotch.\",\"href\":\"/documentation/changelog\"},{\"sidebarTitle\":\"Develop\",\"title\":\"Develop\",\"description\":\"Learn how to contribute to Hoppscotch.\",\"href\":\"/documentation/develop\"},{\"sidebarTitle\":\"i18n\",\"title\":\"i18n\",\"description\":\"Internationalization and localization.\",\"href\":\"/documentation/i18n\"}]}]},{\"anchor\":\"Guides\",\"icon\":\"check-double\",\"groups\":[{\"group\":\"Getting started\",\"pages\":[{\"sidebarTitle\":\"Introduction\",\"title\":\"Introduction\",\"description\":\"Guides and tutorials to help you get started with Hoppscotch.\",\"href\":\"/guides/getting-started/introduction\"}]},{\"group\":\"Articles\",\"pages\":[{\"sidebarTitle\":\"Index\",\"title\":\"Index\",\"description\":\"Complete list of articles and guides for Hoppscotch.\",\"href\":\"/guides/articles\"},{\"sidebarTitle\":\"OpenTelemetry Integration\",\"title\":\"Set Up OpenTelemetry Stack with Docker\",\"description\":\"A guide to setting up the OpenTelemetry stack using Docker.\",\"href\":\"/guides/articles/set-up-opentelemetry-stack-with-docker\"},{\"sidebarTitle\":\"Audit Logs in Hoppscotch\",\"title\":\"Audit Logs in Hoppscotch\",\"description\":\"Learn about the audit logs in Hoppscotch Enterprise.\",\"href\":\"/guides/articles/audit-logs\"},{\"sidebarTitle\":\"RESTful API Testing\",\"title\":\"RESTful API testing with Hoppscotch\",\"description\":\"Learn how to use Hoppscotch to test your RESTful APIs.\",\"href\":\"/guides/articles/restful-api-testing-with-hoppscotch\"},{\"sidebarTitle\":\"GraphQL API Testing\",\"title\":\"Understanding the GraphQL Schema on Hoppscotch\",\"description\":\"Learn how to use the GraphQL schema on Hoppscotch.\",\"href\":\"/guides/articles/understanding-graphql\"},{\"sidebarTitle\":\"API Workflow\",\"title\":\"Improving your API workflow\",\"description\":\"Learn how to improve your API workflow with Hoppscotch.\",\"href\":\"/guides/articles/improving-your-api-workflow\"},{\"sidebarTitle\":\"Hoppscotch Widgets\",\"title\":\"Uplifting web experience with Hoppscotch Widgets\",\"description\":\"Create and add interactive and dynamic elements to your website using Hoppscotch Widgets.\",\"href\":\"/guides/articles/uplifting-web-experience-with-hoppscotch-widgets\"},{\"sidebarTitle\":\"Self-Host Hoppscotch\",\"title\":\"Self-Host Hoppscotch on your own servers\",\"description\":\"Set up Hoppscotch on your servers for complete control and customization.\",\"href\":\"/guides/articles/self-host-hoppscotch-on-your-own-servers\"},{\"sidebarTitle\":\"Enterprise License Key\",\"title\":\"Managing your Enterprise License Key\",\"description\":\"Learn how to manage your Hoppscotch Enterprise Edition license key.\",\"href\":\"/guides/articles/manage-an-enterprise-license-key\"}]}]},{\"anchor\":\"Support\",\"icon\":\"question\",\"groups\":[{\"group\":\"Getting started\",\"pages\":[{\"sidebarTitle\":\"Introduction\",\"title\":\"Introduction\",\"description\":\"We're here to help you get started with Hoppscotch.\",\"href\":\"/support/getting-started/introduction\"},{\"sidebarTitle\":\"FAQ\",\"title\":\"Frequently Asked Questions\",\"description\":\"These are the most frequently asked questions about Hoppscotch.\",\"href\":\"/support/getting-started/faq\"},{\"sidebarTitle\":\"Contact\",\"title\":\"Contact\",\"description\":\"Contact us to get technical support, report a bug, suggest a new feature or improvement, or join our community.\",\"href\":\"/support/getting-started/contact\"}]},{\"group\":\"Support\",\"pages\":[{\"sidebarTitle\":\"Community\",\"title\":\"Community\",\"description\":\"Hoppscotch is an open-source project. Community is at the heart of everything we do.\",\"href\":\"/support/solutions/community\"},{\"sidebarTitle\":\"Experts\",\"title\":\"Hoppscotch Experts\",\"description\":\"Get help from our experts.\",\"href\":\"/support/solutions/experts\"}]},{\"group\":\"Resources\",\"pages\":[{\"sidebarTitle\":\"License\",\"title\":\"License\",\"description\":\"Hoppscotch is licensed under MIT License.\",\"href\":\"/support/license\"},{\"sidebarTitle\":\"Code of conduct\",\"title\":\"Code of conduct\",\"description\":\"Code of conduct for Hoppscotch.\",\"href\":\"/support/code-of-conduct\"},{\"sidebarTitle\":\"Security policy\",\"title\":\"Security policy\",\"description\":\"Security procedures and general policies for Hoppscotch.\",\"href\":\"/support/security-policy\"},{\"sidebarTitle\":\"Privacy policy\",\"title\":\"Privacy policy\",\"description\":\"Privacy policy for Hoppscotch.\",\"href\":\"/support/privacy\"},{\"sidebarTitle\":\"Terms and conditions\",\"title\":\"Terms and conditions\",\"description\":\"Terms and conditions for Hoppscotch.\",\"href\":\"/support/terms\"}]}]},{\"anchor\":\"Download\",\"icon\":\"angles-down\",\"href\":\"https://hoppscotch.com/download\"},{\"anchor\":\"Blog\",\"icon\":\"quote-left\",\"href\":\"https://hoppscotch.com/blog\"},{\"anchor\":\"Changelog\",\"icon\":\"shapes\",\"href\":\"https://github.com/hoppscotch/hoppscotch/releases\"}]},\"legacyThemeSettings\":{\"isSidePrimaryNav\":false,\"isSolidSidenav\":false,\"isTopbarGradient\":false,\"isSearchAtSidebar\":false,\"shouldUseTabsInTopNav\":false,\"sidebarStyle\":\"container\",\"rounded\":\"default\"}},\"children\":\"$L26\"}]}]}]}]]}]\n"])</script><script>self.__next_f.push([1,"27:I[74190,[\"3473\",\"static/chunks/891cff7f-2ca7d0df884db9d0.js\",\"1725\",\"static/chunks/d30757c7-6effe791c08262b1.js\",\"803\",\"static/chunks/cd24890f-549fb4ba2f588ca6.js\",\"4368\",\"static/chunks/4368-ba814e7a6fbcef8d.js\",\"7261\",\"static/chunks/7261-f7cddf0d79dec697.js\",\"7267\",\"static/chunks/7267-4ae27d995fb80e6e.js\",\"9884\",\"static/chunks/9884-7de6993c6eaf8ebe.js\",\"7417\",\"static/chunks/7417-f80268fa1bfb6ac6.js\",\"7694\",\"static/chunks/7694-be5e0baf29ec311f.js\",\"7741\",\"static/chunks/7741-6aae3511c9a349f0.js\",\"1251\",\"static/chunks/1251-9e08ab4fcf4ec130.js\",\"6795\",\"static/chunks/6795-e69b9cf0aaf2d4f2.js\",\"3484\",\"static/chunks/3484-d2378bdc4b5e4f39.js\",\"9319\",\"static/chunks/9319-bd4367696c487773.js\",\"1750\",\"static/chunks/1750-1d0d5b4e9ed03a4f.js\",\"3972\",\"static/chunks/3972-a1df414e240d549c.js\",\"409\",\"static/chunks/409-46ca51541cd3a87b.js\",\"5456\",\"static/chunks/app/%255Fsites/%5Bsubdomain%5D/(multitenant)/layout-59d5e59150e8a4e4.js\"],\"LoginButtonProvider\",1]\n"])</script><script>self.__next_f.push([1,"28:I[84922,[\"3473\",\"static/chunks/891cff7f-2ca7d0df884db9d0.js\",\"1725\",\"static/chunks/d30757c7-6effe791c08262b1.js\",\"803\",\"static/chunks/cd24890f-549fb4ba2f588ca6.js\",\"4368\",\"static/chunks/4368-ba814e7a6fbcef8d.js\",\"7261\",\"static/chunks/7261-f7cddf0d79dec697.js\",\"7267\",\"static/chunks/7267-4ae27d995fb80e6e.js\",\"9884\",\"static/chunks/9884-7de6993c6eaf8ebe.js\",\"7417\",\"static/chunks/7417-f80268fa1bfb6ac6.js\",\"7694\",\"static/chunks/7694-be5e0baf29ec311f.js\",\"7741\",\"static/chunks/7741-6aae3511c9a349f0.js\",\"1251\",\"static/chunks/1251-9e08ab4fcf4ec130.js\",\"6795\",\"static/chunks/6795-e69b9cf0aaf2d4f2.js\",\"3484\",\"static/chunks/3484-d2378bdc4b5e4f39.js\",\"9319\",\"static/chunks/9319-bd4367696c487773.js\",\"1750\",\"static/chunks/1750-1d0d5b4e9ed03a4f.js\",\"3972\",\"static/chunks/3972-a1df414e240d549c.js\",\"409\",\"static/chunks/409-46ca51541cd3a87b.js\",\"5456\",\"static/chunks/app/%255Fsites/%5Bsubdomain%5D/(multitenant)/layout-59d5e59150e8a4e4.js\"],\"SidebarLoginButtonProvider\",1]\n"])</script><script>self.__next_f.push([1,"29:I[93351,[\"3473\",\"static/chunks/891cff7f-2ca7d0df884db9d0.js\",\"1725\",\"static/chunks/d30757c7-6effe791c08262b1.js\",\"803\",\"static/chunks/cd24890f-549fb4ba2f588ca6.js\",\"4368\",\"static/chunks/4368-ba814e7a6fbcef8d.js\",\"7261\",\"static/chunks/7261-f7cddf0d79dec697.js\",\"7267\",\"static/chunks/7267-4ae27d995fb80e6e.js\",\"9884\",\"static/chunks/9884-7de6993c6eaf8ebe.js\",\"7417\",\"static/chunks/7417-f80268fa1bfb6ac6.js\",\"7694\",\"static/chunks/7694-be5e0baf29ec311f.js\",\"7741\",\"static/chunks/7741-6aae3511c9a349f0.js\",\"1251\",\"static/chunks/1251-9e08ab4fcf4ec130.js\",\"6795\",\"static/chunks/6795-e69b9cf0aaf2d4f2.js\",\"3484\",\"static/chunks/3484-d2378bdc4b5e4f39.js\",\"9319\",\"static/chunks/9319-bd4367696c487773.js\",\"1750\",\"static/chunks/1750-1d0d5b4e9ed03a4f.js\",\"3972\",\"static/chunks/3972-a1df414e240d549c.js\",\"409\",\"static/chunks/409-46ca51541cd3a87b.js\",\"5456\",\"static/chunks/app/%255Fsites/%5Bsubdomain%5D/(multitenant)/layout-59d5e59150e8a4e4.js\"],\"NavigationContextController\",1]\n"])</script><script>self.__next_f.push([1,"2a:I[80976,[\"3473\",\"static/chunks/891cff7f-2ca7d0df884db9d0.js\",\"1725\",\"static/chunks/d30757c7-6effe791c08262b1.js\",\"803\",\"static/chunks/cd24890f-549fb4ba2f588ca6.js\",\"4368\",\"static/chunks/4368-ba814e7a6fbcef8d.js\",\"7261\",\"static/chunks/7261-f7cddf0d79dec697.js\",\"7267\",\"static/chunks/7267-4ae27d995fb80e6e.js\",\"9884\",\"static/chunks/9884-7de6993c6eaf8ebe.js\",\"7417\",\"static/chunks/7417-f80268fa1bfb6ac6.js\",\"7694\",\"static/chunks/7694-be5e0baf29ec311f.js\",\"7741\",\"static/chunks/7741-6aae3511c9a349f0.js\",\"1251\",\"static/chunks/1251-9e08ab4fcf4ec130.js\",\"6795\",\"static/chunks/6795-e69b9cf0aaf2d4f2.js\",\"3484\",\"static/chunks/3484-d2378bdc4b5e4f39.js\",\"9319\",\"static/chunks/9319-bd4367696c487773.js\",\"1750\",\"static/chunks/1750-1d0d5b4e9ed03a4f.js\",\"3972\",\"static/chunks/3972-a1df414e240d549c.js\",\"409\",\"static/chunks/409-46ca51541cd3a87b.js\",\"5456\",\"static/chunks/app/%255Fsites/%5Bsubdomain%5D/(multitenant)/layout-59d5e59150e8a4e4.js\"],\"BannerProvider\"]\n"])</script><script>self.__next_f.push([1,"2b:I[99543,[\"3473\",\"static/chunks/891cff7f-2ca7d0df884db9d0.js\",\"1725\",\"static/chunks/d30757c7-6effe791c08262b1.js\",\"8788\",\"static/chunks/271c4271-e47f34f62bcfeead.js\",\"4368\",\"static/chunks/4368-ba814e7a6fbcef8d.js\",\"7261\",\"static/chunks/7261-f7cddf0d79dec697.js\",\"7267\",\"static/chunks/7267-4ae27d995fb80e6e.js\",\"9884\",\"static/chunks/9884-7de6993c6eaf8ebe.js\",\"7417\",\"static/chunks/7417-f80268fa1bfb6ac6.js\",\"3365\",\"static/chunks/3365-454992a36759b84c.js\",\"7694\",\"static/chunks/7694-be5e0baf29ec311f.js\",\"4960\",\"static/chunks/4960-ee8b5fbb01e1499b.js\",\"1251\",\"static/chunks/1251-9e08ab4fcf4ec130.js\",\"6795\",\"static/chunks/6795-e69b9cf0aaf2d4f2.js\",\"3484\",\"static/chunks/3484-d2378bdc4b5e4f39.js\",\"9319\",\"static/chunks/9319-bd4367696c487773.js\",\"1750\",\"static/chunks/1750-1d0d5b4e9ed03a4f.js\",\"5143\",\"static/chunks/5143-31261db986b6fb7f.js\",\"1398\",\"static/chunks/1398-89a0fd2f8a70761b.js\",\"3972\",\"static/chunks/3972-a1df414e240d549c.js\",\"457\",\"static/chunks/457-c340c72e118d74a1.js\",\"2544\",\"static/chunks/2544-93a10271db7901cd.js\",\"9841\",\"static/chunks/app/%255Fsites/%5Bsubdomain%5D/(multitenant)/%5B%5B...slug%5D%5D/page-4dde76c33c4e15c1.js\"],\"ScrollTopScript\",1]\n"])</script><script>self.__next_f.push([1,"2c:I[2001,[\"3473\",\"static/chunks/891cff7f-2ca7d0df884db9d0.js\",\"1725\",\"static/chunks/d30757c7-6effe791c08262b1.js\",\"803\",\"static/chunks/cd24890f-549fb4ba2f588ca6.js\",\"4368\",\"static/chunks/4368-ba814e7a6fbcef8d.js\",\"7261\",\"static/chunks/7261-f7cddf0d79dec697.js\",\"7267\",\"static/chunks/7267-4ae27d995fb80e6e.js\",\"9884\",\"static/chunks/9884-7de6993c6eaf8ebe.js\",\"7417\",\"static/chunks/7417-f80268fa1bfb6ac6.js\",\"7694\",\"static/chunks/7694-be5e0baf29ec311f.js\",\"7741\",\"static/chunks/7741-6aae3511c9a349f0.js\",\"1251\",\"static/chunks/1251-9e08ab4fcf4ec130.js\",\"6795\",\"static/chunks/6795-e69b9cf0aaf2d4f2.js\",\"3484\",\"static/chunks/3484-d2378bdc4b5e4f39.js\",\"9319\",\"static/chunks/9319-bd4367696c487773.js\",\"1750\",\"static/chunks/1750-1d0d5b4e9ed03a4f.js\",\"3972\",\"static/chunks/3972-a1df414e240d549c.js\",\"409\",\"static/chunks/409-46ca51541cd3a87b.js\",\"5456\",\"static/chunks/app/%255Fsites/%5Bsubdomain%5D/(multitenant)/layout-59d5e59150e8a4e4.js\"],\"LocalStorageAndAnalyticsProviders\",1]\n"])</script><script>self.__next_f.push([1,"2d:I[71476,[\"3473\",\"static/chunks/891cff7f-2ca7d0df884db9d0.js\",\"1725\",\"static/chunks/d30757c7-6effe791c08262b1.js\",\"803\",\"static/chunks/cd24890f-549fb4ba2f588ca6.js\",\"4368\",\"static/chunks/4368-ba814e7a6fbcef8d.js\",\"7261\",\"static/chunks/7261-f7cddf0d79dec697.js\",\"7267\",\"static/chunks/7267-4ae27d995fb80e6e.js\",\"9884\",\"static/chunks/9884-7de6993c6eaf8ebe.js\",\"7417\",\"static/chunks/7417-f80268fa1bfb6ac6.js\",\"7694\",\"static/chunks/7694-be5e0baf29ec311f.js\",\"7741\",\"static/chunks/7741-6aae3511c9a349f0.js\",\"1251\",\"static/chunks/1251-9e08ab4fcf4ec130.js\",\"6795\",\"static/chunks/6795-e69b9cf0aaf2d4f2.js\",\"3484\",\"static/chunks/3484-d2378bdc4b5e4f39.js\",\"9319\",\"static/chunks/9319-bd4367696c487773.js\",\"1750\",\"static/chunks/1750-1d0d5b4e9ed03a4f.js\",\"3972\",\"static/chunks/3972-a1df414e240d549c.js\",\"409\",\"static/chunks/409-46ca51541cd3a87b.js\",\"5456\",\"static/chunks/app/%255Fsites/%5Bsubdomain%5D/(multitenant)/layout-59d5e59150e8a4e4.js\"],\"SearchProvider\",1]\n"])</script><script>self.__next_f.push([1,"2e:I[46826,[\"3473\",\"static/chunks/891cff7f-2ca7d0df884db9d0.js\",\"1725\",\"static/chunks/d30757c7-6effe791c08262b1.js\",\"803\",\"static/chunks/cd24890f-549fb4ba2f588ca6.js\",\"4368\",\"static/chunks/4368-ba814e7a6fbcef8d.js\",\"7261\",\"static/chunks/7261-f7cddf0d79dec697.js\",\"7267\",\"static/chunks/7267-4ae27d995fb80e6e.js\",\"9884\",\"static/chunks/9884-7de6993c6eaf8ebe.js\",\"7417\",\"static/chunks/7417-f80268fa1bfb6ac6.js\",\"7694\",\"static/chunks/7694-be5e0baf29ec311f.js\",\"7741\",\"static/chunks/7741-6aae3511c9a349f0.js\",\"1251\",\"static/chunks/1251-9e08ab4fcf4ec130.js\",\"6795\",\"static/chunks/6795-e69b9cf0aaf2d4f2.js\",\"3484\",\"static/chunks/3484-d2378bdc4b5e4f39.js\",\"9319\",\"static/chunks/9319-bd4367696c487773.js\",\"1750\",\"static/chunks/1750-1d0d5b4e9ed03a4f.js\",\"3972\",\"static/chunks/3972-a1df414e240d549c.js\",\"409\",\"static/chunks/409-46ca51541cd3a87b.js\",\"5456\",\"static/chunks/app/%255Fsites/%5Bsubdomain%5D/(multitenant)/layout-59d5e59150e8a4e4.js\"],\"NavScroller\"]\n"])</script><script>self.__next_f.push([1,"2f:I[44464,[\"3473\",\"static/chunks/891cff7f-2ca7d0df884db9d0.js\",\"1725\",\"static/chunks/d30757c7-6effe791c08262b1.js\",\"803\",\"static/chunks/cd24890f-549fb4ba2f588ca6.js\",\"4368\",\"static/chunks/4368-ba814e7a6fbcef8d.js\",\"7261\",\"static/chunks/7261-f7cddf0d79dec697.js\",\"7267\",\"static/chunks/7267-4ae27d995fb80e6e.js\",\"9884\",\"static/chunks/9884-7de6993c6eaf8ebe.js\",\"7417\",\"static/chunks/7417-f80268fa1bfb6ac6.js\",\"7694\",\"static/chunks/7694-be5e0baf29ec311f.js\",\"7741\",\"static/chunks/7741-6aae3511c9a349f0.js\",\"1251\",\"static/chunks/1251-9e08ab4fcf4ec130.js\",\"6795\",\"static/chunks/6795-e69b9cf0aaf2d4f2.js\",\"3484\",\"static/chunks/3484-d2378bdc4b5e4f39.js\",\"9319\",\"static/chunks/9319-bd4367696c487773.js\",\"1750\",\"static/chunks/1750-1d0d5b4e9ed03a4f.js\",\"3972\",\"static/chunks/3972-a1df414e240d549c.js\",\"409\",\"static/chunks/409-46ca51541cd3a87b.js\",\"5456\",\"static/chunks/app/%255Fsites/%5Bsubdomain%5D/(multitenant)/layout-59d5e59150e8a4e4.js\"],\"MainContentLayout\",1]\n"])</script><script>self.__next_f.push([1,"30:I[27791,[\"3473\",\"static/chunks/891cff7f-2ca7d0df884db9d0.js\",\"1725\",\"static/chunks/d30757c7-6effe791c08262b1.js\",\"803\",\"static/chunks/cd24890f-549fb4ba2f588ca6.js\",\"4368\",\"static/chunks/4368-ba814e7a6fbcef8d.js\",\"7261\",\"static/chunks/7261-f7cddf0d79dec697.js\",\"7267\",\"static/chunks/7267-4ae27d995fb80e6e.js\",\"9884\",\"static/chunks/9884-7de6993c6eaf8ebe.js\",\"7417\",\"static/chunks/7417-f80268fa1bfb6ac6.js\",\"7694\",\"static/chunks/7694-be5e0baf29ec311f.js\",\"7741\",\"static/chunks/7741-6aae3511c9a349f0.js\",\"1251\",\"static/chunks/1251-9e08ab4fcf4ec130.js\",\"6795\",\"static/chunks/6795-e69b9cf0aaf2d4f2.js\",\"3484\",\"static/chunks/3484-d2378bdc4b5e4f39.js\",\"9319\",\"static/chunks/9319-bd4367696c487773.js\",\"1750\",\"static/chunks/1750-1d0d5b4e9ed03a4f.js\",\"3972\",\"static/chunks/3972-a1df414e240d549c.js\",\"409\",\"static/chunks/409-46ca51541cd3a87b.js\",\"5456\",\"static/chunks/app/%255Fsites/%5Bsubdomain%5D/(multitenant)/layout-59d5e59150e8a4e4.js\"],\"ChatAssistantSheet\",1]\n"])</script><script>self.__next_f.push([1,"39:I[4400,[\"3473\",\"static/chunks/891cff7f-2ca7d0df884db9d0.js\",\"1725\",\"static/chunks/d30757c7-6effe791c08262b1.js\",\"8788\",\"static/chunks/271c4271-e47f34f62bcfeead.js\",\"4368\",\"static/chunks/4368-ba814e7a6fbcef8d.js\",\"7261\",\"static/chunks/7261-f7cddf0d79dec697.js\",\"7267\",\"static/chunks/7267-4ae27d995fb80e6e.js\",\"9884\",\"static/chunks/9884-7de6993c6eaf8ebe.js\",\"7417\",\"static/chunks/7417-f80268fa1bfb6ac6.js\",\"3365\",\"static/chunks/3365-454992a36759b84c.js\",\"7694\",\"static/chunks/7694-be5e0baf29ec311f.js\",\"4960\",\"static/chunks/4960-ee8b5fbb01e1499b.js\",\"1251\",\"static/chunks/1251-9e08ab4fcf4ec130.js\",\"6795\",\"static/chunks/6795-e69b9cf0aaf2d4f2.js\",\"3484\",\"static/chunks/3484-d2378bdc4b5e4f39.js\",\"9319\",\"static/chunks/9319-bd4367696c487773.js\",\"1750\",\"static/chunks/1750-1d0d5b4e9ed03a4f.js\",\"5143\",\"static/chunks/5143-31261db986b6fb7f.js\",\"1398\",\"static/chunks/1398-89a0fd2f8a70761b.js\",\"3972\",\"static/chunks/3972-a1df414e240d549c.js\",\"457\",\"static/chunks/457-c340c72e118d74a1.js\",\"2544\",\"static/chunks/2544-93a10271db7901cd.js\",\"9841\",\"static/chunks/app/%255Fsites/%5Bsubdomain%5D/(multitenant)/%5B%5B...slug%5D%5D/page-4dde76c33c4e15c1.js\"],\"TopBar\",1]\n"])</script><script>self.__next_f.push([1,"3a:I[71197,[\"3473\",\"static/chunks/891cff7f-2ca7d0df884db9d0.js\",\"1725\",\"static/chunks/d30757c7-6effe791c08262b1.js\",\"8788\",\"static/chunks/271c4271-e47f34f62bcfeead.js\",\"4368\",\"static/chunks/4368-ba814e7a6fbcef8d.js\",\"7261\",\"static/chunks/7261-f7cddf0d79dec697.js\",\"7267\",\"static/chunks/7267-4ae27d995fb80e6e.js\",\"9884\",\"static/chunks/9884-7de6993c6eaf8ebe.js\",\"7417\",\"static/chunks/7417-f80268fa1bfb6ac6.js\",\"3365\",\"static/chunks/3365-454992a36759b84c.js\",\"7694\",\"static/chunks/7694-be5e0baf29ec311f.js\",\"4960\",\"static/chunks/4960-ee8b5fbb01e1499b.js\",\"1251\",\"static/chunks/1251-9e08ab4fcf4ec130.js\",\"6795\",\"static/chunks/6795-e69b9cf0aaf2d4f2.js\",\"3484\",\"static/chunks/3484-d2378bdc4b5e4f39.js\",\"9319\",\"static/chunks/9319-bd4367696c487773.js\",\"1750\",\"static/chunks/1750-1d0d5b4e9ed03a4f.js\",\"5143\",\"static/chunks/5143-31261db986b6fb7f.js\",\"1398\",\"static/chunks/1398-89a0fd2f8a70761b.js\",\"3972\",\"static/chunks/3972-a1df414e240d549c.js\",\"457\",\"static/chunks/457-c340c72e118d74a1.js\",\"2544\",\"static/chunks/2544-93a10271db7901cd.js\",\"9841\",\"static/chunks/app/%255Fsites/%5Bsubdomain%5D/(multitenant)/%5B%5B...slug%5D%5D/page-4dde76c33c4e15c1.js\"],\"ApiReferenceProvider\",1]\n"])</script><script>self.__next_f.push([1,"3b:I[71197,[\"3473\",\"static/chunks/891cff7f-2ca7d0df884db9d0.js\",\"1725\",\"static/chunks/d30757c7-6effe791c08262b1.js\",\"8788\",\"static/chunks/271c4271-e47f34f62bcfeead.js\",\"4368\",\"static/chunks/4368-ba814e7a6fbcef8d.js\",\"7261\",\"static/chunks/7261-f7cddf0d79dec697.js\",\"7267\",\"static/chunks/7267-4ae27d995fb80e6e.js\",\"9884\",\"static/chunks/9884-7de6993c6eaf8ebe.js\",\"7417\",\"static/chunks/7417-f80268fa1bfb6ac6.js\",\"3365\",\"static/chunks/3365-454992a36759b84c.js\",\"7694\",\"static/chunks/7694-be5e0baf29ec311f.js\",\"4960\",\"static/chunks/4960-ee8b5fbb01e1499b.js\",\"1251\",\"static/chunks/1251-9e08ab4fcf4ec130.js\",\"6795\",\"static/chunks/6795-e69b9cf0aaf2d4f2.js\",\"3484\",\"static/chunks/3484-d2378bdc4b5e4f39.js\",\"9319\",\"static/chunks/9319-bd4367696c487773.js\",\"1750\",\"static/chunks/1750-1d0d5b4e9ed03a4f.js\",\"5143\",\"static/chunks/5143-31261db986b6fb7f.js\",\"1398\",\"static/chunks/1398-89a0fd2f8a70761b.js\",\"3972\",\"static/chunks/3972-a1df414e240d549c.js\",\"457\",\"static/chunks/457-c340c72e118d74a1.js\",\"2544\",\"static/chunks/2544-93a10271db7901cd.js\",\"9841\",\"static/chunks/app/%255Fsites/%5Bsubdomain%5D/(multitenant)/%5B%5B...slug%5D%5D/page-4dde76c33c4e15c1.js\"],\"PageProvider\",1]\n"])</script><script>self.__next_f.push([1,"3c:I[99543,[\"3473\",\"static/chunks/891cff7f-2ca7d0df884db9d0.js\",\"1725\",\"static/chunks/d30757c7-6effe791c08262b1.js\",\"8788\",\"static/chunks/271c4271-e47f34f62bcfeead.js\",\"4368\",\"static/chunks/4368-ba814e7a6fbcef8d.js\",\"7261\",\"static/chunks/7261-f7cddf0d79dec697.js\",\"7267\",\"static/chunks/7267-4ae27d995fb80e6e.js\",\"9884\",\"static/chunks/9884-7de6993c6eaf8ebe.js\",\"7417\",\"static/chunks/7417-f80268fa1bfb6ac6.js\",\"3365\",\"static/chunks/3365-454992a36759b84c.js\",\"7694\",\"static/chunks/7694-be5e0baf29ec311f.js\",\"4960\",\"static/chunks/4960-ee8b5fbb01e1499b.js\",\"1251\",\"static/chunks/1251-9e08ab4fcf4ec130.js\",\"6795\",\"static/chunks/6795-e69b9cf0aaf2d4f2.js\",\"3484\",\"static/chunks/3484-d2378bdc4b5e4f39.js\",\"9319\",\"static/chunks/9319-bd4367696c487773.js\",\"1750\",\"static/chunks/1750-1d0d5b4e9ed03a4f.js\",\"5143\",\"static/chunks/5143-31261db986b6fb7f.js\",\"1398\",\"static/chunks/1398-89a0fd2f8a70761b.js\",\"3972\",\"static/chunks/3972-a1df414e240d549c.js\",\"457\",\"static/chunks/457-c340c72e118d74a1.js\",\"2544\",\"static/chunks/2544-93a10271db7901cd.js\",\"9841\",\"static/chunks/app/%255Fsites/%5Bsubdomain%5D/(multitenant)/%5B%5B...slug%5D%5D/page-4dde76c33c4e15c1.js\"],\"FooterAndSidebarScrollScript\",1]\n"])</script><script>self.__next_f.push([1,"3e:I[35319,[\"3473\",\"static/chunks/891cff7f-2ca7d0df884db9d0.js\",\"1725\",\"static/chunks/d30757c7-6effe791c08262b1.js\",\"8788\",\"static/chunks/271c4271-e47f34f62bcfeead.js\",\"4368\",\"static/chunks/4368-ba814e7a6fbcef8d.js\",\"7261\",\"static/chunks/7261-f7cddf0d79dec697.js\",\"7267\",\"static/chunks/7267-4ae27d995fb80e6e.js\",\"9884\",\"static/chunks/9884-7de6993c6eaf8ebe.js\",\"7417\",\"static/chunks/7417-f80268fa1bfb6ac6.js\",\"3365\",\"static/chunks/3365-454992a36759b84c.js\",\"7694\",\"static/chunks/7694-be5e0baf29ec311f.js\",\"4960\",\"static/chunks/4960-ee8b5fbb01e1499b.js\",\"1251\",\"static/chunks/1251-9e08ab4fcf4ec130.js\",\"6795\",\"static/chunks/6795-e69b9cf0aaf2d4f2.js\",\"3484\",\"static/chunks/3484-d2378bdc4b5e4f39.js\",\"9319\",\"static/chunks/9319-bd4367696c487773.js\",\"1750\",\"static/chunks/1750-1d0d5b4e9ed03a4f.js\",\"5143\",\"static/chunks/5143-31261db986b6fb7f.js\",\"1398\",\"static/chunks/1398-89a0fd2f8a70761b.js\",\"3972\",\"static/chunks/3972-a1df414e240d549c.js\",\"457\",\"static/chunks/457-c340c72e118d74a1.js\",\"2544\",\"static/chunks/2544-93a10271db7901cd.js\",\"9841\",\"static/chunks/app/%255Fsites/%5Bsubdomain%5D/(multitenant)/%5B%5B...slug%5D%5D/page-4dde76c33c4e15c1.js\"],\"MDXContentProvider\",1]\n"])</script><script>self.__next_f.push([1,"3f:I[86022,[\"3473\",\"static/chunks/891cff7f-2ca7d0df884db9d0.js\",\"1725\",\"static/chunks/d30757c7-6effe791c08262b1.js\",\"8788\",\"static/chunks/271c4271-e47f34f62bcfeead.js\",\"4368\",\"static/chunks/4368-ba814e7a6fbcef8d.js\",\"7261\",\"static/chunks/7261-f7cddf0d79dec697.js\",\"7267\",\"static/chunks/7267-4ae27d995fb80e6e.js\",\"9884\",\"static/chunks/9884-7de6993c6eaf8ebe.js\",\"7417\",\"static/chunks/7417-f80268fa1bfb6ac6.js\",\"3365\",\"static/chunks/3365-454992a36759b84c.js\",\"7694\",\"static/chunks/7694-be5e0baf29ec311f.js\",\"4960\",\"static/chunks/4960-ee8b5fbb01e1499b.js\",\"1251\",\"static/chunks/1251-9e08ab4fcf4ec130.js\",\"6795\",\"static/chunks/6795-e69b9cf0aaf2d4f2.js\",\"3484\",\"static/chunks/3484-d2378bdc4b5e4f39.js\",\"9319\",\"static/chunks/9319-bd4367696c487773.js\",\"1750\",\"static/chunks/1750-1d0d5b4e9ed03a4f.js\",\"5143\",\"static/chunks/5143-31261db986b6fb7f.js\",\"1398\",\"static/chunks/1398-89a0fd2f8a70761b.js\",\"3972\",\"static/chunks/3972-a1df414e240d549c.js\",\"457\",\"static/chunks/457-c340c72e118d74a1.js\",\"2544\",\"static/chunks/2544-93a10271db7901cd.js\",\"9841\",\"static/chunks/app/%255Fsites/%5Bsubdomain%5D/(multitenant)/%5B%5B...slug%5D%5D/page-4dde76c33c4e15c1.js\"],\"ContainerWrapper\"]\n"])</script><script>self.__next_f.push([1,"26:[\"$\",\"$L27\",null,{\"children\":[\"$\",\"$L28\",null,{\"children\":[\"$\",\"$L29\",null,{\"children\":[null,[[\"$\",\"style\",null,{\"children\":\":root {\\n    --primary: 13 147 115;\\n    --primary-light: 7 201 131;\\n    --primary-dark: 13 147 115;\\n    --background-light: 255 255 255;\\n    --background-dark: 9 13 13;\\n    --gray-50: 243 247 246;\\n    --gray-100: 238 242 241;\\n    --gray-200: 222 226 226;\\n    --gray-300: 206 210 209;\\n    --gray-400: 158 163 162;\\n    --gray-500: 112 116 115;\\n    --gray-600: 80 84 83;\\n    --gray-700: 62 67 66;\\n    --gray-800: 37 41 40;\\n    --gray-900: 23 27 26;\\n    --gray-950: 10 14 13;\\n  }\"}],null,[\"$\",\"div\",null,{\"className\":\"relative antialiased text-gray-500 dark:text-gray-400\",\"children\":[\"$\",\"$L2a\",null,{\"initialBanner\":null,\"config\":\"$undefined\",\"subdomain\":\"hoppscotch\",\"children\":[[\"$\",\"$L2b\",null,{\"theme\":\"mint\"}],[\"$\",\"$L2c\",null,{\"subdomain\":\"hoppscotch\",\"internalAnalyticsWriteKey\":\"phc_TXdpocbGVeZVm5VJmAsHTMrCofBQu3e0kN8HGMNGTVW\",\"org\":{\"plan\":\"hobby\",\"createdAt\":\"2023-11-17T02:21:54.030Z\"},\"children\":[\"$\",\"$L2d\",null,{\"subdomain\":\"hoppscotch\",\"hasChatPermissions\":false,\"assistantConfig\":{},\"children\":[[\"$\",\"$L2e\",null,{}],[[\"$\",\"$L2\",null,{\"parallelRouterKey\":\"topbar\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L4\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}],[\"$\",\"$L2f\",null,{\"children\":[[\"$\",\"$L2\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L4\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}],[\"$\",\"$L30\",null,{}]]}]]]}]}]]}]}]]]}]}]}]\n"])</script><script>self.__next_f.push([1,"1b:{\"metadata\":[[\"$\",\"title\",\"0\",{\"children\":\"Hoppscotch Documentation - Hoppscotch Documentation\"}],[\"$\",\"meta\",\"1\",{\"name\":\"description\",\"content\":\"Find user guides, quickstarts, tutorials, use cases, code samples, and more.\"}],[\"$\",\"meta\",\"2\",{\"name\":\"application-name\",\"content\":\"Hoppscotch Documentation\"}],[\"$\",\"meta\",\"3\",{\"name\":\"generator\",\"content\":\"Mintlify\"}],[\"$\",\"meta\",\"4\",{\"name\":\"msapplication-config\",\"content\":\"/mintlify-assets/_mintlify/favicons/hoppscotch/-JgjP-ycG3ET4V1_/_generated/favicon/browserconfig.xml\"}],[\"$\",\"meta\",\"5\",{\"name\":\"apple-mobile-web-app-title\",\"content\":\"Hoppscotch Documentation\"}],[\"$\",\"meta\",\"6\",{\"name\":\"msapplication-TileColor\",\"content\":\"#0D9373\"}],[\"$\",\"meta\",\"7\",{\"name\":\"charset\",\"content\":\"utf-8\"}],[\"$\",\"meta\",\"8\",{\"name\":\"og:site_name\",\"content\":\"Hoppscotch Documentation\"}],[\"$\",\"link\",\"9\",{\"rel\":\"alternate\",\"type\":\"application/xml\",\"href\":\"/sitemap.xml\"}],[\"$\",\"meta\",\"10\",{\"property\":\"og:title\",\"content\":\"Hoppscotch Documentation - Hoppscotch Documentation\"}],[\"$\",\"meta\",\"11\",{\"property\":\"og:description\",\"content\":\"Find user guides, quickstarts, tutorials, use cases, code samples, and more.\"}],[\"$\",\"meta\",\"12\",{\"property\":\"og:image\",\"content\":\"https://hoppscotch.mintlify.app/mintlify-assets/_next/image?url=%2F_mintlify%2Fapi%2Fog%3Fdivision%3DDocumentation%26title%3DHoppscotch%2BDocumentation%26description%3DFind%2Buser%2Bguides%252C%2Bquickstarts%252C%2Btutorials%252C%2Buse%2Bcases%252C%2Bcode%2Bsamples%252C%2Band%2Bmore.%26logoLight%3Dhttps%253A%252F%252Fmintcdn.com%252Fhoppscotch%252FWCaaGbVhL02n1fVh%252Flogo%252Flight.svg%253FmaxW%253D24%2526auto%253Dformat%2526n%253DWCaaGbVhL02n1fVh%2526q%253D85%2526s%253D16bb5835da6a36c3e15623e498995666%26logoDark%3Dhttps%253A%252F%252Fmintcdn.com%252Fhoppscotch%252FWCaaGbVhL02n1fVh%252Flogo%252Fdark.svg%253FmaxW%253D24%2526auto%253Dformat%2526n%253DWCaaGbVhL02n1fVh%2526q%253D85%2526s%253D0b5b10713c4319ddfb2b4280c62403ce%26primaryColor%3D%25230D9373%26lightColor%3D%252307C983%26darkColor%3D%25230D9373%26backgroundLight%3D%2523ffffff%26backgroundDark%3D%2523090d0d\u0026w=1200\u0026q=100\"}],[\"$\",\"meta\",\"13\",{\"property\":\"og:image:width\",\"content\":\"1200\"}],[\"$\",\"meta\",\"14\",{\"property\":\"og:image:height\",\"content\":\"630\"}],[\"$\",\"meta\",\"15\",{\"property\":\"og:type\",\"content\":\"website\"}],[\"$\",\"meta\",\"16\",{\"name\":\"twitter:card\",\"content\":\"summary_large_image\"}],[\"$\",\"meta\",\"17\",{\"name\":\"twitter:title\",\"content\":\"Hoppscotch Documentation - Hoppscotch Documentation\"}],[\"$\",\"meta\",\"18\",{\"name\":\"twitter:description\",\"content\":\"Find user guides, quickstarts, tutorials, use cases, code samples, and more.\"}],[\"$\",\"meta\",\"19\",{\"name\":\"twitter:image\",\"content\":\"https://hoppscotch.mintlify.app/mintlify-assets/_next/image?url=%2F_mintlify%2Fapi%2Fog%3Fdivision%3DDocumentation%26title%3DHoppscotch%2BDocumentation%26description%3DFind%2Buser%2Bguides%252C%2Bquickstarts%252C%2Btutorials%252C%2Buse%2Bcases%252C%2Bcode%2Bsamples%252C%2Band%2Bmore.%26logoLight%3Dhttps%253A%252F%252Fmintcdn.com%252Fhoppscotch%252FWCaaGbVhL02n1fVh%252Flogo%252Flight.svg%253FmaxW%253D24%2526auto%253Dformat%2526n%253DWCaaGbVhL02n1fVh%2526q%253D85%2526s%253D16bb5835da6a36c3e15623e498995666%26logoDark%3Dhttps%253A%252F%252Fmintcdn.com%252Fhoppscotch%252FWCaaGbVhL02n1fVh%252Flogo%252Fdark.svg%253FmaxW%253D24%2526auto%253Dformat%2526n%253DWCaaGbVhL02n1fVh%2526q%253D85%2526s%253D0b5b10713c4319ddfb2b4280c62403ce%26primaryColor%3D%25230D9373%26lightColor%3D%252307C983%26darkColor%3D%25230D9373%26backgroundLight%3D%2523ffffff%26backgroundDark%3D%2523090d0d\u0026w=1200\u0026q=100\"}],[\"$\",\"meta\",\"20\",{\"name\":\"twitter:image:width\",\"content\":\"1200\"}],[\"$\",\"meta\",\"21\",{\"name\":\"twitter:image:height\",\"content\":\"630\"}],\"$L31\",\"$L32\",\"$L33\",\"$L34\",\"$L35\",\"$L36\",\"$L37\",\"$L38\"],\"error\":null,\"digest\":\"$undefined\"}\n"])</script><script>self.__next_f.push([1,"20:\"$1b:metadata\"\n14:[\"$\",\"$L39\",null,{\"className\":\"peer is-not-custom peer is-not-center peer is-not-wide peer is-not-frame\",\"pageMetadata\":{\"sidebarTitle\":\"Home\",\"title\":\"Hoppscotch Documentation\",\"description\":\"Find user guides, quickstarts, tutorials, use cases, code samples, and more.\",\"href\":\"/index\"}}]\n3d:T7d2,"])</script><script>self.__next_f.push([1,".mint-pointer-events-none {\n    pointer-events: none\n}\n.mint-mt-1 {\n    margin-top: 0.25rem\n}\n.mint-mt-4 {\n    margin-top: 1rem\n}\n.mint-block {\n    display: block\n}\n.mint-flex {\n    display: flex\n}\n.mint-hidden {\n    display: none\n}\n.mint-w-full {\n    width: 100%\n}\n.mint-cursor-pointer {\n    cursor: pointer\n}\n.mint-items-center {\n    align-items: center\n}\n.mint-space-x-1 \u003e :not([hidden]) ~ :not([hidden]) {\n    --tw-space-x-reverse: 0;\n    margin-right: calc(0.25rem * var(--tw-space-x-reverse));\n    margin-left: calc(0.25rem * calc(1 - var(--tw-space-x-reverse)))\n}\n.mint-rounded-md {\n    border-radius: 0.375rem\n}\n.mint-border-b {\n    border-bottom-width: 1px\n}\n.mint-border-gray-500 {\n    --tw-border-opacity: 1;\n    border-color: rgb(107 114 128 / var(--tw-border-opacity))\n}\n.mint-px-4 {\n    padding-left: 1rem;\n    padding-right: 1rem\n}\n.mint-py-2 {\n    padding-top: 0.5rem;\n    padding-bottom: 0.5rem\n}\n.mint-pb-8 {\n    padding-bottom: 2rem\n}\n.mint-text-sm {\n    font-size: 0.875rem;\n    line-height: 1.25rem\n}\n.mint-font-bold {\n    font-weight: 700\n}\n.mint-font-semibold {\n    font-weight: 600\n}\n.mint-leading-6 {\n    line-height: 1.5rem\n}\n.mint-text-gray-600 {\n    --tw-text-opacity: 1;\n    color: rgb(75 85 99 / var(--tw-text-opacity))\n}\n.mint-text-gray-900 {\n    --tw-text-opacity: 1;\n    color: rgb(17 24 39 / var(--tw-text-opacity))\n}\n.mint-text-white {\n    --tw-text-opacity: 1;\n    color: rgb(255 255 255 / var(--tw-text-opacity))\n}\n.mint-group:hover .group-hover\\:mint-opacity-\\[0\\.9\\] {\n    opacity: 0.9\n}\n.dark\\:mint-block:is(.dark *) {\n    display: block\n}\n.dark\\:mint-hidden:is(.dark *) {\n    display: none\n}\n.dark\\:mint-border-gray-800:is(.dark *) {\n    --tw-border-opacity: 1;\n    border-color: rgb(31 41 55 / var(--tw-border-opacity))\n}\n.dark\\:mint-text-gray-400:is(.dark *) {\n    --tw-text-opacity: 1;\n    color: rgb(156 163 175 / var(--tw-text-opacity))\n}\n.dark\\:mint-text-white:is(.dark *) {\n    --tw-text-opacity: 1;\n    color: rgb(255 255 255 / var(--tw-text-opacity))\n}"])</script><script>self.__next_f.push([1,"18:[\"$\",\"$L3a\",null,{\"value\":{\"apiReferenceData\":{}},\"children\":[\"$\",\"$L3b\",null,{\"value\":{\"pageMetadata\":\"$14:props:pageMetadata\",\"description\":{\"compiledSource\":\"\\\"use strict\\\";\\nconst {jsx: _jsx} = arguments[0];\\nconst {useMDXComponents: _provideComponents} = arguments[0];\\nfunction _createMdxContent(props) {\\n  const _components = {\\n    p: \\\"p\\\",\\n    ..._provideComponents(),\\n    ...props.components\\n  };\\n  return _jsx(_components.p, {\\n    children: \\\"Find user guides, quickstarts, tutorials, use cases, code samples, and more.\\\"\\n  });\\n}\\nfunction MDXContent(props = {}) {\\n  const {wrapper: MDXLayout} = {\\n    ..._provideComponents(),\\n    ...props.components\\n  };\\n  return MDXLayout ? _jsx(MDXLayout, {\\n    ...props,\\n    children: _jsx(_createMdxContent, {\\n      ...props\\n    })\\n  }) : _createMdxContent(props);\\n}\\nreturn {\\n  default: MDXContent\\n};\\n\",\"frontmatter\":{},\"scope\":{}},\"mdxExtracts\":{\"tableOfContents\":[],\"codeExamples\":{}},\"pageType\":\"$undefined\",\"panelMdxSource\":\"$undefined\",\"panelMdxSourceWithNoJs\":\"$undefined\"},\"children\":[[\"$\",\"$L21\",null,{\"id\":\"_mintlify-page-mode-script\",\"strategy\":\"beforeInteractive\",\"dangerouslySetInnerHTML\":{\"__html\":\"document.documentElement.setAttribute('data-page-mode', 'none');\"}}],[\"$\",\"$L3c\",null,{\"theme\":\"mint\"}],[[\"$\",\"span\",null,{\"className\":\"fixed inset-0 bg-background-light dark:bg-background-dark -z-10 pointer-events-none\"}],null,false,false],[[\"$\",\"style\",\"0\",{\"dangerouslySetInnerHTML\":{\"__html\":\"$3d\"}}]],[],[[\"$\",\"$L3e\",\"/\",{\"children\":[\"$\",\"$L3f\",null,{\"isCustom\":false,\"children\":[\"$L40\",\"$L41\"]}]}]]]}]}]\n"])</script><script>self.__next_f.push([1,"42:I[74780,[],\"IconMark\"]\n"])</script><script>self.__next_f.push([1,"43:I[93010,[\"3473\",\"static/chunks/891cff7f-2ca7d0df884db9d0.js\",\"1725\",\"static/chunks/d30757c7-6effe791c08262b1.js\",\"8788\",\"static/chunks/271c4271-e47f34f62bcfeead.js\",\"4368\",\"static/chunks/4368-ba814e7a6fbcef8d.js\",\"7261\",\"static/chunks/7261-f7cddf0d79dec697.js\",\"7267\",\"static/chunks/7267-4ae27d995fb80e6e.js\",\"9884\",\"static/chunks/9884-7de6993c6eaf8ebe.js\",\"7417\",\"static/chunks/7417-f80268fa1bfb6ac6.js\",\"3365\",\"static/chunks/3365-454992a36759b84c.js\",\"7694\",\"static/chunks/7694-be5e0baf29ec311f.js\",\"4960\",\"static/chunks/4960-ee8b5fbb01e1499b.js\",\"1251\",\"static/chunks/1251-9e08ab4fcf4ec130.js\",\"6795\",\"static/chunks/6795-e69b9cf0aaf2d4f2.js\",\"3484\",\"static/chunks/3484-d2378bdc4b5e4f39.js\",\"9319\",\"static/chunks/9319-bd4367696c487773.js\",\"1750\",\"static/chunks/1750-1d0d5b4e9ed03a4f.js\",\"5143\",\"static/chunks/5143-31261db986b6fb7f.js\",\"1398\",\"static/chunks/1398-89a0fd2f8a70761b.js\",\"3972\",\"static/chunks/3972-a1df414e240d549c.js\",\"457\",\"static/chunks/457-c340c72e118d74a1.js\",\"2544\",\"static/chunks/2544-93a10271db7901cd.js\",\"9841\",\"static/chunks/app/%255Fsites/%5Bsubdomain%5D/(multitenant)/%5B%5B...slug%5D%5D/page-4dde76c33c4e15c1.js\"],\"SidePanel\",1]\n"])</script><script>self.__next_f.push([1,"44:I[10457,[\"3473\",\"static/chunks/891cff7f-2ca7d0df884db9d0.js\",\"1725\",\"static/chunks/d30757c7-6effe791c08262b1.js\",\"8788\",\"static/chunks/271c4271-e47f34f62bcfeead.js\",\"4368\",\"static/chunks/4368-ba814e7a6fbcef8d.js\",\"7261\",\"static/chunks/7261-f7cddf0d79dec697.js\",\"7267\",\"static/chunks/7267-4ae27d995fb80e6e.js\",\"9884\",\"static/chunks/9884-7de6993c6eaf8ebe.js\",\"7417\",\"static/chunks/7417-f80268fa1bfb6ac6.js\",\"3365\",\"static/chunks/3365-454992a36759b84c.js\",\"7694\",\"static/chunks/7694-be5e0baf29ec311f.js\",\"4960\",\"static/chunks/4960-ee8b5fbb01e1499b.js\",\"1251\",\"static/chunks/1251-9e08ab4fcf4ec130.js\",\"6795\",\"static/chunks/6795-e69b9cf0aaf2d4f2.js\",\"3484\",\"static/chunks/3484-d2378bdc4b5e4f39.js\",\"9319\",\"static/chunks/9319-bd4367696c487773.js\",\"1750\",\"static/chunks/1750-1d0d5b4e9ed03a4f.js\",\"5143\",\"static/chunks/5143-31261db986b6fb7f.js\",\"1398\",\"static/chunks/1398-89a0fd2f8a70761b.js\",\"3972\",\"static/chunks/3972-a1df414e240d549c.js\",\"457\",\"static/chunks/457-c340c72e118d74a1.js\",\"2544\",\"static/chunks/2544-93a10271db7901cd.js\",\"9841\",\"static/chunks/app/%255Fsites/%5Bsubdomain%5D/(multitenant)/%5B%5B...slug%5D%5D/page-4dde76c33c4e15c1.js\"],\"PageHeader\",1]\n"])</script><script>self.__next_f.push([1,"45:I[98959,[\"3473\",\"static/chunks/891cff7f-2ca7d0df884db9d0.js\",\"1725\",\"static/chunks/d30757c7-6effe791c08262b1.js\",\"8788\",\"static/chunks/271c4271-e47f34f62bcfeead.js\",\"4368\",\"static/chunks/4368-ba814e7a6fbcef8d.js\",\"7261\",\"static/chunks/7261-f7cddf0d79dec697.js\",\"7267\",\"static/chunks/7267-4ae27d995fb80e6e.js\",\"9884\",\"static/chunks/9884-7de6993c6eaf8ebe.js\",\"7417\",\"static/chunks/7417-f80268fa1bfb6ac6.js\",\"3365\",\"static/chunks/3365-454992a36759b84c.js\",\"7694\",\"static/chunks/7694-be5e0baf29ec311f.js\",\"4960\",\"static/chunks/4960-ee8b5fbb01e1499b.js\",\"1251\",\"static/chunks/1251-9e08ab4fcf4ec130.js\",\"6795\",\"static/chunks/6795-e69b9cf0aaf2d4f2.js\",\"3484\",\"static/chunks/3484-d2378bdc4b5e4f39.js\",\"9319\",\"static/chunks/9319-bd4367696c487773.js\",\"1750\",\"static/chunks/1750-1d0d5b4e9ed03a4f.js\",\"5143\",\"static/chunks/5143-31261db986b6fb7f.js\",\"1398\",\"static/chunks/1398-89a0fd2f8a70761b.js\",\"3972\",\"static/chunks/3972-a1df414e240d549c.js\",\"457\",\"static/chunks/457-c340c72e118d74a1.js\",\"2544\",\"static/chunks/2544-93a10271db7901cd.js\",\"9841\",\"static/chunks/app/%255Fsites/%5Bsubdomain%5D/(multitenant)/%5B%5B...slug%5D%5D/page-4dde76c33c4e15c1.js\"],\"MdxPanel\",1]\n"])</script><script>self.__next_f.push([1,"46:I[32907,[\"3473\",\"static/chunks/891cff7f-2ca7d0df884db9d0.js\",\"1725\",\"static/chunks/d30757c7-6effe791c08262b1.js\",\"8788\",\"static/chunks/271c4271-e47f34f62bcfeead.js\",\"4368\",\"static/chunks/4368-ba814e7a6fbcef8d.js\",\"7261\",\"static/chunks/7261-f7cddf0d79dec697.js\",\"7267\",\"static/chunks/7267-4ae27d995fb80e6e.js\",\"9884\",\"static/chunks/9884-7de6993c6eaf8ebe.js\",\"7417\",\"static/chunks/7417-f80268fa1bfb6ac6.js\",\"3365\",\"static/chunks/3365-454992a36759b84c.js\",\"7694\",\"static/chunks/7694-be5e0baf29ec311f.js\",\"4960\",\"static/chunks/4960-ee8b5fbb01e1499b.js\",\"1251\",\"static/chunks/1251-9e08ab4fcf4ec130.js\",\"6795\",\"static/chunks/6795-e69b9cf0aaf2d4f2.js\",\"3484\",\"static/chunks/3484-d2378bdc4b5e4f39.js\",\"9319\",\"static/chunks/9319-bd4367696c487773.js\",\"1750\",\"static/chunks/1750-1d0d5b4e9ed03a4f.js\",\"5143\",\"static/chunks/5143-31261db986b6fb7f.js\",\"1398\",\"static/chunks/1398-89a0fd2f8a70761b.js\",\"3972\",\"static/chunks/3972-a1df414e240d549c.js\",\"457\",\"static/chunks/457-c340c72e118d74a1.js\",\"2544\",\"static/chunks/2544-93a10271db7901cd.js\",\"9841\",\"static/chunks/app/%255Fsites/%5Bsubdomain%5D/(multitenant)/%5B%5B...slug%5D%5D/page-4dde76c33c4e15c1.js\"],\"Api\",1]\n"])</script><script>self.__next_f.push([1,"47:I[41270,[\"3473\",\"static/chunks/891cff7f-2ca7d0df884db9d0.js\",\"1725\",\"static/chunks/d30757c7-6effe791c08262b1.js\",\"8788\",\"static/chunks/271c4271-e47f34f62bcfeead.js\",\"4368\",\"static/chunks/4368-ba814e7a6fbcef8d.js\",\"7261\",\"static/chunks/7261-f7cddf0d79dec697.js\",\"7267\",\"static/chunks/7267-4ae27d995fb80e6e.js\",\"9884\",\"static/chunks/9884-7de6993c6eaf8ebe.js\",\"7417\",\"static/chunks/7417-f80268fa1bfb6ac6.js\",\"3365\",\"static/chunks/3365-454992a36759b84c.js\",\"7694\",\"static/chunks/7694-be5e0baf29ec311f.js\",\"4960\",\"static/chunks/4960-ee8b5fbb01e1499b.js\",\"1251\",\"static/chunks/1251-9e08ab4fcf4ec130.js\",\"6795\",\"static/chunks/6795-e69b9cf0aaf2d4f2.js\",\"3484\",\"static/chunks/3484-d2378bdc4b5e4f39.js\",\"9319\",\"static/chunks/9319-bd4367696c487773.js\",\"1750\",\"static/chunks/1750-1d0d5b4e9ed03a4f.js\",\"5143\",\"static/chunks/5143-31261db986b6fb7f.js\",\"1398\",\"static/chunks/1398-89a0fd2f8a70761b.js\",\"3972\",\"static/chunks/3972-a1df414e240d549c.js\",\"457\",\"static/chunks/457-c340c72e118d74a1.js\",\"2544\",\"static/chunks/2544-93a10271db7901cd.js\",\"9841\",\"static/chunks/app/%255Fsites/%5Bsubdomain%5D/(multitenant)/%5B%5B...slug%5D%5D/page-4dde76c33c4e15c1.js\"],\"default\",1]\n"])</script><script>self.__next_f.push([1,"31:[\"$\",\"link\",\"22\",{\"rel\":\"apple-touch-icon\",\"href\":\"/mintlify-assets/_mintlify/favicons/hoppscotch/-JgjP-ycG3ET4V1_/_generated/favicon/apple-touch-icon.png\",\"type\":\"image/png\",\"sizes\":\"180x180\",\"media\":\"$undefined\"}]\n32:[\"$\",\"link\",\"23\",{\"rel\":\"icon\",\"href\":\"/mintlify-assets/_mintlify/favicons/hoppscotch/-JgjP-ycG3ET4V1_/_generated/favicon/favicon-16x16.png\",\"type\":\"image/png\",\"sizes\":\"16x16\",\"media\":\"(prefers-color-scheme: light)\"}]\n33:[\"$\",\"link\",\"24\",{\"rel\":\"icon\",\"href\":\"/mintlify-assets/_mintlify/favicons/hoppscotch/-JgjP-ycG3ET4V1_/_generated/favicon/favicon-32x32.png\",\"type\":\"image/png\",\"sizes\":\"32x32\",\"media\":\"(prefers-color-scheme: light)\"}]\n34:[\"$\",\"link\",\"25\",{\"rel\":\"shortcut icon\",\"href\":\"/mintlify-assets/_mintlify/favicons/hoppscotch/-JgjP-ycG3ET4V1_/_generated/favicon/favicon.ico\",\"type\":\"image/x-icon\",\"sizes\":\"$undefined\",\"media\":\"(prefers-color-scheme: light)\"}]\n35:[\"$\",\"link\",\"26\",{\"rel\":\"icon\",\"href\":\"/mintlify-assets/_mintlify/favicons/hoppscotch/-JgjP-ycG3ET4V1_/_generated/favicon-dark/favicon-16x16.png\",\"type\":\"image/png\",\"sizes\":\"16x16\",\"media\":\"(prefers-color-scheme: dark)\"}]\n36:[\"$\",\"link\",\"27\",{\"rel\":\"icon\",\"href\":\"/mintlify-assets/_mintlify/favicons/hoppscotch/-JgjP-ycG3ET4V1_/_generated/favicon-dark/favicon-32x32.png\",\"type\":\"image/png\",\"sizes\":\"32x32\",\"media\":\"(prefers-color-scheme: dark)\"}]\n37:[\"$\",\"link\",\"28\",{\"rel\":\"shortcut icon\",\"href\":\"/mintlify-assets/_mintlify/favicons/hoppscotch/-JgjP-ycG3ET4V1_/_generated/favicon-dark/favicon.ico\",\"type\":\"image/x-icon\",\"sizes\":\"$undefined\",\"media\":\"(prefers-color-scheme: dark)\"}]\n38:[\"$\",\"$L42\",\"29\",{}]\n40:[\"$\",\"$L43\",null,{}]\n48:T195d,"])</script><script>self.__next_f.push([1,"\"use strict\";\nconst {Fragment: _Fragment, jsx: _jsx, jsxs: _jsxs} = arguments[0];\nconst {useMDXComponents: _provideComponents} = arguments[0];\nconst HeroCard = ({imgDark, imgLight, title, description, href}) =\u003e {\n  const _components = {\n    a: \"a\",\n    h1: \"h1\",\n    h2: \"h2\",\n    img: \"img\",\n    ..._provideComponents()\n  };\n  return _jsxs(_components.a, {\n    className: \"mint-border-b mint-pb-8 mint-cursor-pointer mint-border-gray-500 dark:mint-border-gray-800 hover:!border-primary dark:hover:!border-primary-light\",\n    href: href,\n    children: [_jsx(_components.img, {\n      src: imgLight,\n      className: \"mint-block dark:mint-hidden mint-w-full mint-pointer-events-none\"\n    }), _jsx(_components.img, {\n      src: imgDark,\n      className: \"mint-hidden dark:mint-block mint-w-full mint-pointer-events-none\"\n    }), _jsx(_components.h1, {\n      className: \"mint-mt-4 mint-font-semibold mint-text-gray-900 dark:mint-text-white\",\n      children: title\n    }), _jsx(_components.h2, {\n      className: \"mint-mt-1 mint-text-gray-600 dark:mint-text-gray-400 mint-text-sm mint-leading-6\",\n      children: description\n    })]\n  });\n};\nconst PrimaryButton = ({label, href}) =\u003e {\n  const _components = {\n    a: \"a\",\n    button: \"button\",\n    span: \"span\",\n    ..._provideComponents()\n  };\n  return _jsx(_components.a, {\n    className: \"mint-group\",\n    href: href,\n    children: _jsx(_components.button, {\n      className: \"mint-flex mint-items-center mint-space-x-1 mint-font-bold mint-px-4 mint-py-2 bg-primary-dark mint-rounded-md group-hover:mint-opacity-[0.9] mint-text-white group-hover:mint-opacity-[0.9]\",\n      children: _jsx(_components.span, {\n        children: label\n      })\n    })\n  });\n};\nfunction _createMdxContent(props) {\n  const _components = {\n    p: \"p\",\n    strong: \"strong\",\n    ..._provideComponents(),\n    ...props.components\n  }, {CardGroup, OptimizedImage} = _components;\n  if (!CardGroup) _missingMdxReference(\"CardGroup\", true);\n  if (!OptimizedImage) _missingMdxReference(\"OptimizedImage\", true);\n  return _jsxs(_Fragment, {\n    children: [_jsx(OptimizedImage, {\n      className: \"block dark:hidden\",\n      src: \"https://mintcdn.com/hoppscotch/WCaaGbVhL02n1fVh/images/hero-light.png?maxW=2100\u0026auto=format\u0026n=WCaaGbVhL02n1fVh\u0026q=85\u0026s=a8c4a375f790bd9e03f525d1fbf00c25\",\n      width: \"2100\",\n      height: \"960\",\n      \"data-path\": \"images/hero-light.png\",\n      srcset: \"https://mintcdn.com/hoppscotch/WCaaGbVhL02n1fVh/images/hero-light.png?w=280\u0026maxW=2100\u0026auto=format\u0026n=WCaaGbVhL02n1fVh\u0026q=85\u0026s=a6689b4b445b11fae5dd26854cd55608 280w, https://mintcdn.com/hoppscotch/WCaaGbVhL02n1fVh/images/hero-light.png?w=560\u0026maxW=2100\u0026auto=format\u0026n=WCaaGbVhL02n1fVh\u0026q=85\u0026s=3cd42d5e76ed432173cf5f6614743a2d 560w, https://mintcdn.com/hoppscotch/WCaaGbVhL02n1fVh/images/hero-light.png?w=840\u0026maxW=2100\u0026auto=format\u0026n=WCaaGbVhL02n1fVh\u0026q=85\u0026s=ca9dba458ebddd4b0615b614470a397c 840w, https://mintcdn.com/hoppscotch/WCaaGbVhL02n1fVh/images/hero-light.png?w=1100\u0026maxW=2100\u0026auto=format\u0026n=WCaaGbVhL02n1fVh\u0026q=85\u0026s=cb7bc3d0182202634296ae5d35568913 1100w, https://mintcdn.com/hoppscotch/WCaaGbVhL02n1fVh/images/hero-light.png?w=1650\u0026maxW=2100\u0026auto=format\u0026n=WCaaGbVhL02n1fVh\u0026q=85\u0026s=2aca29bf3b9c9146198e4d654b86dec3 1650w, https://mintcdn.com/hoppscotch/WCaaGbVhL02n1fVh/images/hero-light.png?w=2500\u0026maxW=2100\u0026auto=format\u0026n=WCaaGbVhL02n1fVh\u0026q=85\u0026s=c3574e34dacd6adbbe4507cfaac404af 2500w\",\n      \"data-optimize\": \"true\",\n      \"data-opv\": \"2\"\n    }), \"\\n\", _jsx(OptimizedImage, {\n      className: \"hidden dark:block\",\n      src: \"https://mintcdn.com/hoppscotch/WCaaGbVhL02n1fVh/images/hero-dark.png?maxW=2100\u0026auto=format\u0026n=WCaaGbVhL02n1fVh\u0026q=85\u0026s=93ecd6b9755f93e6cc996629e8263d0c\",\n      width: \"2100\",\n      height: \"960\",\n      \"data-path\": \"images/hero-dark.png\",\n      srcset: \"https://mintcdn.com/hoppscotch/WCaaGbVhL02n1fVh/images/hero-dark.png?w=280\u0026maxW=2100\u0026auto=format\u0026n=WCaaGbVhL02n1fVh\u0026q=85\u0026s=13a3b5e7c4f7a8007d8d0a0c507da96f 280w, https://mintcdn.com/hoppscotch/WCaaGbVhL02n1fVh/images/hero-dark.png?w=560\u0026maxW=2100\u0026auto=format\u0026n=WCaaGbVhL02n1fVh\u0026q=85\u0026s=d50179abaadf70ab59c2441d7cc91d72 560w, https://mintcdn.com/hoppscotch/WCaaGbVhL02n1fVh/images/hero-dark.png?w=840\u0026maxW=2100\u0026auto=format\u0026n=WCaaGbVhL02n1fVh\u0026q=85\u0026s=88432f60bc3258b697a793dd391d0417 840w, https://mintcdn.com/hoppscotch/WCaaGbVhL02n1fVh/images/hero-dark.png?w=1100\u0026maxW=2100\u0026auto=format\u0026n=WCaaGbVhL02n1fVh\u0026q=85\u0026s=fbb5234184b8c5b41898374e3b6fce5e 1100w, https://mintcdn.com/hoppscotch/WCaaGbVhL02n1fVh/images/hero-dark.png?w=1650\u0026maxW=2100\u0026auto=format\u0026n=WCaaGbVhL02n1fVh\u0026q=85\u0026s=423205c2e845dc42ca338f784a26a569 1650w, https://mintcdn.com/hoppscotch/WCaaGbVhL02n1fVh/images/hero-dark.png?w=2500\u0026maxW=2100\u0026auto=format\u0026n=WCaaGbVhL02n1fVh\u0026q=85\u0026s=4cd0b1ac44dd0c350c78a6ca0dc24121 2500w\",\n      \"data-optimize\": \"true\",\n      \"data-opv\": \"2\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.strong, {\n        children: \"Hoppscotch is a lightweight, web-based API development suite.\"\n      })\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"It was built from the ground up with ease of use and accessibility in mind providing all the functionality needed for API developers with minimalist, unobtrusive UI.\"\n    }), \"\\n\", _jsx(PrimaryButton, {\n      label: \"Get started\",\n      href: \"/documentation/getting-started/introduction\"\n    }), \"\\n\", _jsx(\"br\", {}), \"\\n\", _jsx(\"br\", {}), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.strong, {\n        children: \"Quick Start\"\n      })\n    }), \"\\n\", _jsxs(CardGroup, {\n      children: [_jsx(HeroCard, {\n        title: \"Hoppscotch Cloud\",\n        description: \"Everything you need to get started with Hoppscotch, hosted by us.\",\n        href: \"https://hoppscotch.io\"\n      }), _jsx(HeroCard, {\n        title: \"Hoppscotch Self-Host\",\n        description: \"Deploy your own instance of Hoppscotch on your own infrastructure.\",\n        href: \"/documentation/self-host/getting-started\"\n      })]\n    })]\n  });\n}\nfunction MDXContent(props = {}) {\n  const {wrapper: MDXLayout} = {\n    ..._provideComponents(),\n    ...props.components\n  };\n  return MDXLayout ? _jsx(MDXLayout, {\n    ...props,\n    children: _jsx(_createMdxContent, {\n      ...props\n    })\n  }) : _createMdxContent(props);\n}\nreturn {\n  HeroCard,\n  PrimaryButton,\n  default: MDXContent\n};\nfunction _missingMdxReference(id, component) {\n  throw new Error(\"Expected \" + (component ? \"component\" : \"object\") + \" `\" + id + \"` to be defined: you likely forgot to import, pass, or provide it.\");\n}\n"])</script><script>self.__next_f.push([1,"49:T195d,"])</script><script>self.__next_f.push([1,"\"use strict\";\nconst {Fragment: _Fragment, jsx: _jsx, jsxs: _jsxs} = arguments[0];\nconst {useMDXComponents: _provideComponents} = arguments[0];\nconst HeroCard = ({imgDark, imgLight, title, description, href}) =\u003e {\n  const _components = {\n    a: \"a\",\n    h1: \"h1\",\n    h2: \"h2\",\n    img: \"img\",\n    ..._provideComponents()\n  };\n  return _jsxs(_components.a, {\n    className: \"mint-border-b mint-pb-8 mint-cursor-pointer mint-border-gray-500 dark:mint-border-gray-800 hover:!border-primary dark:hover:!border-primary-light\",\n    href: href,\n    children: [_jsx(_components.img, {\n      src: imgLight,\n      className: \"mint-block dark:mint-hidden mint-w-full mint-pointer-events-none\"\n    }), _jsx(_components.img, {\n      src: imgDark,\n      className: \"mint-hidden dark:mint-block mint-w-full mint-pointer-events-none\"\n    }), _jsx(_components.h1, {\n      className: \"mint-mt-4 mint-font-semibold mint-text-gray-900 dark:mint-text-white\",\n      children: title\n    }), _jsx(_components.h2, {\n      className: \"mint-mt-1 mint-text-gray-600 dark:mint-text-gray-400 mint-text-sm mint-leading-6\",\n      children: description\n    })]\n  });\n};\nconst PrimaryButton = ({label, href}) =\u003e {\n  const _components = {\n    a: \"a\",\n    button: \"button\",\n    span: \"span\",\n    ..._provideComponents()\n  };\n  return _jsx(_components.a, {\n    className: \"mint-group\",\n    href: href,\n    children: _jsx(_components.button, {\n      className: \"mint-flex mint-items-center mint-space-x-1 mint-font-bold mint-px-4 mint-py-2 bg-primary-dark mint-rounded-md group-hover:mint-opacity-[0.9] mint-text-white group-hover:mint-opacity-[0.9]\",\n      children: _jsx(_components.span, {\n        children: label\n      })\n    })\n  });\n};\nfunction _createMdxContent(props) {\n  const _components = {\n    p: \"p\",\n    strong: \"strong\",\n    ..._provideComponents(),\n    ...props.components\n  }, {CardGroup, OptimizedImage} = _components;\n  if (!CardGroup) _missingMdxReference(\"CardGroup\", true);\n  if (!OptimizedImage) _missingMdxReference(\"OptimizedImage\", true);\n  return _jsxs(_Fragment, {\n    children: [_jsx(OptimizedImage, {\n      className: \"block dark:hidden\",\n      src: \"https://mintcdn.com/hoppscotch/WCaaGbVhL02n1fVh/images/hero-light.png?maxW=2100\u0026auto=format\u0026n=WCaaGbVhL02n1fVh\u0026q=85\u0026s=a8c4a375f790bd9e03f525d1fbf00c25\",\n      width: \"2100\",\n      height: \"960\",\n      \"data-path\": \"images/hero-light.png\",\n      srcset: \"https://mintcdn.com/hoppscotch/WCaaGbVhL02n1fVh/images/hero-light.png?w=280\u0026maxW=2100\u0026auto=format\u0026n=WCaaGbVhL02n1fVh\u0026q=85\u0026s=a6689b4b445b11fae5dd26854cd55608 280w, https://mintcdn.com/hoppscotch/WCaaGbVhL02n1fVh/images/hero-light.png?w=560\u0026maxW=2100\u0026auto=format\u0026n=WCaaGbVhL02n1fVh\u0026q=85\u0026s=3cd42d5e76ed432173cf5f6614743a2d 560w, https://mintcdn.com/hoppscotch/WCaaGbVhL02n1fVh/images/hero-light.png?w=840\u0026maxW=2100\u0026auto=format\u0026n=WCaaGbVhL02n1fVh\u0026q=85\u0026s=ca9dba458ebddd4b0615b614470a397c 840w, https://mintcdn.com/hoppscotch/WCaaGbVhL02n1fVh/images/hero-light.png?w=1100\u0026maxW=2100\u0026auto=format\u0026n=WCaaGbVhL02n1fVh\u0026q=85\u0026s=cb7bc3d0182202634296ae5d35568913 1100w, https://mintcdn.com/hoppscotch/WCaaGbVhL02n1fVh/images/hero-light.png?w=1650\u0026maxW=2100\u0026auto=format\u0026n=WCaaGbVhL02n1fVh\u0026q=85\u0026s=2aca29bf3b9c9146198e4d654b86dec3 1650w, https://mintcdn.com/hoppscotch/WCaaGbVhL02n1fVh/images/hero-light.png?w=2500\u0026maxW=2100\u0026auto=format\u0026n=WCaaGbVhL02n1fVh\u0026q=85\u0026s=c3574e34dacd6adbbe4507cfaac404af 2500w\",\n      \"data-optimize\": \"true\",\n      \"data-opv\": \"2\"\n    }), \"\\n\", _jsx(OptimizedImage, {\n      className: \"hidden dark:block\",\n      src: \"https://mintcdn.com/hoppscotch/WCaaGbVhL02n1fVh/images/hero-dark.png?maxW=2100\u0026auto=format\u0026n=WCaaGbVhL02n1fVh\u0026q=85\u0026s=93ecd6b9755f93e6cc996629e8263d0c\",\n      width: \"2100\",\n      height: \"960\",\n      \"data-path\": \"images/hero-dark.png\",\n      srcset: \"https://mintcdn.com/hoppscotch/WCaaGbVhL02n1fVh/images/hero-dark.png?w=280\u0026maxW=2100\u0026auto=format\u0026n=WCaaGbVhL02n1fVh\u0026q=85\u0026s=13a3b5e7c4f7a8007d8d0a0c507da96f 280w, https://mintcdn.com/hoppscotch/WCaaGbVhL02n1fVh/images/hero-dark.png?w=560\u0026maxW=2100\u0026auto=format\u0026n=WCaaGbVhL02n1fVh\u0026q=85\u0026s=d50179abaadf70ab59c2441d7cc91d72 560w, https://mintcdn.com/hoppscotch/WCaaGbVhL02n1fVh/images/hero-dark.png?w=840\u0026maxW=2100\u0026auto=format\u0026n=WCaaGbVhL02n1fVh\u0026q=85\u0026s=88432f60bc3258b697a793dd391d0417 840w, https://mintcdn.com/hoppscotch/WCaaGbVhL02n1fVh/images/hero-dark.png?w=1100\u0026maxW=2100\u0026auto=format\u0026n=WCaaGbVhL02n1fVh\u0026q=85\u0026s=fbb5234184b8c5b41898374e3b6fce5e 1100w, https://mintcdn.com/hoppscotch/WCaaGbVhL02n1fVh/images/hero-dark.png?w=1650\u0026maxW=2100\u0026auto=format\u0026n=WCaaGbVhL02n1fVh\u0026q=85\u0026s=423205c2e845dc42ca338f784a26a569 1650w, https://mintcdn.com/hoppscotch/WCaaGbVhL02n1fVh/images/hero-dark.png?w=2500\u0026maxW=2100\u0026auto=format\u0026n=WCaaGbVhL02n1fVh\u0026q=85\u0026s=4cd0b1ac44dd0c350c78a6ca0dc24121 2500w\",\n      \"data-optimize\": \"true\",\n      \"data-opv\": \"2\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.strong, {\n        children: \"Hoppscotch is a lightweight, web-based API development suite.\"\n      })\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"It was built from the ground up with ease of use and accessibility in mind providing all the functionality needed for API developers with minimalist, unobtrusive UI.\"\n    }), \"\\n\", _jsx(PrimaryButton, {\n      label: \"Get started\",\n      href: \"/documentation/getting-started/introduction\"\n    }), \"\\n\", _jsx(\"br\", {}), \"\\n\", _jsx(\"br\", {}), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.strong, {\n        children: \"Quick Start\"\n      })\n    }), \"\\n\", _jsxs(CardGroup, {\n      children: [_jsx(HeroCard, {\n        title: \"Hoppscotch Cloud\",\n        description: \"Everything you need to get started with Hoppscotch, hosted by us.\",\n        href: \"https://hoppscotch.io\"\n      }), _jsx(HeroCard, {\n        title: \"Hoppscotch Self-Host\",\n        description: \"Deploy your own instance of Hoppscotch on your own infrastructure.\",\n        href: \"/documentation/self-host/getting-started\"\n      })]\n    })]\n  });\n}\nfunction MDXContent(props = {}) {\n  const {wrapper: MDXLayout} = {\n    ..._provideComponents(),\n    ...props.components\n  };\n  return MDXLayout ? _jsx(MDXLayout, {\n    ...props,\n    children: _jsx(_createMdxContent, {\n      ...props\n    })\n  }) : _createMdxContent(props);\n}\nreturn {\n  HeroCard,\n  PrimaryButton,\n  default: MDXContent\n};\nfunction _missingMdxReference(id, component) {\n  throw new Error(\"Expected \" + (component ? \"component\" : \"object\") + \" `\" + id + \"` to be defined: you likely forgot to import, pass, or provide it.\");\n}\n"])</script><script>self.__next_f.push([1,"41:[\"$\",\"div\",null,{\"className\":\"relative grow box-border flex-col w-full mx-auto px-1 lg:pl-[23.7rem] lg:-ml-12 xl:w-[calc(100%-28rem)]\",\"id\":\"content-area\",\"children\":[[\"$\",\"$L44\",null,{}],[\"$\",\"$L45\",null,{\"mobile\":true}],[\"$\",\"$L46\",null,{}],[\"$\",\"div\",null,{\"className\":\"mdx-content relative mt-8 mb-14 prose prose-gray dark:prose-invert\",\"data-page-title\":\"Hoppscotch Documentation\",\"data-page-href\":\"/index\",\"id\":\"content\",\"children\":[[\"$\",\"$L47\",null,{\"mdxSource\":{\"compiledSource\":\"$48\",\"frontmatter\":{},\"scope\":{\"config\":{},\"pageMetadata\":{\"sidebarTitle\":\"Home\",\"title\":\"Hoppscotch Documentation\",\"description\":\"Find user guides, quickstarts, tutorials, use cases, code samples, and more.\",\"href\":\"/index\"}}},\"mdxSourceWithNoJs\":{\"compiledSource\":\"$49\",\"frontmatter\":{},\"scope\":{\"config\":{},\"pageMetadata\":{\"sidebarTitle\":\"Home\",\"title\":\"Hoppscotch Documentation\",\"description\":\"Find user guides, quickstarts, tutorials, use cases, code samples, and more.\",\"href\":\"/index\"}}}}],\"$undefined\",\"$undefined\"]}],\"$L4a\",\"$L4b\",\"$L4c\",\"$L4d\"]}]\n"])</script><script>self.__next_f.push([1,"4e:I[44105,[\"3473\",\"static/chunks/891cff7f-2ca7d0df884db9d0.js\",\"1725\",\"static/chunks/d30757c7-6effe791c08262b1.js\",\"8788\",\"static/chunks/271c4271-e47f34f62bcfeead.js\",\"4368\",\"static/chunks/4368-ba814e7a6fbcef8d.js\",\"7261\",\"static/chunks/7261-f7cddf0d79dec697.js\",\"7267\",\"static/chunks/7267-4ae27d995fb80e6e.js\",\"9884\",\"static/chunks/9884-7de6993c6eaf8ebe.js\",\"7417\",\"static/chunks/7417-f80268fa1bfb6ac6.js\",\"3365\",\"static/chunks/3365-454992a36759b84c.js\",\"7694\",\"static/chunks/7694-be5e0baf29ec311f.js\",\"4960\",\"static/chunks/4960-ee8b5fbb01e1499b.js\",\"1251\",\"static/chunks/1251-9e08ab4fcf4ec130.js\",\"6795\",\"static/chunks/6795-e69b9cf0aaf2d4f2.js\",\"3484\",\"static/chunks/3484-d2378bdc4b5e4f39.js\",\"9319\",\"static/chunks/9319-bd4367696c487773.js\",\"1750\",\"static/chunks/1750-1d0d5b4e9ed03a4f.js\",\"5143\",\"static/chunks/5143-31261db986b6fb7f.js\",\"1398\",\"static/chunks/1398-89a0fd2f8a70761b.js\",\"3972\",\"static/chunks/3972-a1df414e240d549c.js\",\"457\",\"static/chunks/457-c340c72e118d74a1.js\",\"2544\",\"static/chunks/2544-93a10271db7901cd.js\",\"9841\",\"static/chunks/app/%255Fsites/%5Bsubdomain%5D/(multitenant)/%5B%5B...slug%5D%5D/page-4dde76c33c4e15c1.js\"],\"UserFeedback\",1]\n"])</script><script>self.__next_f.push([1,"4f:I[52604,[\"3473\",\"static/chunks/891cff7f-2ca7d0df884db9d0.js\",\"1725\",\"static/chunks/d30757c7-6effe791c08262b1.js\",\"8788\",\"static/chunks/271c4271-e47f34f62bcfeead.js\",\"4368\",\"static/chunks/4368-ba814e7a6fbcef8d.js\",\"7261\",\"static/chunks/7261-f7cddf0d79dec697.js\",\"7267\",\"static/chunks/7267-4ae27d995fb80e6e.js\",\"9884\",\"static/chunks/9884-7de6993c6eaf8ebe.js\",\"7417\",\"static/chunks/7417-f80268fa1bfb6ac6.js\",\"3365\",\"static/chunks/3365-454992a36759b84c.js\",\"7694\",\"static/chunks/7694-be5e0baf29ec311f.js\",\"4960\",\"static/chunks/4960-ee8b5fbb01e1499b.js\",\"1251\",\"static/chunks/1251-9e08ab4fcf4ec130.js\",\"6795\",\"static/chunks/6795-e69b9cf0aaf2d4f2.js\",\"3484\",\"static/chunks/3484-d2378bdc4b5e4f39.js\",\"9319\",\"static/chunks/9319-bd4367696c487773.js\",\"1750\",\"static/chunks/1750-1d0d5b4e9ed03a4f.js\",\"5143\",\"static/chunks/5143-31261db986b6fb7f.js\",\"1398\",\"static/chunks/1398-89a0fd2f8a70761b.js\",\"3972\",\"static/chunks/3972-a1df414e240d549c.js\",\"457\",\"static/chunks/457-c340c72e118d74a1.js\",\"2544\",\"static/chunks/2544-93a10271db7901cd.js\",\"9841\",\"static/chunks/app/%255Fsites/%5Bsubdomain%5D/(multitenant)/%5B%5B...slug%5D%5D/page-4dde76c33c4e15c1.js\"],\"Pagination\",1]\n"])</script><script>self.__next_f.push([1,"50:I[48973,[\"3473\",\"static/chunks/891cff7f-2ca7d0df884db9d0.js\",\"1725\",\"static/chunks/d30757c7-6effe791c08262b1.js\",\"8788\",\"static/chunks/271c4271-e47f34f62bcfeead.js\",\"4368\",\"static/chunks/4368-ba814e7a6fbcef8d.js\",\"7261\",\"static/chunks/7261-f7cddf0d79dec697.js\",\"7267\",\"static/chunks/7267-4ae27d995fb80e6e.js\",\"9884\",\"static/chunks/9884-7de6993c6eaf8ebe.js\",\"7417\",\"static/chunks/7417-f80268fa1bfb6ac6.js\",\"3365\",\"static/chunks/3365-454992a36759b84c.js\",\"7694\",\"static/chunks/7694-be5e0baf29ec311f.js\",\"4960\",\"static/chunks/4960-ee8b5fbb01e1499b.js\",\"1251\",\"static/chunks/1251-9e08ab4fcf4ec130.js\",\"6795\",\"static/chunks/6795-e69b9cf0aaf2d4f2.js\",\"3484\",\"static/chunks/3484-d2378bdc4b5e4f39.js\",\"9319\",\"static/chunks/9319-bd4367696c487773.js\",\"1750\",\"static/chunks/1750-1d0d5b4e9ed03a4f.js\",\"5143\",\"static/chunks/5143-31261db986b6fb7f.js\",\"1398\",\"static/chunks/1398-89a0fd2f8a70761b.js\",\"3972\",\"static/chunks/3972-a1df414e240d549c.js\",\"457\",\"static/chunks/457-c340c72e118d74a1.js\",\"2544\",\"static/chunks/2544-93a10271db7901cd.js\",\"9841\",\"static/chunks/app/%255Fsites/%5Bsubdomain%5D/(multitenant)/%5B%5B...slug%5D%5D/page-4dde76c33c4e15c1.js\"],\"default\",1]\n"])</script><script>self.__next_f.push([1,"51:I[16385,[\"3473\",\"static/chunks/891cff7f-2ca7d0df884db9d0.js\",\"1725\",\"static/chunks/d30757c7-6effe791c08262b1.js\",\"8788\",\"static/chunks/271c4271-e47f34f62bcfeead.js\",\"4368\",\"static/chunks/4368-ba814e7a6fbcef8d.js\",\"7261\",\"static/chunks/7261-f7cddf0d79dec697.js\",\"7267\",\"static/chunks/7267-4ae27d995fb80e6e.js\",\"9884\",\"static/chunks/9884-7de6993c6eaf8ebe.js\",\"7417\",\"static/chunks/7417-f80268fa1bfb6ac6.js\",\"3365\",\"static/chunks/3365-454992a36759b84c.js\",\"7694\",\"static/chunks/7694-be5e0baf29ec311f.js\",\"4960\",\"static/chunks/4960-ee8b5fbb01e1499b.js\",\"1251\",\"static/chunks/1251-9e08ab4fcf4ec130.js\",\"6795\",\"static/chunks/6795-e69b9cf0aaf2d4f2.js\",\"3484\",\"static/chunks/3484-d2378bdc4b5e4f39.js\",\"9319\",\"static/chunks/9319-bd4367696c487773.js\",\"1750\",\"static/chunks/1750-1d0d5b4e9ed03a4f.js\",\"5143\",\"static/chunks/5143-31261db986b6fb7f.js\",\"1398\",\"static/chunks/1398-89a0fd2f8a70761b.js\",\"3972\",\"static/chunks/3972-a1df414e240d549c.js\",\"457\",\"static/chunks/457-c340c72e118d74a1.js\",\"2544\",\"static/chunks/2544-93a10271db7901cd.js\",\"9841\",\"static/chunks/app/%255Fsites/%5Bsubdomain%5D/(multitenant)/%5B%5B...slug%5D%5D/page-4dde76c33c4e15c1.js\"],\"Footer\",1]\n"])</script><script>self.__next_f.push([1,"4a:[\"$\",\"$L4e\",null,{}]\n4b:[\"$\",\"$L4f\",null,{}]\n4c:[\"$\",\"$L50\",null,{}]\n4d:[\"$\",\"$L51\",null,{\"className\":\"mt-10 sm:mt-0\"}]\n"])</script></body></html>
