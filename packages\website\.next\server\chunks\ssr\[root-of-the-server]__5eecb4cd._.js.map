{"version": 3, "sources": [], "sections": [{"offset": {"line": 28, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/webs/apiflow/packages/website/src/components/ui/LanguageSwitcher.tsx"], "sourcesContent": ["'use client';\n\nimport { useLocale } from 'next-intl';\nimport { useRouter, usePathname } from 'next/navigation';\nimport { Globe } from 'lucide-react';\nimport { useState } from 'react';\n\nconst languages = [\n  { code: 'zh', name: '中文', flag: '🇨🇳' },\n  { code: 'en', name: 'English', flag: '🇺🇸' }\n];\n\nexport default function LanguageSwitcher() {\n  const locale = useLocale();\n  const router = useRouter();\n  const pathname = usePathname();\n  const [isOpen, setIsOpen] = useState(false);\n\n  const currentLanguage = languages.find(lang => lang.code === locale) || languages[0]; // 默认显示中文\n\n  const handleLanguageChange = (newLocale: string) => {\n    // 从路径名中移除当前语言环境\n    const pathWithoutLocale = pathname.replace(`/${locale}`, '') || '/';\n    \n    // 导航到新的语言环境\n    router.push(`/${newLocale}${pathWithoutLocale}`);\n    setIsOpen(false);\n  };\n\n  // 如果没有找到当前语言，默认显示中文\n  const displayLanguage = currentLanguage || { code: 'zh', name: '中文', flag: '🇨🇳' };\n\n  return (\n    <div className=\"relative\">\n      <button\n        onClick={() => setIsOpen(!isOpen)}\n        className=\"flex items-center space-x-2 px-3 py-2 rounded-lg hover:bg-gray-100 transition-colors duration-200\"\n        aria-label=\"Switch language\"\n      >\n        <Globe className=\"w-4 h-4 text-gray-600\" />\n        <span className=\"text-sm font-medium text-gray-700\">\n          {displayLanguage.flag} {displayLanguage.name}\n        </span>\n        <svg\n          className={`w-4 h-4 text-gray-600 transition-transform duration-200 ${\n            isOpen ? 'rotate-180' : ''\n          }`}\n          fill=\"none\"\n          stroke=\"currentColor\"\n          viewBox=\"0 0 24 24\"\n        >\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 9l-7 7-7-7\" />\n        </svg>\n      </button>\n\n      {isOpen && (\n        <>\n          {/* 背景遮罩 */}\n          <div\n            className=\"fixed inset-0 z-10\"\n            onClick={() => setIsOpen(false)}\n          />\n          \n          {/* 下拉菜单 */}\n          <div className=\"absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 z-20\">\n            <div className=\"py-2\">\n              {languages.map((language) => (\n                <button\n                  key={language.code}\n                  onClick={() => handleLanguageChange(language.code)}\n                  className={`w-full text-left px-4 py-2 text-sm hover:bg-gray-50 transition-colors duration-200 flex items-center space-x-3 ${\n                    locale === language.code ? 'bg-blue-50 text-blue-600' : 'text-gray-700'\n                  }`}\n                >\n                  <span className=\"text-lg\">{language.flag}</span>\n                  <span className=\"font-medium\">{language.name}</span>\n                  {locale === language.code && (\n                    <svg className=\"w-4 h-4 ml-auto\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                      <path fillRule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\n                    </svg>\n                  )}\n                </button>\n              ))}\n            </div>\n          </div>\n        </>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAOA,MAAM,YAAY;IAChB;QAAE,MAAM;QAAM,MAAM;QAAM,MAAM;IAAO;IACvC;QAAE,MAAM;QAAM,MAAM;QAAW,MAAM;IAAO;CAC7C;AAEc,SAAS;IACtB,MAAM,SAAS,IAAA,+KAAS;IACxB,MAAM,SAAS,IAAA,+IAAS;IACxB,MAAM,WAAW,IAAA,iJAAW;IAC5B,MAAM,CAAC,QAAQ,UAAU,GAAG,IAAA,iNAAQ,EAAC;IAErC,MAAM,kBAAkB,UAAU,IAAI,CAAC,CAAA,OAAQ,KAAK,IAAI,KAAK,WAAW,SAAS,CAAC,EAAE,EAAE,SAAS;IAE/F,MAAM,uBAAuB,CAAC;QAC5B,gBAAgB;QAChB,MAAM,oBAAoB,SAAS,OAAO,CAAC,CAAC,CAAC,EAAE,QAAQ,EAAE,OAAO;QAEhE,YAAY;QACZ,OAAO,IAAI,CAAC,CAAC,CAAC,EAAE,YAAY,mBAAmB;QAC/C,UAAU;IACZ;IAEA,oBAAoB;IACpB,MAAM,kBAAkB,mBAAmB;QAAE,MAAM;QAAM,MAAM;QAAM,MAAM;IAAO;IAElF,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBACC,SAAS,IAAM,UAAU,CAAC;gBAC1B,WAAU;gBACV,cAAW;;kCAEX,8OAAC,6MAAK;wBAAC,WAAU;;;;;;kCACjB,8OAAC;wBAAK,WAAU;;4BACb,gBAAgB,IAAI;4BAAC;4BAAE,gBAAgB,IAAI;;;;;;;kCAE9C,8OAAC;wBACC,WAAW,CAAC,wDAAwD,EAClE,SAAS,eAAe,IACxB;wBACF,MAAK;wBACL,QAAO;wBACP,SAAQ;kCAER,cAAA,8OAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;;;;;;;YAIxE,wBACC;;kCAEE,8OAAC;wBACC,WAAU;wBACV,SAAS,IAAM,UAAU;;;;;;kCAI3B,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACZ,UAAU,GAAG,CAAC,CAAC,yBACd,8OAAC;oCAEC,SAAS,IAAM,qBAAqB,SAAS,IAAI;oCACjD,WAAW,CAAC,+GAA+G,EACzH,WAAW,SAAS,IAAI,GAAG,6BAA6B,iBACxD;;sDAEF,8OAAC;4CAAK,WAAU;sDAAW,SAAS,IAAI;;;;;;sDACxC,8OAAC;4CAAK,WAAU;sDAAe,SAAS,IAAI;;;;;;wCAC3C,WAAW,SAAS,IAAI,kBACvB,8OAAC;4CAAI,WAAU;4CAAkB,MAAK;4CAAe,SAAQ;sDAC3D,cAAA,8OAAC;gDAAK,UAAS;gDAAU,GAAE;gDAAqH,UAAS;;;;;;;;;;;;mCAVxJ,SAAS,IAAI;;;;;;;;;;;;;;;;;;;;;;;AAqBpC", "debugId": null}}, {"offset": {"line": 208, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/webs/apiflow/packages/website/src/components/ui/GitHubStars.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { Star } from 'lucide-react';\n\ninterface GitHubStarsProps {\n  repo: string; // 格式: \"owner/repo\"\n  className?: string;\n}\n\nexport default function GitHubStars({ repo, className = '' }: GitHubStarsProps) {\n  const [stars, setStars] = useState<number | null>(null);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    const fetchStars = async () => {\n      try {\n        const response = await fetch(`https://api.github.com/repos/${repo}`);\n        if (response.ok) {\n          const data = await response.json();\n          setStars(data.stargazers_count);\n        }\n      } catch (error) {\n        console.error('Failed to fetch GitHub stars:', error);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchStars();\n  }, [repo]);\n\n  const formatStars = (count: number) => {\n    if (count >= 1000) {\n      return `${(count / 1000).toFixed(1)}k`;\n    }\n    return count.toString();\n  };\n\n  if (loading) {\n    return (\n      <div className={`inline-flex items-center space-x-1 ${className}`}>\n        <Star className=\"w-4 h-4 text-yellow-500\" />\n        <span className=\"text-sm text-gray-600\">...</span>\n      </div>\n    );\n  }\n\n  if (stars === null) {\n    return null;\n  }\n\n  return (\n    <div className={`inline-flex items-center space-x-1 ${className}`}>\n      <Star className=\"w-4 h-4 text-yellow-500 fill-current\" />\n      <span className=\"text-sm font-medium text-gray-700\">{formatStars(stars)}</span>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAHA;;;;AAUe,SAAS,YAAY,EAAE,IAAI,EAAE,YAAY,EAAE,EAAoB;IAC5E,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,iNAAQ,EAAgB;IAClD,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,iNAAQ,EAAC;IAEvC,IAAA,kNAAS,EAAC;QACR,MAAM,aAAa;YACjB,IAAI;gBACF,MAAM,WAAW,MAAM,MAAM,CAAC,6BAA6B,EAAE,MAAM;gBACnE,IAAI,SAAS,EAAE,EAAE;oBACf,MAAM,OAAO,MAAM,SAAS,IAAI;oBAChC,SAAS,KAAK,gBAAgB;gBAChC;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,iCAAiC;YACjD,SAAU;gBACR,WAAW;YACb;QACF;QAEA;IACF,GAAG;QAAC;KAAK;IAET,MAAM,cAAc,CAAC;QACnB,IAAI,SAAS,MAAM;YACjB,OAAO,GAAG,CAAC,QAAQ,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;QACxC;QACA,OAAO,MAAM,QAAQ;IACvB;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAW,CAAC,mCAAmC,EAAE,WAAW;;8BAC/D,8OAAC,0MAAI;oBAAC,WAAU;;;;;;8BAChB,8OAAC;oBAAK,WAAU;8BAAwB;;;;;;;;;;;;IAG9C;IAEA,IAAI,UAAU,MAAM;QAClB,OAAO;IACT;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAC,mCAAmC,EAAE,WAAW;;0BAC/D,8OAAC,0MAAI;gBAAC,WAAU;;;;;;0BAChB,8OAAC;gBAAK,WAAU;0BAAqC,YAAY;;;;;;;;;;;;AAGvE", "debugId": null}}, {"offset": {"line": 304, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/webs/apiflow/packages/website/src/components/layout/Header.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Link from 'next/link';\nimport Image from 'next/image';\nimport { Menu, X } from 'lucide-react';\nimport { useTranslations } from 'next-intl';\nimport LanguageSwitcher from '@/components/ui/LanguageSwitcher';\nimport GitHubStars from '@/components/ui/GitHubStars';\n\nexport default function Header() {\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n  const t = useTranslations();\n\n  const navigation = [\n    { name: t('功能特性'), href: '#features' },\n    { name: t('产品演示'), href: '#demo' },\n    { name: t('关于我们'), href: '#about' },\n    { name: t('联系我们'), href: '#contact' },\n  ];\n\n  const repositoryLinks = [\n    {\n      name: 'GitHub',\n      href: 'https://github.com/trueleaf/apiflow',\n      icon: '/github.svg',\n      showStars: true\n    }\n  ];\n\n  return (\n    <header className=\"fixed top-0 w-full bg-white/95 backdrop-blur-sm border-b border-gray-200 z-50\">\n      <nav className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex items-center py-4\">\n          {/* 左侧 - Logo */}\n          <div className=\"flex items-center\">\n            <Link href=\"/\" className=\"flex items-center space-x-2\">\n              <Image\n                src=\"/logo.png\"\n                alt=\"Apiflow Logo\"\n                width={32}\n                height={32}\n                className=\"w-8 h-8\"\n              />\n              <span className=\"text-xl font-bold text-gray-900\">Apiflow</span>\n            </Link>\n          </div>\n\n          {/* 右侧 - 导航和操作 */}\n          <div className=\"flex-1 flex justify-end items-center\">\n            {/* 桌面端导航 */}\n            <div className=\"hidden md:flex items-center space-x-8 mr-8\">\n              {navigation.map((item) => (\n                <Link\n                  key={item.name}\n                  href={item.href}\n                  className=\"text-gray-600 hover:text-blue-600 transition-colors duration-200 font-medium\"\n                >\n                  {item.name}\n                </Link>\n              ))}\n            </div>\n\n            {/* 仓库链接和语言切换器 */}\n            <div className=\"hidden md:flex items-center space-x-4\">\n              {/* 仓库链接 */}\n              <div className=\"flex items-center space-x-3\">\n                {repositoryLinks.map((repo) => (\n                  <Link\n                    key={repo.name}\n                    href={repo.href}\n                    target=\"_blank\"\n                    rel=\"noopener noreferrer\"\n                    className=\"flex items-center space-x-2 text-gray-600 hover:text-blue-600 transition-colors duration-200 text-sm\"\n                  >\n                    <Image \n                      src={repo.icon} \n                      alt={`${repo.name} icon`}\n                      width={16} \n                      height={16} \n                      className=\"w-4 h-4\" \n                    />\n                    {repo.showStars && (\n                      <GitHubStars repo=\"trueleaf/apiflow\" className=\"ml-1\" />\n                    )}\n                  </Link>\n                ))}\n              </div>\n\n              {/* 语言切换器 */}\n              <LanguageSwitcher />\n            </div>\n\n            {/* 移动端菜单按钮 */}\n            <div className=\"md:hidden\">\n              <button\n                onClick={() => setIsMenuOpen(!isMenuOpen)}\n                className=\"text-gray-600 hover:text-gray-900 transition-colors duration-200\"\n              >\n                {isMenuOpen ? <X size={24} /> : <Menu size={24} />}\n              </button>\n            </div>\n          </div>\n        </div>\n\n        {/* 移动端导航 */}\n        {isMenuOpen && (\n          <div className=\"md:hidden\">\n            <div className=\"px-2 pt-2 pb-3 space-y-1 bg-white border-t border-gray-200\">\n              {navigation.map((item) => (\n                <Link\n                  key={item.name}\n                  href={item.href}\n                  className=\"block px-3 py-2 text-gray-600 hover:text-blue-600 transition-colors duration-200 font-medium\"\n                  onClick={() => setIsMenuOpen(false)}\n                >\n                  {item.name}\n                </Link>\n              ))}\n\n              {/* 移动端仓库链接 */}\n              <div className=\"pt-4 border-t border-gray-200\">\n                <div className=\"px-3 py-2 text-sm font-medium text-gray-500\">\n                  Open Source\n                </div>\n                {repositoryLinks.map((repo) => (\n                  <Link\n                    key={repo.name}\n                    href={repo.href}\n                    target=\"_blank\"\n                    rel=\"noopener noreferrer\"\n                    className=\"flex items-center space-x-2 px-3 py-2 text-gray-600 hover:text-blue-600 transition-colors duration-200\"\n                    onClick={() => setIsMenuOpen(false)}\n                  >\n                    <Image \n                      src={repo.icon} \n                      alt={`${repo.name} icon`}\n                      width={16} \n                      height={16} \n                      className=\"w-4 h-4\" \n                    />\n                    {repo.showStars && (\n                      <GitHubStars repo=\"trueleaf/apiflow\" className=\"ml-auto\" />\n                    )}\n                  </Link>\n                ))}\n              </div>\n\n              {/* 移动端语言切换器 */}\n              <div className=\"pt-4 px-3\">\n                <LanguageSwitcher />\n              </div>\n            </div>\n          </div>\n        )}\n      </nav>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AARA;;;;;;;;;AAUe,SAAS;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,iNAAQ,EAAC;IAC7C,MAAM,IAAI,IAAA,yNAAe;IAEzB,MAAM,aAAa;QACjB;YAAE,MAAM,EAAE;YAAS,MAAM;QAAY;QACrC;YAAE,MAAM,EAAE;YAAS,MAAM;QAAQ;QACjC;YAAE,MAAM,EAAE;YAAS,MAAM;QAAS;QAClC;YAAE,MAAM,EAAE;YAAS,MAAM;QAAW;KACrC;IAED,MAAM,kBAAkB;QACtB;YACE,MAAM;YACN,MAAM;YACN,MAAM;YACN,WAAW;QACb;KACD;IAED,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,uKAAI;gCAAC,MAAK;gCAAI,WAAU;;kDACvB,8OAAC,wIAAK;wCACJ,KAAI;wCACJ,KAAI;wCACJ,OAAO;wCACP,QAAQ;wCACR,WAAU;;;;;;kDAEZ,8OAAC;wCAAK,WAAU;kDAAkC;;;;;;;;;;;;;;;;;sCAKtD,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;8CACZ,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC,uKAAI;4CAEH,MAAM,KAAK,IAAI;4CACf,WAAU;sDAET,KAAK,IAAI;2CAJL,KAAK,IAAI;;;;;;;;;;8CAUpB,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC;4CAAI,WAAU;sDACZ,gBAAgB,GAAG,CAAC,CAAC,qBACpB,8OAAC,uKAAI;oDAEH,MAAM,KAAK,IAAI;oDACf,QAAO;oDACP,KAAI;oDACJ,WAAU;;sEAEV,8OAAC,wIAAK;4DACJ,KAAK,KAAK,IAAI;4DACd,KAAK,GAAG,KAAK,IAAI,CAAC,KAAK,CAAC;4DACxB,OAAO;4DACP,QAAQ;4DACR,WAAU;;;;;;wDAEX,KAAK,SAAS,kBACb,8OAAC,yKAAW;4DAAC,MAAK;4DAAmB,WAAU;;;;;;;mDAd5C,KAAK,IAAI;;;;;;;;;;sDAqBpB,8OAAC,8KAAgB;;;;;;;;;;;8CAInB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCACC,SAAS,IAAM,cAAc,CAAC;wCAC9B,WAAU;kDAET,2BAAa,8OAAC,iMAAC;4CAAC,MAAM;;;;;iEAAS,8OAAC,0MAAI;4CAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBAOnD,4BACC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;4BACZ,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC,uKAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAU;oCACV,SAAS,IAAM,cAAc;8CAE5B,KAAK,IAAI;mCALL,KAAK,IAAI;;;;;0CAUlB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAA8C;;;;;;oCAG5D,gBAAgB,GAAG,CAAC,CAAC,qBACpB,8OAAC,uKAAI;4CAEH,MAAM,KAAK,IAAI;4CACf,QAAO;4CACP,KAAI;4CACJ,WAAU;4CACV,SAAS,IAAM,cAAc;;8DAE7B,8OAAC,wIAAK;oDACJ,KAAK,KAAK,IAAI;oDACd,KAAK,GAAG,KAAK,IAAI,CAAC,KAAK,CAAC;oDACxB,OAAO;oDACP,QAAQ;oDACR,WAAU;;;;;;gDAEX,KAAK,SAAS,kBACb,8OAAC,yKAAW;oDAAC,MAAK;oDAAmB,WAAU;;;;;;;2CAf5C,KAAK,IAAI;;;;;;;;;;;0CAsBpB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,8KAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQjC", "debugId": null}}, {"offset": {"line": 613, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/webs/apiflow/packages/website/src/components/seo/StructuredData.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect } from 'react';\n\nexport default function StructuredData() {\n  useEffect(() => {\n    const organizationSchema = {\n      \"@context\": \"https://schema.org\",\n      \"@type\": \"Organization\",\n      \"name\": \"Apiflow\",\n      \"description\": \"The modern API documentation and testing tool that developers love. Build, test, and document your APIs with ease.\",\n      \"url\": \"https://apiflow.com\",\n      \"logo\": \"https://apiflow.com/logo.png\",\n      \"foundingDate\": \"2022\",\n      \"founders\": [\n        {\n          \"@type\": \"Person\",\n          \"name\": \"<PERSON>\"\n        },\n        {\n          \"@type\": \"Person\", \n          \"name\": \"<PERSON>\"\n        }\n      ],\n      \"contactPoint\": {\n        \"@type\": \"ContactPoint\",\n        \"telephone\": \"******-123-4567\",\n        \"contactType\": \"customer service\",\n        \"email\": \"<EMAIL>\",\n        \"availableLanguage\": \"English\"\n      },\n      \"sameAs\": [\n        \"https://twitter.com/apiflow\",\n        \"https://github.com/apiflow\",\n        \"https://linkedin.com/company/apiflow\"\n      ]\n    };\n\n    const softwareSchema = {\n      \"@context\": \"https://schema.org\",\n      \"@type\": \"SoftwareApplication\",\n      \"name\": \"Apiflow\",\n      \"description\": \"Modern API documentation and testing tool. A powerful alternative to Postman and Apifox for developers.\",\n      \"url\": \"https://apiflow.com\",\n      \"applicationCategory\": \"DeveloperApplication\",\n      \"operatingSystem\": \"Web, Windows, macOS, Linux\",\n      \"offers\": [\n        {\n          \"@type\": \"Offer\",\n          \"name\": \"Free Plan\",\n          \"price\": \"0\",\n          \"priceCurrency\": \"USD\",\n          \"description\": \"Perfect for individual developers and small projects\"\n        },\n        {\n          \"@type\": \"Offer\",\n          \"name\": \"Pro Plan\",\n          \"price\": \"29\",\n          \"priceCurrency\": \"USD\",\n          \"billingIncrement\": \"P1M\",\n          \"description\": \"Ideal for growing teams and professional projects\"\n        }\n      ],\n      \"aggregateRating\": {\n        \"@type\": \"AggregateRating\",\n        \"ratingValue\": \"4.9\",\n        \"ratingCount\": \"1250\",\n        \"bestRating\": \"5\",\n        \"worstRating\": \"1\"\n      },\n      \"featureList\": [\n        \"API Testing & Documentation\",\n        \"Real-time Collaboration\", \n        \"Advanced Mock Servers\",\n        \"Automated Testing Workflows\",\n        \"GraphQL Support\",\n        \"Enterprise Security\"\n      ]\n    };\n\n    const websiteSchema = {\n      \"@context\": \"https://schema.org\",\n      \"@type\": \"WebSite\",\n      \"name\": \"Apiflow\",\n      \"url\": \"https://apiflow.com\",\n      \"description\": \"The modern API documentation and testing tool that developers love.\",\n      \"potentialAction\": {\n        \"@type\": \"SearchAction\",\n        \"target\": \"https://apiflow.com/search?q={search_term_string}\",\n        \"query-input\": \"required name=search_term_string\"\n      }\n    };\n\n    const faqSchema = {\n      \"@context\": \"https://schema.org\",\n      \"@type\": \"FAQPage\",\n      \"mainEntity\": [\n        {\n          \"@type\": \"Question\",\n          \"name\": \"How does Apiflow compare to Postman?\",\n          \"acceptedAnswer\": {\n            \"@type\": \"Answer\",\n            \"text\": \"Apiflow offers 3x faster API testing workflows, better real-time collaboration, and enterprise-ready features at a more affordable price point compared to Postman.\"\n          }\n        },\n        {\n          \"@type\": \"Question\",\n          \"name\": \"Can I import my existing Postman collections?\",\n          \"acceptedAnswer\": {\n            \"@type\": \"Answer\",\n            \"text\": \"Yes! We provide easy import tools to migrate your collections from Postman, Insomnia, and other popular API tools seamlessly.\"\n          }\n        },\n        {\n          \"@type\": \"Question\",\n          \"name\": \"Is there a free plan available?\",\n          \"acceptedAnswer\": {\n            \"@type\": \"Answer\",\n            \"text\": \"Yes, we offer a generous free plan that includes up to 5 API collections, basic testing & documentation, and community support.\"\n          }\n        }\n      ]\n    };\n\n    const schemas = [organizationSchema, softwareSchema, websiteSchema, faqSchema];\n    \n    // 向头部添加结构化数据脚本\n    schemas.forEach((schema) => {\n      const script = document.createElement('script');\n      script.type = 'application/ld+json';\n      script.textContent = JSON.stringify(schema);\n      document.head.appendChild(script);\n    });\n\n    // 组件卸载时清理函数，移除脚本\n    return () => {\n      const scripts = document.head.querySelectorAll('script[type=\"application/ld+json\"]');\n      scripts.forEach(script => script.remove());\n    };\n  }, []);\n\n  return null; // 此组件不渲染任何可见内容\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;AAIe,SAAS;IACtB,IAAA,kNAAS,EAAC;QACR,MAAM,qBAAqB;YACzB,YAAY;YACZ,SAAS;YACT,QAAQ;YACR,eAAe;YACf,OAAO;YACP,QAAQ;YACR,gBAAgB;YAChB,YAAY;gBACV;oBACE,SAAS;oBACT,QAAQ;gBACV;gBACA;oBACE,SAAS;oBACT,QAAQ;gBACV;aACD;YACD,gBAAgB;gBACd,SAAS;gBACT,aAAa;gBACb,eAAe;gBACf,SAAS;gBACT,qBAAqB;YACvB;YACA,UAAU;gBACR;gBACA;gBACA;aACD;QACH;QAEA,MAAM,iBAAiB;YACrB,YAAY;YACZ,SAAS;YACT,QAAQ;YACR,eAAe;YACf,OAAO;YACP,uBAAuB;YACvB,mBAAmB;YACnB,UAAU;gBACR;oBACE,SAAS;oBACT,QAAQ;oBACR,SAAS;oBACT,iBAAiB;oBACjB,eAAe;gBACjB;gBACA;oBACE,SAAS;oBACT,QAAQ;oBACR,SAAS;oBACT,iBAAiB;oBACjB,oBAAoB;oBACpB,eAAe;gBACjB;aACD;YACD,mBAAmB;gBACjB,SAAS;gBACT,eAAe;gBACf,eAAe;gBACf,cAAc;gBACd,eAAe;YACjB;YACA,eAAe;gBACb;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;QACH;QAEA,MAAM,gBAAgB;YACpB,YAAY;YACZ,SAAS;YACT,QAAQ;YACR,OAAO;YACP,eAAe;YACf,mBAAmB;gBACjB,SAAS;gBACT,UAAU;gBACV,eAAe;YACjB;QACF;QAEA,MAAM,YAAY;YAChB,YAAY;YACZ,SAAS;YACT,cAAc;gBACZ;oBACE,SAAS;oBACT,QAAQ;oBACR,kBAAkB;wBAChB,SAAS;wBACT,QAAQ;oBACV;gBACF;gBACA;oBACE,SAAS;oBACT,QAAQ;oBACR,kBAAkB;wBAChB,SAAS;wBACT,QAAQ;oBACV;gBACF;gBACA;oBACE,SAAS;oBACT,QAAQ;oBACR,kBAAkB;wBAChB,SAAS;wBACT,QAAQ;oBACV;gBACF;aACD;QACH;QAEA,MAAM,UAAU;YAAC;YAAoB;YAAgB;YAAe;SAAU;QAE9E,eAAe;QACf,QAAQ,OAAO,CAAC,CAAC;YACf,MAAM,SAAS,SAAS,aAAa,CAAC;YACtC,OAAO,IAAI,GAAG;YACd,OAAO,WAAW,GAAG,KAAK,SAAS,CAAC;YACpC,SAAS,IAAI,CAAC,WAAW,CAAC;QAC5B;QAEA,iBAAiB;QACjB,OAAO;YACL,MAAM,UAAU,SAAS,IAAI,CAAC,gBAAgB,CAAC;YAC/C,QAAQ,OAAO,CAAC,CAAA,SAAU,OAAO,MAAM;QACzC;IACF,GAAG,EAAE;IAEL,OAAO,MAAM,eAAe;AAC9B", "debugId": null}}]}