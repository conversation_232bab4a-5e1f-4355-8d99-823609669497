{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_64d8cd1e._.js", "server/edge/chunks/[root-of-the-server]__ee6b1bdb._.js", "server/edge/chunks/turbopack-packages_website_edge-wrapper_d0b1f80c.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next|_vercel|.*\\..*).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next|_vercel|.*\\..*).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "afg8pKhnCsgeS22ryFG1dd5x9bVj9fneOlAn7UPDJog=", "__NEXT_PREVIEW_MODE_ID": "d5714a047450797329e3964f92ea0edd", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "9b5aea8f40e25f4b11e2b38b795cc6a0a34db81f8bff00c9f4adbc0bfc581325", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "f1b1b32d8ca43bec4eceee094a9c803e14e36a47a913e5fe68740bc03c51ede3"}}}, "sortedMiddleware": ["/"], "functions": {}}