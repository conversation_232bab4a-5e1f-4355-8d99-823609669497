{"功能特性": "功能特性", "产品演示": "产品演示", "关于我们": "关于我们", "联系我们": "联系我们", "GitHub": "GitHub", "🚀 新功能：高级 API 测试功能现已上线": "🚀 新功能：高级 API 测试功能现已上线", "Postman 和 Apifox 的": "Postman 和 Apifox 的", "免费开源替代品": "免费开源替代品", "下载互联网版本": "下载互联网版本", "下载客户端": "下载客户端", "开发者": "开发者", "评分": "评分", "正常运行时间": "正常运行时间", "离线运行": "离线运行", "除协作外可以使用所有功能": "除协作外可以使用所有功能", "团队协作": "团队协作", "我们提供实时协作功能": "我们提供实时协作功能", "本地部署": "本地部署", "所有功能支持Docker本地部署": "所有功能支持Docker本地部署", "数据自由": "数据自由", "可随时导出数据到其他工具": "可随时导出数据到其他工具", "完全免费": "完全免费", "所有核心功能永久免费使用": "所有核心功能永久免费使用", "协议支持": "协议支持", "支持HTTP、WebSocket、GraphQL等协议": "支持HTTP、WebSocket、GraphQL等协议", "在 Vibe Coding 的支持下，个人开发者或小团队同样能够构建媲美企业级应用的产品，欢迎 Star，支持我们走得更远": "在 Vibe Coding 的支持下，个人开发者或小团队同样能够构建媲美企业级应用的产品，欢迎 Star，支持我们走得更远", "为什么选择 Apiflow？": "为什么选择 Apiflow？", "其他工具有的功能我们也有，其他工具收费的功能我们免费": "其他工具有的功能我们也有，其他工具收费的功能我们免费", "如果发现我们还有不支持的功能，请稍等一下，过两天就有了": "如果发现我们还有不支持的功能，请稍等一下，过两天就有了", "功能对比": "功能对比", "我们正在努力追赶与竞品之间的差距": "我们正在努力追赶与竞品之间的差距", "核心功能": "核心功能", "API 测试与文档": "API 测试与文档", "实时协作": "实时协作", "高级模拟服务器": "高级模拟服务器", "自动化测试工作流": "自动化测试工作流", "GraphQL 支持": "GraphQL 支持", "开发者体验": "开发者体验", "直观界面": "直观界面", "快速性能": "快速性能", "离线模式": "离线模式", "自定义主题": "自定义主题", "插件生态系统": "插件生态系统", "企业功能": "企业功能", "SSO 集成": "SSO 集成", "高级安全": "高级安全", "自定义部署": "自定义部署", "优先支持": "优先支持", "合规就绪": "合规就绪", "有限": "有限", "付费": "付费", "良好": "良好", "较慢": "较慢", "企业版": "企业版", "准备体验不同之处？": "准备体验不同之处？", "开始免费试用": "开始免费试用", "开发者喜爱的现代化 API 文档与测试工具。轻松构建、测试和记录您的 API。": "开发者喜爱的现代化 API 文档与测试工具。轻松构建、测试和记录您的 API。", "产品": "产品", "公司": "公司", "资源": "资源", "法律": "法律", "功能": "功能", "演示": "演示", "文档": "文档", "博客": "博客", "招聘": "招聘", "帮助中心": "帮助中心", "API 参考": "API 参考", "社区": "社区", "状态": "状态", "隐私政策": "隐私政策", "服务条款": "服务条款", "Cookie 政策": "<PERSON><PERSON> 政策", "安全": "安全", "为全世界的开发者用 ❤️ 构建": "为全世界的开发者用 ❤️ 构建", "开源项目": "开源项目", "Apiflow 是一个开源项目，欢迎社区贡献": "Apiflow 是一个开源项目，欢迎社区贡献", "在 GitHub 上加星": "在 GitHub 上加星", "在 Gitee 上查看": "在 Gitee 上查看", "贡献代码": "贡献代码", "MIT 许可证": "MIT 许可证"}