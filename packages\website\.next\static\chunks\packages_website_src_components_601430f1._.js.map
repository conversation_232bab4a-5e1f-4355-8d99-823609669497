{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/webs/apiflow/packages/website/src/components/ui/LanguageSwitcher.tsx"], "sourcesContent": ["'use client';\n\nimport { useLocale } from 'next-intl';\nimport { useRouter, usePathname } from 'next/navigation';\nimport { Globe } from 'lucide-react';\nimport { useState } from 'react';\n\nconst languages = [\n  { code: 'zh', name: '中文', flag: '🇨🇳' },\n  { code: 'en', name: 'English', flag: '🇺🇸' }\n];\n\nexport default function LanguageSwitcher() {\n  const locale = useLocale();\n  const router = useRouter();\n  const pathname = usePathname();\n  const [isOpen, setIsOpen] = useState(false);\n\n  const currentLanguage = languages.find(lang => lang.code === locale) || languages[0]; // 默认显示中文\n\n  const handleLanguageChange = (newLocale: string) => {\n    // 从路径名中移除当前语言环境\n    const pathWithoutLocale = pathname.replace(`/${locale}`, '') || '/';\n    \n    // 导航到新的语言环境\n    router.push(`/${newLocale}${pathWithoutLocale}`);\n    setIsOpen(false);\n  };\n\n  // 如果没有找到当前语言，默认显示中文\n  const displayLanguage = currentLanguage || { code: 'zh', name: '中文', flag: '🇨🇳' };\n\n  return (\n    <div className=\"relative\">\n      <button\n        onClick={() => setIsOpen(!isOpen)}\n        className=\"flex items-center space-x-2 px-3 py-2 rounded-lg hover:bg-gray-100 transition-colors duration-200\"\n        aria-label=\"Switch language\"\n      >\n        <Globe className=\"w-4 h-4 text-gray-600\" />\n        <span className=\"text-sm font-medium text-gray-700\">\n          {displayLanguage.flag} {displayLanguage.name}\n        </span>\n        <svg\n          className={`w-4 h-4 text-gray-600 transition-transform duration-200 ${\n            isOpen ? 'rotate-180' : ''\n          }`}\n          fill=\"none\"\n          stroke=\"currentColor\"\n          viewBox=\"0 0 24 24\"\n        >\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 9l-7 7-7-7\" />\n        </svg>\n      </button>\n\n      {isOpen && (\n        <>\n          {/* 背景遮罩 */}\n          <div\n            className=\"fixed inset-0 z-10\"\n            onClick={() => setIsOpen(false)}\n          />\n          \n          {/* 下拉菜单 */}\n          <div className=\"absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 z-20\">\n            <div className=\"py-2\">\n              {languages.map((language) => (\n                <button\n                  key={language.code}\n                  onClick={() => handleLanguageChange(language.code)}\n                  className={`w-full text-left px-4 py-2 text-sm hover:bg-gray-50 transition-colors duration-200 flex items-center space-x-3 ${\n                    locale === language.code ? 'bg-blue-50 text-blue-600' : 'text-gray-700'\n                  }`}\n                >\n                  <span className=\"text-lg\">{language.flag}</span>\n                  <span className=\"font-medium\">{language.name}</span>\n                  {locale === language.code && (\n                    <svg className=\"w-4 h-4 ml-auto\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                      <path fillRule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\n                    </svg>\n                  )}\n                </button>\n              ))}\n            </div>\n          </div>\n        </>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAOA,MAAM,YAAY;IAChB;QAAE,MAAM;QAAM,MAAM;QAAM,MAAM;IAAO;IACvC;QAAE,MAAM;QAAM,MAAM;QAAW,MAAM;IAAO;CAC7C;AAEc,SAAS;;IACtB,MAAM,SAAS,IAAA,kLAAS;IACxB,MAAM,SAAS,IAAA,kJAAS;IACxB,MAAM,WAAW,IAAA,oJAAW;IAC5B,MAAM,CAAC,QAAQ,UAAU,GAAG,IAAA,yKAAQ,EAAC;IAErC,MAAM,kBAAkB,UAAU,IAAI,CAAC,CAAA,OAAQ,KAAK,IAAI,KAAK,WAAW,SAAS,CAAC,EAAE,EAAE,SAAS;IAE/F,MAAM,uBAAuB,CAAC;QAC5B,gBAAgB;QAChB,MAAM,oBAAoB,SAAS,OAAO,CAAC,AAAC,IAAU,OAAP,SAAU,OAAO;QAEhE,YAAY;QACZ,OAAO,IAAI,CAAC,AAAC,IAAe,OAAZ,WAA8B,OAAlB;QAC5B,UAAU;IACZ;IAEA,oBAAoB;IACpB,MAAM,kBAAkB,mBAAmB;QAAE,MAAM;QAAM,MAAM;QAAM,MAAM;IAAO;IAElF,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBACC,SAAS,IAAM,UAAU,CAAC;gBAC1B,WAAU;gBACV,cAAW;;kCAEX,6LAAC,gNAAK;wBAAC,WAAU;;;;;;kCACjB,6LAAC;wBAAK,WAAU;;4BACb,gBAAgB,IAAI;4BAAC;4BAAE,gBAAgB,IAAI;;;;;;;kCAE9C,6LAAC;wBACC,WAAW,AAAC,2DAEX,OADC,SAAS,eAAe;wBAE1B,MAAK;wBACL,QAAO;wBACP,SAAQ;kCAER,cAAA,6LAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;;;;;;;YAIxE,wBACC;;kCAEE,6LAAC;wBACC,WAAU;wBACV,SAAS,IAAM,UAAU;;;;;;kCAI3B,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACZ,UAAU,GAAG,CAAC,CAAC,yBACd,6LAAC;oCAEC,SAAS,IAAM,qBAAqB,SAAS,IAAI;oCACjD,WAAW,AAAC,kHAEX,OADC,WAAW,SAAS,IAAI,GAAG,6BAA6B;;sDAG1D,6LAAC;4CAAK,WAAU;sDAAW,SAAS,IAAI;;;;;;sDACxC,6LAAC;4CAAK,WAAU;sDAAe,SAAS,IAAI;;;;;;wCAC3C,WAAW,SAAS,IAAI,kBACvB,6LAAC;4CAAI,WAAU;4CAAkB,MAAK;4CAAe,SAAQ;sDAC3D,cAAA,6LAAC;gDAAK,UAAS;gDAAU,GAAE;gDAAqH,UAAS;;;;;;;;;;;;mCAVxJ,SAAS,IAAI;;;;;;;;;;;;;;;;;;;;;;;AAqBpC;GA7EwB;;QACP,kLAAS;QACT,kJAAS;QACP,oJAAW;;;KAHN", "debugId": null}}, {"offset": {"line": 199, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/webs/apiflow/packages/website/src/components/ui/GitHubStars.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { Star } from 'lucide-react';\n\ninterface GitHubStarsProps {\n  repo: string; // 格式: \"owner/repo\"\n  className?: string;\n}\n\nexport default function GitHubStars({ repo, className = '' }: GitHubStarsProps) {\n  const [stars, setStars] = useState<number | null>(null);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    const fetchStars = async () => {\n      try {\n        const response = await fetch(`https://api.github.com/repos/${repo}`);\n        if (response.ok) {\n          const data = await response.json();\n          setStars(data.stargazers_count);\n        }\n      } catch (error) {\n        console.error('Failed to fetch GitHub stars:', error);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchStars();\n  }, [repo]);\n\n  const formatStars = (count: number) => {\n    if (count >= 1000) {\n      return `${(count / 1000).toFixed(1)}k`;\n    }\n    return count.toString();\n  };\n\n  if (loading) {\n    return (\n      <div className={`inline-flex items-center space-x-1 ${className}`}>\n        <Star className=\"w-4 h-4 text-yellow-500\" />\n        <span className=\"text-sm text-gray-600\">...</span>\n      </div>\n    );\n  }\n\n  if (stars === null) {\n    return null;\n  }\n\n  return (\n    <div className={`inline-flex items-center space-x-1 ${className}`}>\n      <Star className=\"w-4 h-4 text-yellow-500 fill-current\" />\n      <span className=\"text-sm font-medium text-gray-700\">{formatStars(stars)}</span>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;;;AAHA;;;AAUe,SAAS,YAAY,KAA0C;QAA1C,EAAE,IAAI,EAAE,YAAY,EAAE,EAAoB,GAA1C;;IAClC,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,yKAAQ,EAAgB;IAClD,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,yKAAQ,EAAC;IAEvC,IAAA,0KAAS;iCAAC;YACR,MAAM;oDAAa;oBACjB,IAAI;wBACF,MAAM,WAAW,MAAM,MAAM,AAAC,gCAAoC,OAAL;wBAC7D,IAAI,SAAS,EAAE,EAAE;4BACf,MAAM,OAAO,MAAM,SAAS,IAAI;4BAChC,SAAS,KAAK,gBAAgB;wBAChC;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,iCAAiC;oBACjD,SAAU;wBACR,WAAW;oBACb;gBACF;;YAEA;QACF;gCAAG;QAAC;KAAK;IAET,MAAM,cAAc,CAAC;QACnB,IAAI,SAAS,MAAM;YACjB,OAAO,AAAC,GAA4B,OAA1B,CAAC,QAAQ,IAAI,EAAE,OAAO,CAAC,IAAG;QACtC;QACA,OAAO,MAAM,QAAQ;IACvB;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAW,AAAC,sCAA+C,OAAV;;8BACpD,6LAAC,6MAAI;oBAAC,WAAU;;;;;;8BAChB,6LAAC;oBAAK,WAAU;8BAAwB;;;;;;;;;;;;IAG9C;IAEA,IAAI,UAAU,MAAM;QAClB,OAAO;IACT;IAEA,qBACE,6LAAC;QAAI,WAAW,AAAC,sCAA+C,OAAV;;0BACpD,6LAAC,6MAAI;gBAAC,WAAU;;;;;;0BAChB,6LAAC;gBAAK,WAAU;0BAAqC,YAAY;;;;;;;;;;;;AAGvE;GAhDwB;KAAA", "debugId": null}}, {"offset": {"line": 309, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/webs/apiflow/packages/website/src/components/layout/Header.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Link from 'next/link';\nimport Image from 'next/image';\nimport { Menu, X } from 'lucide-react';\nimport { useTranslations } from 'next-intl';\nimport LanguageSwitcher from '@/components/ui/LanguageSwitcher';\nimport GitHubStars from '@/components/ui/GitHubStars';\n\nexport default function Header() {\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n  const t = useTranslations();\n\n  const navigation = [\n    { name: t('功能特性'), href: '#features' },\n    { name: t('产品演示'), href: '#demo' },\n    { name: t('关于我们'), href: '#about' },\n    { name: t('联系我们'), href: '#contact' },\n  ];\n\n  const repositoryLinks = [\n    {\n      name: 'GitHub',\n      href: 'https://github.com/trueleaf/apiflow',\n      icon: '/github.svg',\n      showStars: true\n    }\n  ];\n\n  return (\n    <header className=\"fixed top-0 w-full bg-white/95 backdrop-blur-sm border-b border-gray-200 z-50\">\n      <nav className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex items-center py-4\">\n          {/* 左侧 - Logo */}\n          <div className=\"flex items-center\">\n            <Link href=\"/\" className=\"flex items-center space-x-2\">\n              <Image\n                src=\"/logo.png\"\n                alt=\"Apiflow Logo\"\n                width={32}\n                height={32}\n                className=\"w-8 h-8\"\n              />\n              <span className=\"text-xl font-bold text-gray-900\">Apiflow</span>\n            </Link>\n          </div>\n\n          {/* 右侧 - 导航和操作 */}\n          <div className=\"flex-1 flex justify-end items-center\">\n            {/* 桌面端导航 */}\n            <div className=\"hidden md:flex items-center space-x-8 mr-8\">\n              {navigation.map((item) => (\n                <Link\n                  key={item.name}\n                  href={item.href}\n                  className=\"text-gray-600 hover:text-blue-600 transition-colors duration-200 font-medium\"\n                >\n                  {item.name}\n                </Link>\n              ))}\n            </div>\n\n            {/* 仓库链接和语言切换器 */}\n            <div className=\"hidden md:flex items-center space-x-4\">\n              {/* 仓库链接 */}\n              <div className=\"flex items-center space-x-3\">\n                {repositoryLinks.map((repo) => (\n                  <Link\n                    key={repo.name}\n                    href={repo.href}\n                    target=\"_blank\"\n                    rel=\"noopener noreferrer\"\n                    className=\"flex items-center space-x-2 text-gray-600 hover:text-blue-600 transition-colors duration-200 text-sm\"\n                  >\n                    <Image \n                      src={repo.icon} \n                      alt={`${repo.name} icon`}\n                      width={16} \n                      height={16} \n                      className=\"w-4 h-4\" \n                    />\n                    {repo.showStars && (\n                      <GitHubStars repo=\"trueleaf/apiflow\" className=\"ml-1\" />\n                    )}\n                  </Link>\n                ))}\n              </div>\n\n              {/* 语言切换器 */}\n              <LanguageSwitcher />\n            </div>\n\n            {/* 移动端菜单按钮 */}\n            <div className=\"md:hidden\">\n              <button\n                onClick={() => setIsMenuOpen(!isMenuOpen)}\n                className=\"text-gray-600 hover:text-gray-900 transition-colors duration-200\"\n              >\n                {isMenuOpen ? <X size={24} /> : <Menu size={24} />}\n              </button>\n            </div>\n          </div>\n        </div>\n\n        {/* 移动端导航 */}\n        {isMenuOpen && (\n          <div className=\"md:hidden\">\n            <div className=\"px-2 pt-2 pb-3 space-y-1 bg-white border-t border-gray-200\">\n              {navigation.map((item) => (\n                <Link\n                  key={item.name}\n                  href={item.href}\n                  className=\"block px-3 py-2 text-gray-600 hover:text-blue-600 transition-colors duration-200 font-medium\"\n                  onClick={() => setIsMenuOpen(false)}\n                >\n                  {item.name}\n                </Link>\n              ))}\n\n              {/* 移动端仓库链接 */}\n              <div className=\"pt-4 border-t border-gray-200\">\n                <div className=\"px-3 py-2 text-sm font-medium text-gray-500\">\n                  Open Source\n                </div>\n                {repositoryLinks.map((repo) => (\n                  <Link\n                    key={repo.name}\n                    href={repo.href}\n                    target=\"_blank\"\n                    rel=\"noopener noreferrer\"\n                    className=\"flex items-center space-x-2 px-3 py-2 text-gray-600 hover:text-blue-600 transition-colors duration-200\"\n                    onClick={() => setIsMenuOpen(false)}\n                  >\n                    <Image \n                      src={repo.icon} \n                      alt={`${repo.name} icon`}\n                      width={16} \n                      height={16} \n                      className=\"w-4 h-4\" \n                    />\n                    {repo.showStars && (\n                      <GitHubStars repo=\"trueleaf/apiflow\" className=\"ml-auto\" />\n                    )}\n                  </Link>\n                ))}\n              </div>\n\n              {/* 移动端语言切换器 */}\n              <div className=\"pt-4 px-3\">\n                <LanguageSwitcher />\n              </div>\n            </div>\n          </div>\n        )}\n      </nav>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;;;AARA;;;;;;;;AAUe,SAAS;;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,yKAAQ,EAAC;IAC7C,MAAM,IAAI,IAAA,4NAAe;IAEzB,MAAM,aAAa;QACjB;YAAE,MAAM,EAAE;YAAS,MAAM;QAAY;QACrC;YAAE,MAAM,EAAE;YAAS,MAAM;QAAQ;QACjC;YAAE,MAAM,EAAE;YAAS,MAAM;QAAS;QAClC;YAAE,MAAM,EAAE;YAAS,MAAM;QAAW;KACrC;IAED,MAAM,kBAAkB;QACtB;YACE,MAAM;YACN,MAAM;YACN,MAAM;YACN,WAAW;QACb;KACD;IAED,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,0KAAI;gCAAC,MAAK;gCAAI,WAAU;;kDACvB,6LAAC,2IAAK;wCACJ,KAAI;wCACJ,KAAI;wCACJ,OAAO;wCACP,QAAQ;wCACR,WAAU;;;;;;kDAEZ,6LAAC;wCAAK,WAAU;kDAAkC;;;;;;;;;;;;;;;;;sCAKtD,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;8CACZ,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC,0KAAI;4CAEH,MAAM,KAAK,IAAI;4CACf,WAAU;sDAET,KAAK,IAAI;2CAJL,KAAK,IAAI;;;;;;;;;;8CAUpB,6LAAC;oCAAI,WAAU;;sDAEb,6LAAC;4CAAI,WAAU;sDACZ,gBAAgB,GAAG,CAAC,CAAC,qBACpB,6LAAC,0KAAI;oDAEH,MAAM,KAAK,IAAI;oDACf,QAAO;oDACP,KAAI;oDACJ,WAAU;;sEAEV,6LAAC,2IAAK;4DACJ,KAAK,KAAK,IAAI;4DACd,KAAK,AAAC,GAAY,OAAV,KAAK,IAAI,EAAC;4DAClB,OAAO;4DACP,QAAQ;4DACR,WAAU;;;;;;wDAEX,KAAK,SAAS,kBACb,6LAAC,4KAAW;4DAAC,MAAK;4DAAmB,WAAU;;;;;;;mDAd5C,KAAK,IAAI;;;;;;;;;;sDAqBpB,6LAAC,iLAAgB;;;;;;;;;;;8CAInB,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCACC,SAAS,IAAM,cAAc,CAAC;wCAC9B,WAAU;kDAET,2BAAa,6LAAC,oMAAC;4CAAC,MAAM;;;;;iEAAS,6LAAC,6MAAI;4CAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBAOnD,4BACC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;4BACZ,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC,0KAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAU;oCACV,SAAS,IAAM,cAAc;8CAE5B,KAAK,IAAI;mCALL,KAAK,IAAI;;;;;0CAUlB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDAA8C;;;;;;oCAG5D,gBAAgB,GAAG,CAAC,CAAC,qBACpB,6LAAC,0KAAI;4CAEH,MAAM,KAAK,IAAI;4CACf,QAAO;4CACP,KAAI;4CACJ,WAAU;4CACV,SAAS,IAAM,cAAc;;8DAE7B,6LAAC,2IAAK;oDACJ,KAAK,KAAK,IAAI;oDACd,KAAK,AAAC,GAAY,OAAV,KAAK,IAAI,EAAC;oDAClB,OAAO;oDACP,QAAQ;oDACR,WAAU;;;;;;gDAEX,KAAK,SAAS,kBACb,6LAAC,4KAAW;oDAAC,MAAK;oDAAmB,WAAU;;;;;;;2CAf5C,KAAK,IAAI;;;;;;;;;;;0CAsBpB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,iLAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQjC;GApJwB;;QAEZ,4NAAe;;;KAFH", "debugId": null}}, {"offset": {"line": 631, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/webs/apiflow/packages/website/src/components/seo/StructuredData.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect } from 'react';\n\nexport default function StructuredData() {\n  useEffect(() => {\n    const organizationSchema = {\n      \"@context\": \"https://schema.org\",\n      \"@type\": \"Organization\",\n      \"name\": \"Apiflow\",\n      \"description\": \"The modern API documentation and testing tool that developers love. Build, test, and document your APIs with ease.\",\n      \"url\": \"https://apiflow.com\",\n      \"logo\": \"https://apiflow.com/logo.png\",\n      \"foundingDate\": \"2022\",\n      \"founders\": [\n        {\n          \"@type\": \"Person\",\n          \"name\": \"<PERSON>\"\n        },\n        {\n          \"@type\": \"Person\", \n          \"name\": \"<PERSON>\"\n        }\n      ],\n      \"contactPoint\": {\n        \"@type\": \"ContactPoint\",\n        \"telephone\": \"******-123-4567\",\n        \"contactType\": \"customer service\",\n        \"email\": \"<EMAIL>\",\n        \"availableLanguage\": \"English\"\n      },\n      \"sameAs\": [\n        \"https://twitter.com/apiflow\",\n        \"https://github.com/apiflow\",\n        \"https://linkedin.com/company/apiflow\"\n      ]\n    };\n\n    const softwareSchema = {\n      \"@context\": \"https://schema.org\",\n      \"@type\": \"SoftwareApplication\",\n      \"name\": \"Apiflow\",\n      \"description\": \"Modern API documentation and testing tool. A powerful alternative to Postman and Apifox for developers.\",\n      \"url\": \"https://apiflow.com\",\n      \"applicationCategory\": \"DeveloperApplication\",\n      \"operatingSystem\": \"Web, Windows, macOS, Linux\",\n      \"offers\": [\n        {\n          \"@type\": \"Offer\",\n          \"name\": \"Free Plan\",\n          \"price\": \"0\",\n          \"priceCurrency\": \"USD\",\n          \"description\": \"Perfect for individual developers and small projects\"\n        },\n        {\n          \"@type\": \"Offer\",\n          \"name\": \"Pro Plan\",\n          \"price\": \"29\",\n          \"priceCurrency\": \"USD\",\n          \"billingIncrement\": \"P1M\",\n          \"description\": \"Ideal for growing teams and professional projects\"\n        }\n      ],\n      \"aggregateRating\": {\n        \"@type\": \"AggregateRating\",\n        \"ratingValue\": \"4.9\",\n        \"ratingCount\": \"1250\",\n        \"bestRating\": \"5\",\n        \"worstRating\": \"1\"\n      },\n      \"featureList\": [\n        \"API Testing & Documentation\",\n        \"Real-time Collaboration\", \n        \"Advanced Mock Servers\",\n        \"Automated Testing Workflows\",\n        \"GraphQL Support\",\n        \"Enterprise Security\"\n      ]\n    };\n\n    const websiteSchema = {\n      \"@context\": \"https://schema.org\",\n      \"@type\": \"WebSite\",\n      \"name\": \"Apiflow\",\n      \"url\": \"https://apiflow.com\",\n      \"description\": \"The modern API documentation and testing tool that developers love.\",\n      \"potentialAction\": {\n        \"@type\": \"SearchAction\",\n        \"target\": \"https://apiflow.com/search?q={search_term_string}\",\n        \"query-input\": \"required name=search_term_string\"\n      }\n    };\n\n    const faqSchema = {\n      \"@context\": \"https://schema.org\",\n      \"@type\": \"FAQPage\",\n      \"mainEntity\": [\n        {\n          \"@type\": \"Question\",\n          \"name\": \"How does Apiflow compare to Postman?\",\n          \"acceptedAnswer\": {\n            \"@type\": \"Answer\",\n            \"text\": \"Apiflow offers 3x faster API testing workflows, better real-time collaboration, and enterprise-ready features at a more affordable price point compared to Postman.\"\n          }\n        },\n        {\n          \"@type\": \"Question\",\n          \"name\": \"Can I import my existing Postman collections?\",\n          \"acceptedAnswer\": {\n            \"@type\": \"Answer\",\n            \"text\": \"Yes! We provide easy import tools to migrate your collections from Postman, Insomnia, and other popular API tools seamlessly.\"\n          }\n        },\n        {\n          \"@type\": \"Question\",\n          \"name\": \"Is there a free plan available?\",\n          \"acceptedAnswer\": {\n            \"@type\": \"Answer\",\n            \"text\": \"Yes, we offer a generous free plan that includes up to 5 API collections, basic testing & documentation, and community support.\"\n          }\n        }\n      ]\n    };\n\n    const schemas = [organizationSchema, softwareSchema, websiteSchema, faqSchema];\n    \n    // 向头部添加结构化数据脚本\n    schemas.forEach((schema) => {\n      const script = document.createElement('script');\n      script.type = 'application/ld+json';\n      script.textContent = JSON.stringify(schema);\n      document.head.appendChild(script);\n    });\n\n    // 组件卸载时清理函数，移除脚本\n    return () => {\n      const scripts = document.head.querySelectorAll('script[type=\"application/ld+json\"]');\n      scripts.forEach(script => script.remove());\n    };\n  }, []);\n\n  return null; // 此组件不渲染任何可见内容\n}\n"], "names": [], "mappings": ";;;;AAEA;;AAFA;;AAIe,SAAS;;IACtB,IAAA,0KAAS;oCAAC;YACR,MAAM,qBAAqB;gBACzB,YAAY;gBACZ,SAAS;gBACT,QAAQ;gBACR,eAAe;gBACf,OAAO;gBACP,QAAQ;gBACR,gBAAgB;gBAChB,YAAY;oBACV;wBACE,SAAS;wBACT,QAAQ;oBACV;oBACA;wBACE,SAAS;wBACT,QAAQ;oBACV;iBACD;gBACD,gBAAgB;oBACd,SAAS;oBACT,aAAa;oBACb,eAAe;oBACf,SAAS;oBACT,qBAAqB;gBACvB;gBACA,UAAU;oBACR;oBACA;oBACA;iBACD;YACH;YAEA,MAAM,iBAAiB;gBACrB,YAAY;gBACZ,SAAS;gBACT,QAAQ;gBACR,eAAe;gBACf,OAAO;gBACP,uBAAuB;gBACvB,mBAAmB;gBACnB,UAAU;oBACR;wBACE,SAAS;wBACT,QAAQ;wBACR,SAAS;wBACT,iBAAiB;wBACjB,eAAe;oBACjB;oBACA;wBACE,SAAS;wBACT,QAAQ;wBACR,SAAS;wBACT,iBAAiB;wBACjB,oBAAoB;wBACpB,eAAe;oBACjB;iBACD;gBACD,mBAAmB;oBACjB,SAAS;oBACT,eAAe;oBACf,eAAe;oBACf,cAAc;oBACd,eAAe;gBACjB;gBACA,eAAe;oBACb;oBACA;oBACA;oBACA;oBACA;oBACA;iBACD;YACH;YAEA,MAAM,gBAAgB;gBACpB,YAAY;gBACZ,SAAS;gBACT,QAAQ;gBACR,OAAO;gBACP,eAAe;gBACf,mBAAmB;oBACjB,SAAS;oBACT,UAAU;oBACV,eAAe;gBACjB;YACF;YAEA,MAAM,YAAY;gBAChB,YAAY;gBACZ,SAAS;gBACT,cAAc;oBACZ;wBACE,SAAS;wBACT,QAAQ;wBACR,kBAAkB;4BAChB,SAAS;4BACT,QAAQ;wBACV;oBACF;oBACA;wBACE,SAAS;wBACT,QAAQ;wBACR,kBAAkB;4BAChB,SAAS;4BACT,QAAQ;wBACV;oBACF;oBACA;wBACE,SAAS;wBACT,QAAQ;wBACR,kBAAkB;4BAChB,SAAS;4BACT,QAAQ;wBACV;oBACF;iBACD;YACH;YAEA,MAAM,UAAU;gBAAC;gBAAoB;gBAAgB;gBAAe;aAAU;YAE9E,eAAe;YACf,QAAQ,OAAO;4CAAC,CAAC;oBACf,MAAM,SAAS,SAAS,aAAa,CAAC;oBACtC,OAAO,IAAI,GAAG;oBACd,OAAO,WAAW,GAAG,KAAK,SAAS,CAAC;oBACpC,SAAS,IAAI,CAAC,WAAW,CAAC;gBAC5B;;YAEA,iBAAiB;YACjB;4CAAO;oBACL,MAAM,UAAU,SAAS,IAAI,CAAC,gBAAgB,CAAC;oBAC/C,QAAQ,OAAO;oDAAC,CAAA,SAAU,OAAO,MAAM;;gBACzC;;QACF;mCAAG,EAAE;IAEL,OAAO,MAAM,eAAe;AAC9B;GA1IwB;KAAA", "debugId": null}}]}