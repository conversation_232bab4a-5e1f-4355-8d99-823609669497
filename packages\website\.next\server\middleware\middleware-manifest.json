{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_64d8cd1e._.js", "server/edge/chunks/[root-of-the-server]__ee6b1bdb._.js", "server/edge/chunks/turbopack-packages_website_edge-wrapper_d0b1f80c.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!api|_next|_vercel|.*\\..*).*){(\\\\.json)}?", "originalSource": "/((?!api|_next|_vercel|.*\\..*).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "afg8pKhnCsgeS22ryFG1dd5x9bVj9fneOlAn7UPDJog=", "__NEXT_PREVIEW_MODE_ID": "44a8e06b94dcd00924b3d79d4a2bfa4b", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "b76b536f06de8e399a024da5d33c7869e0f64c1a49d623148b618c71ed22cd36", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "fdea9d63dec8acd423f392889ae2e6181c001d74d84e4f40c42d2d6c6b6220c2"}}}, "instrumentation": null, "functions": {}}