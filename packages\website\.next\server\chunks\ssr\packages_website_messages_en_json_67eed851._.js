module.exports = [
"[project]/packages/website/messages/en.json (json)", ((__turbopack_context__) => {

__turbopack_context__.v(JSON.parse("{\"功能特性\":\"Features\",\"产品演示\":\"Demo\",\"关于我们\":\"About\",\"联系我们\":\"Contact\",\"GitHub\":\"GitHub\",\"🚀 新功能：高级 API 测试功能现已上线\":\"🚀 New: Advanced API Testing Features Now Available\",\"Postman 和 Apifox 的\":\"Modern Alternative to\",\"免费开源替代品\":\"Postman & Apifox\",\"下载客户端\":\"Download Client\",\"开发者\":\"Developers\",\"评分\":\"Rating\",\"正常运行时间\":\"Uptime\",\"离线运行\":\"Offline Ready\",\"除协作外可以使用所有功能\":\"Switch to standalone mode for offline use\",\"团队协作\":\"Team Collaboration\",\"我们提供实时协作功能\":\"Real-time collaboration features included\",\"本地部署\":\"Local Deployment\",\"所有功能支持Docker本地部署\":\"Full Docker local deployment support\",\"数据自由\":\"Data Freedom\",\"可随时导出数据到其他工具\":\"Export data to other API tools anytime\",\"完全免费\":\"Completely Free\",\"所有核心功能永久免费使用\":\"All core features are permanently free\",\"协议支持\":\"Protocol Support\",\"支持HTTP、WebSocket、GraphQL等协议\":\"Supports HTTP, WebSocket, GraphQL and more\",\"在 Vibe Coding 的支持下，个人开发者或小团队同样能够构建媲美企业级应用的产品，欢迎 Star，支持我们走得更远\":\"With Vibe Coding's support, individual developers and small teams can build enterprise-grade applications. Welcome to Star and support us to go further\",\"为什么选择 Apiflow？\":\"Why Choose Apiflow?\",\"其他工具有的功能我们也有，其他工具收费的功能我们免费\":\"We have all the features that other tools have, and we offer for free what other tools charge for\",\"如果发现我们还有不支持的功能，请稍等一下，过两天就有了\":\"If you find features we don't support yet, please wait a moment - they'll be available in a couple of days\",\"功能对比\":\"Feature Comparison\",\"我们正在努力追赶与竞品之间的差距\":\"See how we stack up against the competition\",\"核心功能\":\"Core Features\",\"API 测试与文档\":\"API Testing & Documentation\",\"实时协作\":\"Real-time Collaboration\",\"高级模拟服务器\":\"Advanced Mock Servers\",\"自动化测试工作流\":\"Automated Testing Workflows\",\"GraphQL 支持\":\"GraphQL Support\",\"开发者体验\":\"Developer Experience\",\"直观界面\":\"Intuitive Interface\",\"快速性能\":\"Fast Performance\",\"离线模式\":\"Offline Mode\",\"自定义主题\":\"Custom Themes\",\"插件生态系统\":\"Plugin Ecosystem\",\"企业功能\":\"Enterprise Features\",\"SSO 集成\":\"SSO Integration\",\"高级安全\":\"Advanced Security\",\"自定义部署\":\"Custom Deployment\",\"优先支持\":\"Priority Support\",\"合规就绪\":\"Compliance Ready\",\"有限\":\"Limited\",\"付费\":\"Paid\",\"良好\":\"Good\",\"较慢\":\"Slow\",\"企业版\":\"Enterprise\",\"准备体验不同之处？\":\"Ready to experience the difference?\",\"开始免费试用\":\"Start Free Trial\",\"开发者喜爱的现代化 API 文档与测试工具。轻松构建、测试和记录您的 API。\":\"Modern API documentation and testing tool loved by developers. Build, test, and document your APIs with ease.\",\"产品\":\"Product\",\"公司\":\"Company\",\"资源\":\"Resources\",\"法律\":\"Legal\",\"功能\":\"Features\",\"演示\":\"Demo\",\"文档\":\"Documentation\",\"博客\":\"Blog\",\"招聘\":\"Careers\",\"帮助中心\":\"Help Center\",\"API 参考\":\"API Reference\",\"社区\":\"Community\",\"状态\":\"Status\",\"隐私政策\":\"Privacy Policy\",\"服务条款\":\"Terms of Service\",\"Cookie 政策\":\"Cookie Policy\",\"安全\":\"Security\",\"为全世界的开发者用 ❤️ 构建\":\"Built with ❤️ for developers worldwide\",\"开源项目\":\"Open Source\",\"Apiflow 是一个开源项目，欢迎社区贡献\":\"Apiflow is an open source project, community contributions welcome\",\"在 GitHub 上加星\":\"Star on GitHub\",\"在 Gitee 上查看\":\"View on Gitee\",\"贡献代码\":\"Contribute\",\"MIT 许可证\":\"MIT License\"}"));}),
];