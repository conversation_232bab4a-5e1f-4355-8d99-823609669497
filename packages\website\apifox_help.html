<!DOCTYPE html><html lang="zh-CN" class="group/root" id="html" data-theme="light" data-accent-color="purple"><head><script src="https://cdn.apifox.com/docs-site/assets/prepareDocsConfigScript-DwKyrslF.js"></script><script>__prepareDocsConfigScript(JSON.parse("{\"theme\":\"system\",\"themePrimarySettings\":{\"light\":{\"primaryColor\":\"purple\",\"textColor\":\"WHITE\",\"backgroundColor\":\"#ffffff\",\"accentColor\":\"purple\"},\"dark\":{\"primaryColor\":\"purple\",\"textColor\":\"WHITE\",\"backgroundColor\":\"#1a1922\",\"accentColor\":\"purple\"}},\"logoSettings\":{\"light\":\"https://cdn.apifox.com/app/project-icon/custom/20240903/5960e2f3-e538-486f-9c67-a2710de3b7b0.png\",\"dark\":\"https://cdn.apifox.com/app/project-icon/custom/20240903/5960e2f3-e538-486f-9c67-a2710de3b7b0.png\"},\"id\":5431806,\"subdirectory\":\"\"}"));</script><script>window.eventTracking = {
      dataLayer: []
    }
    window.eventTracking.report = function(){
        window.eventTracking.dataLayer.push(arguments);
    }</script><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="icon" href="https://cdn.apifox.com/app/apidoc-image/custom/20250109/3eb87359-988c-4309-be0e-5e5c212a8922.png"/><link rel="canonical" href="https://docs.apifox.com/help"/><meta name="description" content="甯姪涓績 - Apifox 甯姪鏂囨。"/><meta name="keywords" content="甯姪涓績, Apifox 甯姪鏂囨。, API 鍗忎綔宸ュ叿,鎺ュ彛鏂囨。,API 璋冭瘯,API Mock,API 鑷姩鍖栨祴璇?API 鏂囨。绠＄悊,鎺ュ彛鏂囨。鑷姩鐢熸垚宸ュ叿,鎺ュ彛璋冭瘯"/><title>甯姪涓績 - Apifox 甯姪鏂囨。</title><link rel="stylesheet" href="https://cdn.apifox.com/docs-site/assets/root-DthXKMUf.css"/></head><body class="overflow-hidden g-body"><style type="text/css">[data-theme=light][data-accent-color=purple] {--ui-primary-color-hover: var(--ui-primary-5);--ui-primary-color-active: var(--ui-primary-7);--ui-picker-basic-cell-hover-with-range-color: #36139c;--ui-picker-date-hover-range-border-color: #4e1be0;--ui-primary-base: #9373EE;--ui-primary-base-p: 147, 115, 238;--ui-primary-color-p: 147, 115, 238;--ui-primary-color: #9373EE;--ui-primary-10: #362b50;--ui-primary-9: #51407e;--ui-primary-8: #634e9b;--ui-primary-7: #7a60c4;--ui-primary-7-p: 122, 96, 196;--ui-primary-6: #9373EE;--ui-primary-6-p: 147, 115, 238;--ui-primary-5: #b499f4;--ui-primary-5-p: 180, 153, 244;--ui-primary-4: #cebaf8;--ui-primary-3: #dfd1fb;--ui-primary-2: #ebe2fc;--ui-primary-1: #f3eefe;--ui-primary-1-p: 243, 238, 254;--ui-primary-a: #f7f3fe;--ui-primary-b: #fbf9ff;--g-color-primary: #9373EE;--ui-btn-primary-color: #fff;--ui-lime-6-p: 160,217,17;--ui-orange-6-p: 250,140,22;--ui-yellow-6-p: 250,219,20;--ui-pink-6-p: 235,47,150;--ui-body-background: #ffffff;--theme-tars-item-active-bg: var(--ui-tree-node-selected-bg);--ifm-menu-color: var(--ui-text-color-sub);--ifm-menu-color-active: var(--ui-text-color-sub);--ifm-menu-color-background-hover: var(--ui-item-hover-bg);--theme-tars-border-color-split: var(--ui-border-color-split);}
              [data-theme=dark][data-accent-color=purple] {--ui-primary-color-hover: var(--ui-primary-7);--ui-primary-color-active: var(--ui-primary-5);--ui-picker-basic-cell-hover-with-range-color: #312865;--ui-picker-date-hover-range-border-color: #4b3d9c;--ui-primary-base: #8276C9;--ui-primary-base-p: 130, 118, 201;--ui-primary-color-p: 130, 118, 201;--ui-primary-color: #8276C9;--ui-primary-10: #dcd7f0;--ui-primary-9: #a69bd9;--ui-primary-8: #b0a5dd;--ui-primary-7: #9c90d4;--ui-primary-7-p: 156, 144, 212;--ui-primary-6: #8276C9;--ui-primary-6-p: 130, 118, 201;--ui-primary-5: #635a96;--ui-primary-5-p: 99, 90, 150;--ui-primary-4: #49436c;--ui-primary-3: #393452;--ui-primary-2: #312d46;--ui-primary-1: #252333;--ui-primary-1-p: 37, 35, 51;--ui-primary-a: #21202d;--ui-primary-b: #1e1c28;--g-color-primary: #8276C9;--ui-btn-primary-color: #fff;--ui-lime-6-p: 160,217,17;--ui-orange-6-p: 250,140,22;--ui-yellow-6-p: 250,219,20;--ui-pink-6-p: 235,47,150;--ui-body-background: #1a1922;--theme-tars-item-active-bg: var(--ui-tree-node-selected-bg);--ifm-menu-color: var(--ui-text-color-sub);--ifm-menu-color-active: var(--ui-text-color-sub);--ifm-menu-color-background-hover: var(--ui-item-hover-bg);--theme-tars-border-color-split: var(--ui-border-color-split);}</style><div class="flex h-full w-full flex-col"><div class="relative h-0 w-full flex-1"><div class="relative h-full w-full overflow-y-auto" id="_main-scroll-container_5d6f"><div class="pointer-events-none absolute inset-x-0 top-0 -z-10 h-[460px] max-h-full w-full overflow-hidden _background_yn525_1"></div><div class="g-header sticky top-0 z-20 mx-auto my-0 flex w-full flex-col backdrop-blur-lg bg-[#ffffff7a] dark:bg-transparent"><div class="g-header-top-navbar flex overflow-hidden py-2.5 os:justify-center h-[57px] border-b border-b-[#0000000a] dark:border-b-[#ffffff0f]"><div class="flex h-9 w-0 flex-1 items-center px-5 os:px-8 os:max-w-[1560px]"><div class="flex items-center max-os:w-0 max-os:flex-1"><a class="g-brand flex items-center truncate" data-discover="true" href="/"><div class="g-logo mr-3 flex-shrink-0"><img class="g-logo-img _brand-logo-img_24bb h-7 min-w-7 rounded-lg object-cover" src="https://cdn.apifox.com/app/project-icon/custom/20240903/5960e2f3-e538-486f-9c67-a2710de3b7b0.png" alt=""/></div><b class="g-title truncate text-paragraph font-600 text-color hover:text-primary">Apifox 甯姪鏂囨。</b></a><div class="flex-1"></div><div class="inline-flex h-8 w-8 cursor-pointer items-center justify-center rounded-lg _container_199t5_1 hidden"><button class="theme-toggle _expand-button_199t5_51 flex items-center" aria-label="Toggle theme"><svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" fill="currentColor" aria-hidden="true" class="theme-toggle-svg" viewBox="0 0 24 24"><clipPath id=":Rb59qn5:a"><path d="M0-11h19A1 1 0 0 0 36 2v22H0Z"></path></clipPath><g clip-path="url(#:Rb59qn5:a)"><circle cx="12" cy="12" r="6.4"></circle><path d="M13.7 2.7c0 1-.7 1.8-1.7 1.8s-1.8-.8-1.8-1.8S11 1 12 1s1.7.7 1.7 1.7zm-3.5 19.5c0-1 .8-1.7 1.8-1.7s1.6.6 1.6 1.6-.7 1.8-1.7 1.8-1.8-.8-1.8-1.8zm11.5-8c-1 0-1.7-.7-1.7-1.7s.7-1.8 1.7-1.8 1.8.8 1.8 1.8-.9 1.6-1.9 1.6zM2.2 10.7c1 0 1.8.8 1.8 1.8s-.8 1.7-1.8 1.7-1.7-.7-1.7-1.7.7-1.8 1.7-1.8zm4.4-5.3c0 1-.8 1.7-1.7 1.7-1 0-1.8-.7-1.8-1.7s.8-1.8 1.8-1.8 1.7.8 1.7 1.8zm12.5 16c-1 0-1.8-.8-1.8-1.8s.8-1.7 1.8-1.7 1.7.7 1.7 1.7-.8 1.8-1.7 1.8zm1.8-16c0 1-.8 1.7-1.8 1.7s-1.8-.8-1.8-1.7c0-1 .8-1.8 1.8-1.8s1.8.8 1.8 1.8zm-16 12.4c1 0 1.7.8 1.7 1.8s-.7 1.7-1.7 1.7-1.8-.8-1.8-1.7c0-1 .8-1.8 1.8-1.8z"></path></g></svg></button></div></div><div class="hidden w-0 flex-1 os:flex"><div class="g-header-top-navbar-left w-0 flex-1 os:pl-8"><div class="flex flex-nowrap items-center gap-2 overflow-hidden os:h-0 max-os:flex-col max-os:items-start max-os:gap-[1px] os:justify-end"><a class="relative inline-flex max-w-full flex-shrink-0 items-center os:max-w-[192px] px-3 py-[5px] max-os:py-2 g-header-top-navbar-left-item g-header-top-navbar-left-item-active" title="甯姪鏂囨。" data-discover="true" href="/help"><span class="inline-flex items-center truncate gap-1 text-base font-500 hover:text-primary text-primary max-os:text-lg max-os:hover:text-primary max-os:text-primary max-os:gap-2 max-os:font-600"><span class="truncate">甯姪鏂囨。</span></span></a><a class="relative inline-flex max-w-full flex-shrink-0 items-center os:max-w-[192px] px-3 py-[5px] max-os:py-2 g-header-top-navbar-left-item" title="甯歌闂" data-discover="true" href="/5835927m0?nav=01JHMQ9DFDQ3G5JYCPTZE4EH3J"><span class="inline-flex items-center truncate gap-1 text-base font-500 hover:text-primary text-secondary max-os:text-lg max-os:hover:text-primary max-os:text-color-sub max-os:gap-2 max-os:font-600"><span class="truncate">甯歌闂</span></span></a><a class="relative inline-flex max-w-full flex-shrink-0 items-center os:max-w-[192px] px-3 py-[5px] max-os:py-2 g-header-top-navbar-left-item" title="Apifox 瀹樼綉" href="https://apifox.com" target="_blank"><span class="inline-flex items-center truncate gap-1 text-base font-500 hover:text-primary text-secondary max-os:text-lg max-os:hover:text-primary max-os:text-color-sub max-os:gap-2 max-os:font-600"><span class="truncate">Apifox 瀹樼綉</span><span role="img" class="appicon app_icon text-disabled" style="font-size:12px"><svg viewBox="0 0 1024 1024" width="1em" height="1em" fill="currentColor" aria-hidden="true" focusable="false" role="img"><path d="M384 256a42.666667 42.666667 0 0 1 42.666667-42.666667h341.333333a42.666667 42.666667 0 0 1 42.666667 42.666667v341.333333a42.666667 42.666667 0 1 1-85.333334 0V358.997333L286.165333 798.165333a42.666667 42.666667 0 0 1-60.330666-60.330666L665.002667 298.666667H426.666667a42.666667 42.666667 0 0 1-42.666667-42.666667z"></path></svg></span></span></a><a class="relative inline-flex max-w-full flex-shrink-0 items-center os:max-w-[192px] px-3 py-[5px] max-os:py-2 g-header-top-navbar-left-item" title="绉佹湁鍖栭儴缃? href="https://apifox.com/siyouhua" target="_blank"><span class="inline-flex items-center truncate gap-1 text-base font-500 hover:text-primary text-secondary max-os:text-lg max-os:hover:text-primary max-os:text-color-sub max-os:gap-2 max-os:font-600"><span class="truncate">绉佹湁鍖栭儴缃?/span><span role="img" class="appicon app_icon text-disabled" style="font-size:12px"><svg viewBox="0 0 1024 1024" width="1em" height="1em" fill="currentColor" aria-hidden="true" focusable="false" role="img"><path d="M384 256a42.666667 42.666667 0 0 1 42.666667-42.666667h341.333333a42.666667 42.666667 0 0 1 42.666667 42.666667v341.333333a42.666667 42.666667 0 1 1-85.333334 0V358.997333L286.165333 798.165333a42.666667 42.666667 0 0 1-60.330666-60.330666L665.002667 298.666667H426.666667a42.666667 42.666667 0 0 1-42.666667-42.666667z"></path></svg></span></span></a><div class="ui-dropdown-trigger flex-shrink-0 cursor-pointer max-os:w-full" title="寮€鍙戣€呬腑蹇?><span to="" class="relative inline-flex max-w-full flex-shrink-0 items-center os:max-w-[192px] px-3 py-[5px] max-os:py-2 g-header-top-navbar-left-item" title="寮€鍙戣€呬腑蹇?><span class="inline-flex items-center truncate gap-1 text-base font-500 hover:text-primary text-secondary max-os:text-lg max-os:hover:text-primary max-os:text-color-sub max-os:gap-2 max-os:font-600"><span class="truncate">寮€鍙戣€呬腑蹇?/span><span role="img" class="appicon app_icon hidden os:inline-block" style="font-size:16px"><svg viewBox="0 0 1024 1024" width="1em" height="1em" fill="currentColor" aria-hidden="true" focusable="false" role="img"><path d="M225.834667 353.834667a42.666667 42.666667 0 0 1 60.330666 0L512 579.669333l225.834667-225.834666a42.666667 42.666667 0 1 1 60.330666 60.330666l-256 256a42.666667 42.666667 0 0 1-60.330666 0l-256-256a42.666667 42.666667 0 0 1 0-60.330666z"></path></svg></span><span role="img" class="appicon app_icon inline-block os:hidden" style="font-size:16px"><svg viewBox="0 0 1024 1024" width="1em" height="1em" fill="currentColor" aria-hidden="true" focusable="false" role="img"><path d="M225.834667 353.834667a42.666667 42.666667 0 0 1 60.330666 0L512 579.669333l225.834667-225.834666a42.666667 42.666667 0 1 1 60.330666 60.330666l-256 256a42.666667 42.666667 0 0 1-60.330666 0l-256-256a42.666667 42.666667 0 0 1 0-60.330666z"></path></svg></span></span></span><ul class="hidden pl-6 max-os:block"><li><a class="relative inline-flex max-w-full flex-shrink-0 items-center os:max-w-[192px] px-3 py-[5px] max-os:py-2 g-header-top-navbar-left-item" title="寮€鏀?API" href="https://apifox-openapi.apifox.cn/" target="_blank"><span class="inline-flex items-center truncate gap-1 text-base font-500 hover:text-primary text-secondary max-os:text-lg max-os:hover:text-primary max-os:text-color-sub max-os:gap-2 max-os:font-500"><span class="truncate">寮€鏀?API</span><span role="img" class="appicon app_icon text-disabled" style="font-size:12px"><svg viewBox="0 0 1024 1024" width="1em" height="1em" fill="currentColor" aria-hidden="true" focusable="false" role="img"><path d="M384 256a42.666667 42.666667 0 0 1 42.666667-42.666667h341.333333a42.666667 42.666667 0 0 1 42.666667 42.666667v341.333333a42.666667 42.666667 0 1 1-85.333334 0V358.997333L286.165333 798.165333a42.666667 42.666667 0 0 1-60.330666-60.330666L665.002667 298.666667H426.666667a42.666667 42.666667 0 0 1-42.666667-42.666667z"></path></svg></span></span></a></li><li><a class="relative inline-flex max-w-full flex-shrink-0 items-center os:max-w-[192px] px-3 py-[5px] max-os:py-2 g-header-top-navbar-left-item" title="鏇存柊鏃ュ織" href="https://docs.apifox.com/changelog" target="_self"><span class="inline-flex items-center truncate gap-1 text-base font-500 hover:text-primary text-secondary max-os:text-lg max-os:hover:text-primary max-os:text-color-sub max-os:gap-2 max-os:font-500"><span class="truncate">鏇存柊鏃ュ織</span><span role="img" class="appicon app_icon text-disabled" style="font-size:12px"><svg viewBox="0 0 1024 1024" width="1em" height="1em" fill="currentColor" aria-hidden="true" focusable="false" role="img"><path d="M384 256a42.666667 42.666667 0 0 1 42.666667-42.666667h341.333333a42.666667 42.666667 0 0 1 42.666667 42.666667v341.333333a42.666667 42.666667 0 1 1-85.333334 0V358.997333L286.165333 798.165333a42.666667 42.666667 0 0 1-60.330666-60.330666L665.002667 298.666667H426.666667a42.666667 42.666667 0 0 1-42.666667-42.666667z"></path></svg></span></span></a></li><li><a class="relative inline-flex max-w-full flex-shrink-0 items-center os:max-w-[192px] px-3 py-[5px] max-os:py-2 g-header-top-navbar-left-item" title="Road Map" href="https://apifox666.feishu.cn/base/bascnA8ZHro9a3k73zpI93f5DKf?table=tblRLUMAyHhyQ4XQ&amp;view=vewUvyaZ4L" target="_blank"><span class="inline-flex items-center truncate gap-1 text-base font-500 hover:text-primary text-secondary max-os:text-lg max-os:hover:text-primary max-os:text-color-sub max-os:gap-2 max-os:font-500"><span class="truncate">Road Map</span><span role="img" class="appicon app_icon text-disabled" style="font-size:12px"><svg viewBox="0 0 1024 1024" width="1em" height="1em" fill="currentColor" aria-hidden="true" focusable="false" role="img"><path d="M384 256a42.666667 42.666667 0 0 1 42.666667-42.666667h341.333333a42.666667 42.666667 0 0 1 42.666667 42.666667v341.333333a42.666667 42.666667 0 1 1-85.333334 0V358.997333L286.165333 798.165333a42.666667 42.666667 0 0 1-60.330666-60.330666L665.002667 298.666667H426.666667a42.666667 42.666667 0 0 1-42.666667-42.666667z"></path></svg></span></span></a></li><li><a class="relative inline-flex max-w-full flex-shrink-0 items-center os:max-w-[192px] px-3 py-[5px] max-os:py-2 g-header-top-navbar-left-item" title="Apifox Markdown" href="https://markdown.apifox.cn/" target="_blank"><span class="inline-flex items-center truncate gap-1 text-base font-500 hover:text-primary text-secondary max-os:text-lg max-os:hover:text-primary max-os:text-color-sub max-os:gap-2 max-os:font-500"><span class="truncate">Apifox Markdown</span><span role="img" class="appicon app_icon text-disabled" style="font-size:12px"><svg viewBox="0 0 1024 1024" width="1em" height="1em" fill="currentColor" aria-hidden="true" focusable="false" role="img"><path d="M384 256a42.666667 42.666667 0 0 1 42.666667-42.666667h341.333333a42.666667 42.666667 0 0 1 42.666667 42.666667v341.333333a42.666667 42.666667 0 1 1-85.333334 0V358.997333L286.165333 798.165333a42.666667 42.666667 0 0 1-60.330666-60.330666L665.002667 298.666667H426.666667a42.666667 42.666667 0 0 1-42.666667-42.666667z"></path></svg></span></span></a></li></ul></div><div class="ui-dropdown-trigger flex-shrink-0 cursor-pointer max-os:w-full" title="涓嬭浇"><span to="" class="relative inline-flex max-w-full flex-shrink-0 items-center os:max-w-[192px] px-3 py-[5px] max-os:py-2 g-header-top-navbar-left-item" title="涓嬭浇"><span class="inline-flex items-center truncate gap-1 text-base font-500 hover:text-primary text-secondary max-os:text-lg max-os:hover:text-primary max-os:text-color-sub max-os:gap-2 max-os:font-600"><span class="truncate">涓嬭浇</span><span role="img" class="appicon app_icon hidden os:inline-block" style="font-size:16px"><svg viewBox="0 0 1024 1024" width="1em" height="1em" fill="currentColor" aria-hidden="true" focusable="false" role="img"><path d="M225.834667 353.834667a42.666667 42.666667 0 0 1 60.330666 0L512 579.669333l225.834667-225.834666a42.666667 42.666667 0 1 1 60.330666 60.330666l-256 256a42.666667 42.666667 0 0 1-60.330666 0l-256-256a42.666667 42.666667 0 0 1 0-60.330666z"></path></svg></span><span role="img" class="appicon app_icon inline-block os:hidden" style="font-size:16px"><svg viewBox="0 0 1024 1024" width="1em" height="1em" fill="currentColor" aria-hidden="true" focusable="false" role="img"><path d="M225.834667 353.834667a42.666667 42.666667 0 0 1 60.330666 0L512 579.669333l225.834667-225.834666a42.666667 42.666667 0 1 1 60.330666 60.330666l-256 256a42.666667 42.666667 0 0 1-60.330666 0l-256-256a42.666667 42.666667 0 0 1 0-60.330666z"></path></svg></span></span></span><ul class="hidden pl-6 max-os:block"><li><a class="relative inline-flex max-w-full flex-shrink-0 items-center os:max-w-[192px] px-3 py-[5px] max-os:py-2 g-header-top-navbar-left-item" title="涓嬭浇 Apifox" href="https://docs.apifox.com/download" target="_self"><span class="inline-flex items-center truncate gap-1 text-base font-500 hover:text-primary text-secondary max-os:text-lg max-os:hover:text-primary max-os:text-color-sub max-os:gap-2 max-os:font-500"><span class="truncate">涓嬭浇 Apifox</span><span role="img" class="appicon app_icon text-disabled" style="font-size:12px"><svg viewBox="0 0 1024 1024" width="1em" height="1em" fill="currentColor" aria-hidden="true" focusable="false" role="img"><path d="M384 256a42.666667 42.666667 0 0 1 42.666667-42.666667h341.333333a42.666667 42.666667 0 0 1 42.666667 42.666667v341.333333a42.666667 42.666667 0 1 1-85.333334 0V358.997333L286.165333 798.165333a42.666667 42.666667 0 0 1-60.330666-60.330666L665.002667 298.666667H426.666667a42.666667 42.666667 0 0 1-42.666667-42.666667z"></path></svg></span></span></a></li><li><a class="relative inline-flex max-w-full flex-shrink-0 items-center os:max-w-[192px] px-3 py-[5px] max-os:py-2 g-header-top-navbar-left-item" title="涓嬭浇 IDEA 鎻掍欢" href="https://docs.apifox.com/5743620m0" target="_self"><span class="inline-flex items-center truncate gap-1 text-base font-500 hover:text-primary text-secondary max-os:text-lg max-os:hover:text-primary max-os:text-color-sub max-os:gap-2 max-os:font-500"><span class="truncate">涓嬭浇 IDEA 鎻掍欢</span><span role="img" class="appicon app_icon text-disabled" style="font-size:12px"><svg viewBox="0 0 1024 1024" width="1em" height="1em" fill="currentColor" aria-hidden="true" focusable="false" role="img"><path d="M384 256a42.666667 42.666667 0 0 1 42.666667-42.666667h341.333333a42.666667 42.666667 0 0 1 42.666667 42.666667v341.333333a42.666667 42.666667 0 1 1-85.333334 0V358.997333L286.165333 798.165333a42.666667 42.666667 0 0 1-60.330666-60.330666L665.002667 298.666667H426.666667a42.666667 42.666667 0 0 1-42.666667-42.666667z"></path></svg></span></span></a></li><li><a class="relative inline-flex max-w-full flex-shrink-0 items-center os:max-w-[192px] px-3 py-[5px] max-os:py-2 g-header-top-navbar-left-item" title="涓嬭浇娴忚鍣ㄦ墿灞? href="https://docs.apifox.com/5807583m0" target="_self"><span class="inline-flex items-center truncate gap-1 text-base font-500 hover:text-primary text-secondary max-os:text-lg max-os:hover:text-primary max-os:text-color-sub max-os:gap-2 max-os:font-500"><span class="truncate">涓嬭浇娴忚鍣ㄦ墿灞?/span><span role="img" class="appicon app_icon text-disabled" style="font-size:12px"><svg viewBox="0 0 1024 1024" width="1em" height="1em" fill="currentColor" aria-hidden="true" focusable="false" role="img"><path d="M384 256a42.666667 42.666667 0 0 1 42.666667-42.666667h341.333333a42.666667 42.666667 0 0 1 42.666667 42.666667v341.333333a42.666667 42.666667 0 1 1-85.333334 0V358.997333L286.165333 798.165333a42.666667 42.666667 0 0 1-60.330666-60.330666L665.002667 298.666667H426.666667a42.666667 42.666667 0 0 1-42.666667-42.666667z"></path></svg></span></span></a></li><li><a class="relative inline-flex max-w-full flex-shrink-0 items-center os:max-w-[192px] px-3 py-[5px] max-os:py-2 g-header-top-navbar-left-item" title="Apifox Web 鐗? href="https://app.apifox.com/" target="_blank"><span class="inline-flex items-center truncate gap-1 text-base font-500 hover:text-primary text-secondary max-os:text-lg max-os:hover:text-primary max-os:text-color-sub max-os:gap-2 max-os:font-500"><span class="truncate">Apifox Web 鐗?/span><span role="img" class="appicon app_icon text-disabled" style="font-size:12px"><svg viewBox="0 0 1024 1024" width="1em" height="1em" fill="currentColor" aria-hidden="true" focusable="false" role="img"><path d="M384 256a42.666667 42.666667 0 0 1 42.666667-42.666667h341.333333a42.666667 42.666667 0 0 1 42.666667 42.666667v341.333333a42.666667 42.666667 0 1 1-85.333334 0V358.997333L286.165333 798.165333a42.666667 42.666667 0 0 1-60.330666-60.330666L665.002667 298.666667H426.666667a42.666667 42.666667 0 0 1-42.666667-42.666667z"></path></svg></span></span></a></li></ul></div></div><div class="flex flex-nowrap items-center gap-2 max-os:hidden max-os:flex-col max-os:items-start max-os:gap-[1px] os:justify-end"><a class="relative inline-flex max-w-full flex-shrink-0 items-center os:max-w-[192px] px-3 py-[5px] max-os:py-2 g-header-top-navbar-left-item g-header-top-navbar-left-item-active" title="甯姪鏂囨。" data-discover="true" href="/help"><span class="inline-flex items-center truncate gap-1 text-base font-500 hover:text-primary text-primary max-os:text-lg max-os:hover:text-primary max-os:text-primary max-os:gap-2 max-os:font-600"><span class="truncate">甯姪鏂囨。</span></span></a><a class="relative inline-flex max-w-full flex-shrink-0 items-center os:max-w-[192px] px-3 py-[5px] max-os:py-2 g-header-top-navbar-left-item" title="甯歌闂" data-discover="true" href="/5835927m0?nav=01JHMQ9DFDQ3G5JYCPTZE4EH3J"><span class="inline-flex items-center truncate gap-1 text-base font-500 hover:text-primary text-secondary max-os:text-lg max-os:hover:text-primary max-os:text-color-sub max-os:gap-2 max-os:font-600"><span class="truncate">甯歌闂</span></span></a><a class="relative inline-flex max-w-full flex-shrink-0 items-center os:max-w-[192px] px-3 py-[5px] max-os:py-2 g-header-top-navbar-left-item" title="Apifox 瀹樼綉" href="https://apifox.com" target="_blank"><span class="inline-flex items-center truncate gap-1 text-base font-500 hover:text-primary text-secondary max-os:text-lg max-os:hover:text-primary max-os:text-color-sub max-os:gap-2 max-os:font-600"><span class="truncate">Apifox 瀹樼綉</span><span role="img" class="appicon app_icon text-disabled" style="font-size:12px"><svg viewBox="0 0 1024 1024" width="1em" height="1em" fill="currentColor" aria-hidden="true" focusable="false" role="img"><path d="M384 256a42.666667 42.666667 0 0 1 42.666667-42.666667h341.333333a42.666667 42.666667 0 0 1 42.666667 42.666667v341.333333a42.666667 42.666667 0 1 1-85.333334 0V358.997333L286.165333 798.165333a42.666667 42.666667 0 0 1-60.330666-60.330666L665.002667 298.666667H426.666667a42.666667 42.666667 0 0 1-42.666667-42.666667z"></path></svg></span></span></a><a class="relative inline-flex max-w-full flex-shrink-0 items-center os:max-w-[192px] px-3 py-[5px] max-os:py-2 g-header-top-navbar-left-item" title="绉佹湁鍖栭儴缃? href="https://apifox.com/siyouhua" target="_blank"><span class="inline-flex items-center truncate gap-1 text-base font-500 hover:text-primary text-secondary max-os:text-lg max-os:hover:text-primary max-os:text-color-sub max-os:gap-2 max-os:font-600"><span class="truncate">绉佹湁鍖栭儴缃?/span><span role="img" class="appicon app_icon text-disabled" style="font-size:12px"><svg viewBox="0 0 1024 1024" width="1em" height="1em" fill="currentColor" aria-hidden="true" focusable="false" role="img"><path d="M384 256a42.666667 42.666667 0 0 1 42.666667-42.666667h341.333333a42.666667 42.666667 0 0 1 42.666667 42.666667v341.333333a42.666667 42.666667 0 1 1-85.333334 0V358.997333L286.165333 798.165333a42.666667 42.666667 0 0 1-60.330666-60.330666L665.002667 298.666667H426.666667a42.666667 42.666667 0 0 1-42.666667-42.666667z"></path></svg></span></span></a><div class="ui-dropdown-trigger flex-shrink-0 cursor-pointer max-os:w-full" title="寮€鍙戣€呬腑蹇?><span to="" class="relative inline-flex max-w-full flex-shrink-0 items-center os:max-w-[192px] px-3 py-[5px] max-os:py-2 g-header-top-navbar-left-item" title="寮€鍙戣€呬腑蹇?><span class="inline-flex items-center truncate gap-1 text-base font-500 hover:text-primary text-secondary max-os:text-lg max-os:hover:text-primary max-os:text-color-sub max-os:gap-2 max-os:font-600"><span class="truncate">寮€鍙戣€呬腑蹇?/span><span role="img" class="appicon app_icon hidden os:inline-block" style="font-size:16px"><svg viewBox="0 0 1024 1024" width="1em" height="1em" fill="currentColor" aria-hidden="true" focusable="false" role="img"><path d="M225.834667 353.834667a42.666667 42.666667 0 0 1 60.330666 0L512 579.669333l225.834667-225.834666a42.666667 42.666667 0 1 1 60.330666 60.330666l-256 256a42.666667 42.666667 0 0 1-60.330666 0l-256-256a42.666667 42.666667 0 0 1 0-60.330666z"></path></svg></span><span role="img" class="appicon app_icon inline-block os:hidden" style="font-size:16px"><svg viewBox="0 0 1024 1024" width="1em" height="1em" fill="currentColor" aria-hidden="true" focusable="false" role="img"><path d="M225.834667 353.834667a42.666667 42.666667 0 0 1 60.330666 0L512 579.669333l225.834667-225.834666a42.666667 42.666667 0 1 1 60.330666 60.330666l-256 256a42.666667 42.666667 0 0 1-60.330666 0l-256-256a42.666667 42.666667 0 0 1 0-60.330666z"></path></svg></span></span></span><ul class="hidden pl-6 max-os:block"><li><a class="relative inline-flex max-w-full flex-shrink-0 items-center os:max-w-[192px] px-3 py-[5px] max-os:py-2 g-header-top-navbar-left-item" title="寮€鏀?API" href="https://apifox-openapi.apifox.cn/" target="_blank"><span class="inline-flex items-center truncate gap-1 text-base font-500 hover:text-primary text-secondary max-os:text-lg max-os:hover:text-primary max-os:text-color-sub max-os:gap-2 max-os:font-500"><span class="truncate">寮€鏀?API</span><span role="img" class="appicon app_icon text-disabled" style="font-size:12px"><svg viewBox="0 0 1024 1024" width="1em" height="1em" fill="currentColor" aria-hidden="true" focusable="false" role="img"><path d="M384 256a42.666667 42.666667 0 0 1 42.666667-42.666667h341.333333a42.666667 42.666667 0 0 1 42.666667 42.666667v341.333333a42.666667 42.666667 0 1 1-85.333334 0V358.997333L286.165333 798.165333a42.666667 42.666667 0 0 1-60.330666-60.330666L665.002667 298.666667H426.666667a42.666667 42.666667 0 0 1-42.666667-42.666667z"></path></svg></span></span></a></li><li><a class="relative inline-flex max-w-full flex-shrink-0 items-center os:max-w-[192px] px-3 py-[5px] max-os:py-2 g-header-top-navbar-left-item" title="鏇存柊鏃ュ織" href="https://docs.apifox.com/changelog" target="_self"><span class="inline-flex items-center truncate gap-1 text-base font-500 hover:text-primary text-secondary max-os:text-lg max-os:hover:text-primary max-os:text-color-sub max-os:gap-2 max-os:font-500"><span class="truncate">鏇存柊鏃ュ織</span><span role="img" class="appicon app_icon text-disabled" style="font-size:12px"><svg viewBox="0 0 1024 1024" width="1em" height="1em" fill="currentColor" aria-hidden="true" focusable="false" role="img"><path d="M384 256a42.666667 42.666667 0 0 1 42.666667-42.666667h341.333333a42.666667 42.666667 0 0 1 42.666667 42.666667v341.333333a42.666667 42.666667 0 1 1-85.333334 0V358.997333L286.165333 798.165333a42.666667 42.666667 0 0 1-60.330666-60.330666L665.002667 298.666667H426.666667a42.666667 42.666667 0 0 1-42.666667-42.666667z"></path></svg></span></span></a></li><li><a class="relative inline-flex max-w-full flex-shrink-0 items-center os:max-w-[192px] px-3 py-[5px] max-os:py-2 g-header-top-navbar-left-item" title="Road Map" href="https://apifox666.feishu.cn/base/bascnA8ZHro9a3k73zpI93f5DKf?table=tblRLUMAyHhyQ4XQ&amp;view=vewUvyaZ4L" target="_blank"><span class="inline-flex items-center truncate gap-1 text-base font-500 hover:text-primary text-secondary max-os:text-lg max-os:hover:text-primary max-os:text-color-sub max-os:gap-2 max-os:font-500"><span class="truncate">Road Map</span><span role="img" class="appicon app_icon text-disabled" style="font-size:12px"><svg viewBox="0 0 1024 1024" width="1em" height="1em" fill="currentColor" aria-hidden="true" focusable="false" role="img"><path d="M384 256a42.666667 42.666667 0 0 1 42.666667-42.666667h341.333333a42.666667 42.666667 0 0 1 42.666667 42.666667v341.333333a42.666667 42.666667 0 1 1-85.333334 0V358.997333L286.165333 798.165333a42.666667 42.666667 0 0 1-60.330666-60.330666L665.002667 298.666667H426.666667a42.666667 42.666667 0 0 1-42.666667-42.666667z"></path></svg></span></span></a></li><li><a class="relative inline-flex max-w-full flex-shrink-0 items-center os:max-w-[192px] px-3 py-[5px] max-os:py-2 g-header-top-navbar-left-item" title="Apifox Markdown" href="https://markdown.apifox.cn/" target="_blank"><span class="inline-flex items-center truncate gap-1 text-base font-500 hover:text-primary text-secondary max-os:text-lg max-os:hover:text-primary max-os:text-color-sub max-os:gap-2 max-os:font-500"><span class="truncate">Apifox Markdown</span><span role="img" class="appicon app_icon text-disabled" style="font-size:12px"><svg viewBox="0 0 1024 1024" width="1em" height="1em" fill="currentColor" aria-hidden="true" focusable="false" role="img"><path d="M384 256a42.666667 42.666667 0 0 1 42.666667-42.666667h341.333333a42.666667 42.666667 0 0 1 42.666667 42.666667v341.333333a42.666667 42.666667 0 1 1-85.333334 0V358.997333L286.165333 798.165333a42.666667 42.666667 0 0 1-60.330666-60.330666L665.002667 298.666667H426.666667a42.666667 42.666667 0 0 1-42.666667-42.666667z"></path></svg></span></span></a></li></ul></div><div class="ui-dropdown-trigger flex-shrink-0 cursor-pointer max-os:w-full" title="涓嬭浇"><span to="" class="relative inline-flex max-w-full flex-shrink-0 items-center os:max-w-[192px] px-3 py-[5px] max-os:py-2 g-header-top-navbar-left-item" title="涓嬭浇"><span class="inline-flex items-center truncate gap-1 text-base font-500 hover:text-primary text-secondary max-os:text-lg max-os:hover:text-primary max-os:text-color-sub max-os:gap-2 max-os:font-600"><span class="truncate">涓嬭浇</span><span role="img" class="appicon app_icon hidden os:inline-block" style="font-size:16px"><svg viewBox="0 0 1024 1024" width="1em" height="1em" fill="currentColor" aria-hidden="true" focusable="false" role="img"><path d="M225.834667 353.834667a42.666667 42.666667 0 0 1 60.330666 0L512 579.669333l225.834667-225.834666a42.666667 42.666667 0 1 1 60.330666 60.330666l-256 256a42.666667 42.666667 0 0 1-60.330666 0l-256-256a42.666667 42.666667 0 0 1 0-60.330666z"></path></svg></span><span role="img" class="appicon app_icon inline-block os:hidden" style="font-size:16px"><svg viewBox="0 0 1024 1024" width="1em" height="1em" fill="currentColor" aria-hidden="true" focusable="false" role="img"><path d="M225.834667 353.834667a42.666667 42.666667 0 0 1 60.330666 0L512 579.669333l225.834667-225.834666a42.666667 42.666667 0 1 1 60.330666 60.330666l-256 256a42.666667 42.666667 0 0 1-60.330666 0l-256-256a42.666667 42.666667 0 0 1 0-60.330666z"></path></svg></span></span></span><ul class="hidden pl-6 max-os:block"><li><a class="relative inline-flex max-w-full flex-shrink-0 items-center os:max-w-[192px] px-3 py-[5px] max-os:py-2 g-header-top-navbar-left-item" title="涓嬭浇 Apifox" href="https://docs.apifox.com/download" target="_self"><span class="inline-flex items-center truncate gap-1 text-base font-500 hover:text-primary text-secondary max-os:text-lg max-os:hover:text-primary max-os:text-color-sub max-os:gap-2 max-os:font-500"><span class="truncate">涓嬭浇 Apifox</span><span role="img" class="appicon app_icon text-disabled" style="font-size:12px"><svg viewBox="0 0 1024 1024" width="1em" height="1em" fill="currentColor" aria-hidden="true" focusable="false" role="img"><path d="M384 256a42.666667 42.666667 0 0 1 42.666667-42.666667h341.333333a42.666667 42.666667 0 0 1 42.666667 42.666667v341.333333a42.666667 42.666667 0 1 1-85.333334 0V358.997333L286.165333 798.165333a42.666667 42.666667 0 0 1-60.330666-60.330666L665.002667 298.666667H426.666667a42.666667 42.666667 0 0 1-42.666667-42.666667z"></path></svg></span></span></a></li><li><a class="relative inline-flex max-w-full flex-shrink-0 items-center os:max-w-[192px] px-3 py-[5px] max-os:py-2 g-header-top-navbar-left-item" title="涓嬭浇 IDEA 鎻掍欢" href="https://docs.apifox.com/5743620m0" target="_self"><span class="inline-flex items-center truncate gap-1 text-base font-500 hover:text-primary text-secondary max-os:text-lg max-os:hover:text-primary max-os:text-color-sub max-os:gap-2 max-os:font-500"><span class="truncate">涓嬭浇 IDEA 鎻掍欢</span><span role="img" class="appicon app_icon text-disabled" style="font-size:12px"><svg viewBox="0 0 1024 1024" width="1em" height="1em" fill="currentColor" aria-hidden="true" focusable="false" role="img"><path d="M384 256a42.666667 42.666667 0 0 1 42.666667-42.666667h341.333333a42.666667 42.666667 0 0 1 42.666667 42.666667v341.333333a42.666667 42.666667 0 1 1-85.333334 0V358.997333L286.165333 798.165333a42.666667 42.666667 0 0 1-60.330666-60.330666L665.002667 298.666667H426.666667a42.666667 42.666667 0 0 1-42.666667-42.666667z"></path></svg></span></span></a></li><li><a class="relative inline-flex max-w-full flex-shrink-0 items-center os:max-w-[192px] px-3 py-[5px] max-os:py-2 g-header-top-navbar-left-item" title="涓嬭浇娴忚鍣ㄦ墿灞? href="https://docs.apifox.com/5807583m0" target="_self"><span class="inline-flex items-center truncate gap-1 text-base font-500 hover:text-primary text-secondary max-os:text-lg max-os:hover:text-primary max-os:text-color-sub max-os:gap-2 max-os:font-500"><span class="truncate">涓嬭浇娴忚鍣ㄦ墿灞?/span><span role="img" class="appicon app_icon text-disabled" style="font-size:12px"><svg viewBox="0 0 1024 1024" width="1em" height="1em" fill="currentColor" aria-hidden="true" focusable="false" role="img"><path d="M384 256a42.666667 42.666667 0 0 1 42.666667-42.666667h341.333333a42.666667 42.666667 0 0 1 42.666667 42.666667v341.333333a42.666667 42.666667 0 1 1-85.333334 0V358.997333L286.165333 798.165333a42.666667 42.666667 0 0 1-60.330666-60.330666L665.002667 298.666667H426.666667a42.666667 42.666667 0 0 1-42.666667-42.666667z"></path></svg></span></span></a></li><li><a class="relative inline-flex max-w-full flex-shrink-0 items-center os:max-w-[192px] px-3 py-[5px] max-os:py-2 g-header-top-navbar-left-item" title="Apifox Web 鐗? href="https://app.apifox.com/" target="_blank"><span class="inline-flex items-center truncate gap-1 text-base font-500 hover:text-primary text-secondary max-os:text-lg max-os:hover:text-primary max-os:text-color-sub max-os:gap-2 max-os:font-500"><span class="truncate">Apifox Web 鐗?/span><span role="img" class="appicon app_icon text-disabled" style="font-size:12px"><svg viewBox="0 0 1024 1024" width="1em" height="1em" fill="currentColor" aria-hidden="true" focusable="false" role="img"><path d="M384 256a42.666667 42.666667 0 0 1 42.666667-42.666667h341.333333a42.666667 42.666667 0 0 1 42.666667 42.666667v341.333333a42.666667 42.666667 0 1 1-85.333334 0V358.997333L286.165333 798.165333a42.666667 42.666667 0 0 1-60.330666-60.330666L665.002667 298.666667H426.666667a42.666667 42.666667 0 0 1-42.666667-42.666667z"></path></svg></span></span></a></li></ul></div></div></div><div class="g-header-top-navbar-right flex flex-none flex-shrink-0 items-center gap-1 os:ml-8"><div class="inline-flex h-8 w-8 cursor-pointer items-center justify-center rounded-lg _container_199t5_1"><button class="theme-toggle _expand-button_199t5_51 flex items-center" aria-label="Toggle theme"><svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" fill="currentColor" aria-hidden="true" class="theme-toggle-svg" viewBox="0 0 24 24"><clipPath id=":Re99qn5:a"><path d="M0-11h19A1 1 0 0 0 36 2v22H0Z"></path></clipPath><g clip-path="url(#:Re99qn5:a)"><circle cx="12" cy="12" r="6.4"></circle><path d="M13.7 2.7c0 1-.7 1.8-1.7 1.8s-1.8-.8-1.8-1.8S11 1 12 1s1.7.7 1.7 1.7zm-3.5 19.5c0-1 .8-1.7 1.8-1.7s1.6.6 1.6 1.6-.7 1.8-1.7 1.8-1.8-.8-1.8-1.8zm11.5-8c-1 0-1.7-.7-1.7-1.7s.7-1.8 1.7-1.8 1.8.8 1.8 1.8-.9 1.6-1.9 1.6zM2.2 10.7c1 0 1.8.8 1.8 1.8s-.8 1.7-1.8 1.7-1.7-.7-1.7-1.7.7-1.8 1.7-1.8zm4.4-5.3c0 1-.8 1.7-1.7 1.7-1 0-1.8-.7-1.8-1.7s.8-1.8 1.8-1.8 1.7.8 1.7 1.8zm12.5 16c-1 0-1.8-.8-1.8-1.8s.8-1.7 1.8-1.7 1.7.7 1.7 1.7-.8 1.8-1.7 1.8zm1.8-16c0 1-.8 1.7-1.8 1.7s-1.8-.8-1.8-1.7c0-1 .8-1.8 1.8-1.8s1.8.8 1.8 1.8zm-16 12.4c1 0 1.7.8 1.7 1.8s-.7 1.7-1.7 1.7-1.8-.8-1.8-1.7c0-1 .8-1.8 1.8-1.8z"></path></g></svg></button></div></div></div><div class="flex justify-end gap-1 os:hidden"><button aria-label="open navigation drawer" type="button" class="ui-btn ui-btn-text ui-btn-flex ui-btn-icon-only"><span role="img" class="appicon app_icon"><svg viewBox="0 0 1024 1024" width="1em" height="1em" fill="currentColor" aria-hidden="true" focusable="false" role="img"><path d="M426.666667 213.333333a85.333333 85.333333 0 1 1 170.666666 0 85.333333 85.333333 0 0 1-170.666666 0zM426.666667 512a85.333333 85.333333 0 1 1 170.666666 0 85.333333 85.333333 0 0 1-170.666666 0zM426.666667 810.666667a85.333333 85.333333 0 1 1 170.666666 0 85.333333 85.333333 0 0 1-170.666666 0z"></path></svg></span></button></div></div></div></div><div class="sticky top-[57px] z-20 flex w-full os:hidden border-b border-b-[#0000000a] backdrop-blur-lg dark:border-b-[#ffffff0f] bg-[#ffffff7a] dark:bg-transparent"><div class="flex w-full items-center gap-2 px-3 py-2"><button aria-label="open sidebar drawer" type="button" class="ui-btn ui-btn-text ui-btn-flex ui-btn-icon-only"><span role="img" class="appicon app_icon"><svg viewBox="0 0 1024 1024" width="1em" height="1em" fill="currentColor" aria-hidden="true" focusable="false" role="img"><path d="M85.333333 256a42.666667 42.666667 0 0 1 42.666667-42.666667h768a42.666667 42.666667 0 1 1 0 85.333334H128a42.666667 42.666667 0 0 1-42.666667-42.666667zM85.333333 512a42.666667 42.666667 0 0 1 42.666667-42.666667h768a42.666667 42.666667 0 1 1 0 85.333334H128a42.666667 42.666667 0 0 1-42.666667-42.666667zM85.333333 768a42.666667 42.666667 0 0 1 42.666667-42.666667h768a42.666667 42.666667 0 1 1 0 85.333334H128a42.666667 42.666667 0 0 1-42.666667-42.666667z"></path></svg></span></button><div class="flex items-center justify-between w-0 flex-1"><nav class="ui-breadcrumb w-0 flex-1 text-[14px] leading-base _docs-breadcrumb_1hvzp_1"><ol><li><span class="ui-breadcrumb-link">甯姪鏂囨。</span><span class="ui-breadcrumb-separator"><span role="img" class="appicon app_icon align-middle" style="font-size:14px"><svg viewBox="0 0 1024 1024" width="1em" height="1em" fill="currentColor" aria-hidden="true" focusable="false" role="img"><path d="M396.501333 311.168a42.666667 42.666667 0 0 1 60.330667 0l170.666667 170.666667a42.666667 42.666667 0 0 1 0 60.330666l-170.666667 170.666667a42.666667 42.666667 0 0 1-60.330667-60.330667L537.002667 512 396.501333 371.498667a42.666667 42.666667 0 0 1 0-60.330667z"></path></svg></span></span></li></ol></nav><div class="flex items-center gap-2"><div class="ui-space-compact ui-dropdown-button flex items-center"><button type="button" class="ui-btn ui-btn-default ui-btn-sm ui-btn-compact-item ui-btn-compact-first-item"><span><span role="img" class="appicon app_icon" style="font-size:12px"><svg viewBox="0 0 1024 1024" width="1em" height="1em" fill="currentColor" aria-hidden="true" focusable="false" role="img"><path d="M328.533333 512c0-25.6 17.066667-42.666667 42.666667-42.666667s42.666667 17.066667 42.666667 42.666667-17.066667 42.666667-42.666667 42.666667-42.666667-17.066667-42.666667-42.666667zM469.333333 512c0-25.6 17.066667-42.666667 42.666667-42.666667h140.8c25.6 0 42.666667 17.066667 42.666667 42.666667s-17.066667 42.666667-42.666667 42.666667H512c-25.6 0-42.666667-17.066667-42.666667-42.666667zM328.533333 699.733333c0-25.6 17.066667-42.666667 42.666667-42.666666s42.666667 17.066667 42.666667 42.666666-17.066667 42.666667-42.666667 42.666667-42.666667-17.066667-42.666667-42.666667zM469.333333 699.733333c0-25.6 17.066667-42.666667 42.666667-42.666666h140.8c25.6 0 42.666667 17.066667 42.666667 42.666666s-17.066667 42.666667-42.666667 42.666667H512c-25.6 0-42.666667-17.066667-42.666667-42.666667z"></path><path d="M366.933333 81.066667c25.6-25.6 59.733333-38.4 98.133334-38.4h93.866666c38.4 0 72.533333 12.8 98.133334 38.4 17.066667 17.066667 25.6 34.133333 34.133333 55.466666h59.733333c38.4 0 72.533333 12.8 98.133334 38.4 25.6 25.6 38.4 59.733333 38.4 98.133334v567.466666c0 38.4-12.8 72.533333-38.4 98.133334-25.6 25.6-59.733333 38.4-98.133334 38.4H273.066667c-38.4 0-72.533333-12.8-98.133334-38.4-25.6-25.6-38.4-59.733333-38.4-98.133334V273.066667c0-38.4 12.8-72.533333 38.4-98.133334 25.6-25.6 59.733333-38.4 98.133334-38.4h59.733333c8.533333-21.333333 17.066667-38.4 34.133333-55.466666zM465.066667 128c-12.8 0-25.6 4.266667-38.4 17.066667-8.533333 8.533333-12.8 21.333333-12.8 34.133333 0 12.8 4.266667 25.6 17.066666 38.4 8.533333 8.533333 21.333333 17.066667 38.4 17.066667h93.866667c12.8 0 25.6-4.266667 38.4-17.066667 8.533333-8.533333 17.066667-21.333333 17.066667-38.4 0-12.8-4.266667-25.6-17.066667-38.4-17.066667-8.533333-29.866667-12.8-42.666667-12.8h-93.866666zM332.8 221.866667H273.066667c-12.8 0-25.6 4.266667-38.4 17.066666-4.266667 8.533333-12.8 21.333333-12.8 34.133334v567.466666c0 12.8 4.266667 25.6 17.066666 38.4s21.333333 17.066667 34.133334 17.066667h473.6c12.8 0 25.6-4.266667 38.4-17.066667 8.533333-8.533333 17.066667-21.333333 17.066666-38.4V273.066667c0-12.8-4.266667-25.6-17.066666-38.4-8.533333-8.533333-21.333333-17.066667-38.4-17.066667h-59.733334c-8.533333 21.333333-17.066667 38.4-34.133333 55.466667-25.6 25.6-59.733333 38.4-98.133333 38.4h-93.866667c-38.4 0-72.533333-12.8-98.133333-38.4-12.8-12.8-21.333333-29.866667-29.866667-51.2z"></path></svg></span><span class="ml-1">澶嶅埗椤甸潰</span></span></button><button type="button" class="ui-btn ui-btn-default ui-btn-flex ui-btn-sm ui-btn-icon-only ui-btn-compact-item ui-btn-compact-last-item ui-dropdown-trigger"><span role="img" class="appicon app_icon"><svg viewBox="0 0 1024 1024" width="1em" height="1em" fill="currentColor" aria-hidden="true" focusable="false" role="img"><path d="M225.834667 353.834667a42.666667 42.666667 0 0 1 60.330666 0L512 579.669333l225.834667-225.834666a42.666667 42.666667 0 1 1 60.330666 60.330666l-256 256a42.666667 42.666667 0 0 1-60.330666 0l-256-256a42.666667 42.666667 0 0 1 0-60.330666z"></path></svg></span></button></div></div></div></div></div><div class="SplitPane vertical mx-auto w-full max-w-[1560px]" style="display:flex;flex-direction:row"><div class="Pane vertical Pane1 sticky hidden os:flex" style="flex:none;width:320px;top:57px;height:calc(100vh - 57px)"><div class="flex w-full flex-col overflow-y-auto"><div class="g-sidebar flex w-full flex-col overflow-y-auto flex-1"><div class="px-2 os:px-5 mt-5"><div><div class="ui-skeleton ui-skeleton-element ui-skeleton-active ui-skeleton-block"><span class="ui-skeleton-input" style="background:var(--ui-input-bg);border-radius:var(--ui-control-border-radius)"></span></div></div></div><div class="g-sidebar-menu _tree-scroll-container_5b4e relative h-full w-full overflow-y-auto px-2 pb-2 pt-5 os:px-5 os:pb-10"><ul class="g-sidebar-item-list w-full"><li class="g-sidebar-item-link g-sidebar-item-link-active"><a class="_sidebar-tree-node_f7c1n_1 _sidebar-tree-node--selected_f7c1n_25 font-600 sidebar-tree-node-doc-5802858" title="甯姪涓績" data-discover="true" href="/help"><span class="break-word">甯姪涓績</span></a></li><li class="g-sidebar-item-link"><a class="_sidebar-tree-node_f7c1n_1" title="鏇存柊鏃ュ織" data-discover="true" href="/changelog"><span class="break-word">鏇存柊鏃ュ織</span></a></li><li class="g-sidebar-item-category"><div to="" class="_sidebar-tree-node_f7c1n_1 cursor-pointer select-none text-color" title="鍏ラ棬"><span class="break-word">鍏ラ棬</span><div class="flex-1"></div><div class="flex h-[22px] w-[22px] items-center justify-center"><span role="img" class="appicon app_icon text-disabled" style="font-size:16px"><svg viewBox="0 0 1024 1024" width="1em" height="1em" fill="currentColor" aria-hidden="true" focusable="false" role="img"><path d="M225.834667 353.834667a42.666667 42.666667 0 0 1 60.330666 0L512 579.669333l225.834667-225.834666a42.666667 42.666667 0 1 1 60.330666 60.330666l-256 256a42.666667 42.666667 0 0 1-60.330666 0l-256-256a42.666667 42.666667 0 0 1 0-60.330666z"></path></svg></span></div></div><ul class="g-sidebar-item-list ml-3 border-l border-color-split pl-2"><li class="g-sidebar-item-link"><a class="_sidebar-tree-node_f7c1n_1" title="浜у搧浠嬬粛" data-discover="true" href="/introduction"><span class="break-word">浜у搧浠嬬粛</span></a></li><li class="g-sidebar-item-link"><a class="_sidebar-tree-node_f7c1n_1" title="鑱旂郴鎴戜滑" data-discover="true" href="/contact-us"><span class="break-word">鑱旂郴鎴戜滑</span></a></li><li class="g-sidebar-item-link"><a class="_sidebar-tree-node_f7c1n_1" title="绉佹湁鍖栭儴缃? data-discover="true" href="/siyouhua"><span class="break-word">绉佹湁鍖栭儴缃?/span></a></li></ul></li><li class="g-sidebar-item-category"><div to="" class="_sidebar-tree-node_f7c1n_1 cursor-pointer select-none text-color" title="寮€濮嬩娇鐢?><span class="break-word">寮€濮嬩娇鐢?/span><div class="flex-1"></div><div class="flex h-[22px] w-[22px] items-center justify-center"><span role="img" class="appicon app_icon text-disabled" style="font-size:16px"><svg viewBox="0 0 1024 1024" width="1em" height="1em" fill="currentColor" aria-hidden="true" focusable="false" role="img"><path d="M225.834667 353.834667a42.666667 42.666667 0 0 1 60.330666 0L512 579.669333l225.834667-225.834666a42.666667 42.666667 0 1 1 60.330666 60.330666l-256 256a42.666667 42.666667 0 0 1-60.330666 0l-256-256a42.666667 42.666667 0 0 1 0-60.330666z"></path></svg></span></div></div><ul class="g-sidebar-item-list ml-3 border-l border-color-split pl-2"><li class="g-sidebar-item-link"><a class="_sidebar-tree-node_f7c1n_1" title="涓嬭浇 Apifox" data-discover="true" href="/download"><span class="break-word">涓嬭浇 Apifox</span></a></li><li class="g-sidebar-item-link"><a class="_sidebar-tree-node_f7c1n_1" title="娉ㄥ唽涓庣櫥褰? data-discover="true" href="/sign-up-and-login"><span class="break-word">娉ㄥ唽涓庣櫥褰?/span></a></li><li class="g-sidebar-item-link"><a class="_sidebar-tree-node_f7c1n_1" title="椤甸潰甯冨眬" data-discover="true" href="/page-layout"><span class="break-word">椤甸潰甯冨眬</span></a></li><li class="g-sidebar-item-link"><a class="_sidebar-tree-node_f7c1n_1" title="鍩烘湰姒傚康" data-discover="true" href="/apifox-basic-concepts"><span class="break-word">鍩烘湰姒傚康</span></a></li><li class="g-sidebar-item-category"><div to="" class="_sidebar-tree-node_f7c1n_1 cursor-pointer select-none" title="蹇€熶笂鎵?><span class="break-word">蹇€熶笂鎵?/span><div class="flex-1"></div><div class="flex h-[22px] w-[22px] items-center justify-center"><span role="img" class="appicon app_icon text-disabled" style="font-size:16px"><svg viewBox="0 0 1024 1024" width="1em" height="1em" fill="currentColor" aria-hidden="true" focusable="false" role="img"><path d="M353.834667 225.834667a42.666667 42.666667 0 0 1 60.330666 0l256 256a42.666667 42.666667 0 0 1 0 60.330666l-256 256a42.666667 42.666667 0 0 1-60.330666-60.330666L579.669333 512 353.834667 286.165333a42.666667 42.666667 0 0 1 0-60.330666z"></path></svg></span></div></div><ul class="g-sidebar-item-list hidden"><li class="g-sidebar-item-link"><a href="/getting-started">姒傝堪</a></li><li class="g-sidebar-item-link"><a href="/create-api">鏂板缓鎺ュ彛</a></li><li class="g-sidebar-item-link"><a href="/send-api-request">鍙戦€佹帴鍙ｈ姹?/a></li><li class="g-sidebar-item-link"><a href="/quick-request">蹇嵎璇锋眰</a></li><li class="g-sidebar-item-link"><a href="/add-assertions">娣诲姞鏂█</a></li><li class="g-sidebar-item-link"><a href="/create-test-scenario">鏂板缓娴嬭瘯鍦烘櫙</a></li><li class="g-sidebar-item-link"><a href="/share-api-documentation">鍒嗕韩 API 鏂囨。</a></li><li class="g-sidebar-item-link"><a href="/learn-more">浜嗚В鏇村</a></li></ul></li><li class="g-sidebar-item-category"><div to="" class="_sidebar-tree-node_f7c1n_1 cursor-pointer select-none" title="鍩虹鐭ヨ瘑"><span class="break-word">鍩虹鐭ヨ瘑</span><div class="flex-1"></div><div class="flex h-[22px] w-[22px] items-center justify-center"><span role="img" class="appicon app_icon text-disabled" style="font-size:16px"><svg viewBox="0 0 1024 1024" width="1em" height="1em" fill="currentColor" aria-hidden="true" focusable="false" role="img"><path d="M353.834667 225.834667a42.666667 42.666667 0 0 1 60.330666 0l256 256a42.666667 42.666667 0 0 1 0 60.330666l-256 256a42.666667 42.666667 0 0 1-60.330666-60.330666L579.669333 512 353.834667 286.165333a42.666667 42.666667 0 0 1 0-60.330666z"></path></svg></span></div></div><ul class="g-sidebar-item-list hidden"><li class="g-sidebar-item-category g-sidebar-item-link"><a href="">鎺ュ彛鍩烘湰淇℃伅</a><ul class="g-sidebar-item-list hidden"><li class="g-sidebar-item-link"><a href="/request-url-and-method">璇锋眰 URL 涓庢柟娉?/a></li><li class="g-sidebar-item-link"><a href="/request-params-and-body">璇锋眰鍙傛暟涓庤姹備綋</a></li><li class="g-sidebar-item-link"><a href="/request-headers">璇锋眰澶?/a></li><li class="g-sidebar-item-link"><a href="/request-param-encoding-decoding">璇锋眰鍙傛暟缂栫爜瑙ｇ爜</a></li><li class="g-sidebar-item-link"><a href="/http2">HTTP/2</a></li></ul></li><li class="g-sidebar-item-category g-sidebar-item-link"><a href="">璁よ瘉涓庢巿鏉?/a><ul class="g-sidebar-item-list hidden"><li class="g-sidebar-item-link"><a href="/authentication-and-authorization">姒傝堪</a></li><li class="g-sidebar-item-link"><a href="/authorization-types">鏀寔鐨勬巿鏉冪被鍨?/a></li><li class="g-sidebar-item-link"><a href="/digest-auth">Digest Auth</a></li><li class="g-sidebar-item-link"><a href="/oauth1">OAuth 1.0</a></li><li class="g-sidebar-item-link"><a href="/oauth2">OAuth 2.0</a></li><li class="g-sidebar-item-link"><a href="/hawk-auth">Hawk Authentication</a></li><li class="g-sidebar-item-link"><a href="/kerberos">Kerberos</a></li><li class="g-sidebar-item-link"><a href="/ntlm">NTLM</a></li><li class="g-sidebar-item-link"><a href="/akamai-edgegrid">Akamai EdgeGrid</a></li><li class="g-sidebar-item-link"><a href="/ca-and-client-certificates">CA 鍜屽鎴风璇佷功</a></li></ul></li><li class="g-sidebar-item-category g-sidebar-item-link"><a href="">鍝嶅簲涓?Cookie</a><ul class="g-sidebar-item-list hidden"><li class="g-sidebar-item-link"><a href="/response-and-cookie">姒傝堪</a></li><li class="g-sidebar-item-link"><a href="/api-response">API 鍝嶅簲</a></li><li class="g-sidebar-item-link"><a href="/create-and-send-cookie">鍒涘缓鍜屽彂閫?Cookie</a></li><li class="g-sidebar-item-link"><a href="/actual-request">瀹為檯璇锋眰</a></li><li class="g-sidebar-item-link"><a href="/extract-response-example">鎻愬彇鍝嶅簲绀轰緥</a></li></ul></li><li class="g-sidebar-item-category g-sidebar-item-link"><a href="">璇锋眰浠ｇ悊</a><ul class="g-sidebar-item-list hidden"><li class="g-sidebar-item-link"><a href="/request-proxy-in-web">缃戦〉绔腑鐨勮姹備唬鐞?/a></li><li class="g-sidebar-item-link"><a href="/share-document-proxy">鍒嗕韩鏂囨。涓殑璇锋眰浠ｇ悊</a></li><li class="g-sidebar-item-link"><a href="/client-side-request-proxy">瀹㈡埛绔腑鐨勮姹備唬鐞?/a></li></ul></li><li class="g-sidebar-item-category g-sidebar-item-link"><a href="">API Hub</a><ul class="g-sidebar-item-list hidden"><li class="g-sidebar-item-link"><a href="/apihub">API Hub</a></li></ul></li></ul></li><li class="g-sidebar-item-category"><div to="" class="_sidebar-tree-node_f7c1n_1 cursor-pointer select-none" title="瀵煎叆瀵煎嚭鏁版嵁"><span class="break-word">瀵煎叆瀵煎嚭鏁版嵁</span><div class="flex-1"></div><div class="flex h-[22px] w-[22px] items-center justify-center"><span role="img" class="appicon app_icon text-disabled" style="font-size:16px"><svg viewBox="0 0 1024 1024" width="1em" height="1em" fill="currentColor" aria-hidden="true" focusable="false" role="img"><path d="M353.834667 225.834667a42.666667 42.666667 0 0 1 60.330666 0l256 256a42.666667 42.666667 0 0 1 0 60.330666l-256 256a42.666667 42.666667 0 0 1-60.330666-60.330666L579.669333 512 353.834667 286.165333a42.666667 42.666667 0 0 1 0-60.330666z"></path></svg></span></div></div><ul class="g-sidebar-item-list hidden"><li class="g-sidebar-item-link"><a href="/import-and-export">姒傝堪</a></li><li class="g-sidebar-item-link"><a href="/manual-import">鎵嬪姩瀵煎叆</a></li><li class="g-sidebar-item-link"><a href="/scheduled-import">瀹氭椂瀵煎叆</a></li><li class="g-sidebar-item-link"><a href="/import-settings">瀵煎叆璁剧疆</a></li><li class="g-sidebar-item-link"><a href="/export-data">瀵煎嚭鏁版嵁</a></li><li class="g-sidebar-item-category g-sidebar-item-link"><a href="">鍏跺畠鏂瑰紡瀵煎叆</a><ul class="g-sidebar-item-list hidden"><li class="g-sidebar-item-link"><a href="/import-openapi-swagger">瀵煎叆 OpenAPI/Swagger</a></li><li class="g-sidebar-item-link"><a href="/import-postman">瀵煎叆 Postman</a></li><li class="g-sidebar-item-link"><a href="/import-apipost">瀵煎叆 Apipost</a></li><li class="g-sidebar-item-link"><a href="/import-eolink">瀵煎叆 Eolink</a></li><li class="g-sidebar-item-link"><a href="/import-curl">瀵煎叆 cURL</a></li><li class="g-sidebar-item-link"><a href="/import-markdown">瀵煎叆 Markdown</a></li><li class="g-sidebar-item-link"><a href="/import-insomnia">瀵煎叆 Insomnia</a></li><li class="g-sidebar-item-link"><a href="/import-apidoc">瀵煎叆 apiDoc</a></li><li class="g-sidebar-item-link"><a href="/import-har">瀵煎叆 .har 鏂囦欢</a></li><li class="g-sidebar-item-link"><a href="/import-knife4j">瀵煎叆 knife4j</a></li><li class="g-sidebar-item-link"><a href="/import-nei">瀵煎叆 NEI</a></li><li class="g-sidebar-item-link"><a href="/import-docway">瀵煎叆灏忓购楦★紙docway锛?/a></li><li class="g-sidebar-item-link"><a href="/import-apizza">瀵煎叆 Apizza</a></li><li class="g-sidebar-item-link"><a href="/import-wsdl">瀵煎叆 WSDL </a></li></ul></li></ul></li></ul></li><li class="g-sidebar-item-category"><div to="" class="_sidebar-tree-node_f7c1n_1 cursor-pointer select-none text-color" title="璁捐 API"><span class="break-word">璁捐 API</span><div class="flex-1"></div><div class="flex h-[22px] w-[22px] items-center justify-center"><span role="img" class="appicon app_icon text-disabled" style="font-size:16px"><svg viewBox="0 0 1024 1024" width="1em" height="1em" fill="currentColor" aria-hidden="true" focusable="false" role="img"><path d="M225.834667 353.834667a42.666667 42.666667 0 0 1 60.330666 0L512 579.669333l225.834667-225.834666a42.666667 42.666667 0 1 1 60.330666 60.330666l-256 256a42.666667 42.666667 0 0 1-60.330666 0l-256-256a42.666667 42.666667 0 0 1 0-60.330666z"></path></svg></span></div></div><ul class="g-sidebar-item-list ml-3 border-l border-color-split pl-2"><li class="g-sidebar-item-link"><a class="_sidebar-tree-node_f7c1n_1" title="姒傝堪" data-discover="true" href="/design-api"><span class="break-word">姒傝堪</span></a></li><li class="g-sidebar-item-link"><a class="_sidebar-tree-node_f7c1n_1" title="鏂板缓 API 椤圭洰" data-discover="true" href="/create-api-project"><span class="break-word">鏂板缓 API 椤圭洰</span></a></li><li class="g-sidebar-item-link"><a class="_sidebar-tree-node_f7c1n_1" title="鎺ュ彛鍩虹鐭ヨ瘑" data-discover="true" href="/create-an-api"><span class="break-word">鎺ュ彛鍩虹鐭ヨ瘑</span></a></li><li class="g-sidebar-item-link"><a class="_sidebar-tree-node_f7c1n_1" title="鎺ュ彛璁捐瑙勮寖" data-discover="true" href="/api-design-guidelines"><span class="break-word">鎺ュ彛璁捐瑙勮寖</span></a></li><li class="g-sidebar-item-link"><a class="_sidebar-tree-node_f7c1n_1" title="妯″潡" data-discover="true" href="/module"><span class="break-word">妯″潡</span></a></li><li class="g-sidebar-item-link"><a class="_sidebar-tree-node_f7c1n_1" title="璇锋眰浣撳绀轰緥閰嶇疆" data-discover="true" href="/multi-example-request-body"><span class="break-word">璇锋眰浣撳绀轰緥閰嶇疆</span></a></li><li class="g-sidebar-item-link"><a class="_sidebar-tree-node_f7c1n_1" title="鍝嶅簲缁勪欢" data-discover="true" href="/response-components"><span class="break-word">鍝嶅簲缁勪欢</span></a></li><li class="g-sidebar-item-link"><a class="_sidebar-tree-node_f7c1n_1" title="甯哥敤瀛楁" data-discover="true" href="/common-fields"><span class="break-word">甯哥敤瀛楁</span></a></li><li class="g-sidebar-item-link"><a class="_sidebar-tree-node_f7c1n_1" title="鍏ㄥ眬鍙傛暟" data-discover="true" href="/global-parameters"><span class="break-word">鍏ㄥ眬鍙傛暟</span></a></li><li class="g-sidebar-item-link"><a class="_sidebar-tree-node_f7c1n_1" title="鍘嗗彶璁板綍" data-discover="true" href="/api-history"><span class="break-word">鍘嗗彶璁板綍</span></a></li><li class="g-sidebar-item-link"><a class="_sidebar-tree-node_f7c1n_1" title="鎺ュ彛璇勮" data-discover="true" href="/api-comments"><span class="break-word">鎺ュ彛璇勮</span></a></li><li class="g-sidebar-item-link"><a class="_sidebar-tree-node_f7c1n_1" title="鎵归噺绠＄悊" data-discover="true" href="/bulk-operations"><span class="break-word">鎵归噺绠＄悊</span></a></li><li class="g-sidebar-item-category"><div to="" class="_sidebar-tree-node_f7c1n_1 cursor-pointer select-none" title="鏁版嵁妯″瀷"><span class="break-word">鏁版嵁妯″瀷</span><div class="flex-1"></div><div class="flex h-[22px] w-[22px] items-center justify-center"><span role="img" class="appicon app_icon text-disabled" style="font-size:16px"><svg viewBox="0 0 1024 1024" width="1em" height="1em" fill="currentColor" aria-hidden="true" focusable="false" role="img"><path d="M353.834667 225.834667a42.666667 42.666667 0 0 1 60.330666 0l256 256a42.666667 42.666667 0 0 1 0 60.330666l-256 256a42.666667 42.666667 0 0 1-60.330666-60.330666L579.669333 512 353.834667 286.165333a42.666667 42.666667 0 0 1 0-60.330666z"></path></svg></span></div></div><ul class="g-sidebar-item-list hidden"><li class="g-sidebar-item-link"><a href="/data-schemas">姒傝堪</a></li><li class="g-sidebar-item-link"><a href="/create-data-schema">鏂板缓鏁版嵁妯″瀷</a></li><li class="g-sidebar-item-link"><a href="/build-data-schemas">鏋勫缓鏁版嵁妯″瀷</a></li><li class="g-sidebar-item-link"><a href="/generate-from-json">閫氳繃 JSON 绛夌敓鎴?/a></li><li class="g-sidebar-item-link"><a href="/advanced-data-types">楂樼骇鏁版嵁绫诲瀷</a></li><li class="g-sidebar-item-category g-sidebar-item-link"><a href="">鏁版嵁妯″瀷杩涢樁</a><ul class="g-sidebar-item-list hidden"><li class="g-sidebar-item-link"><a href="/oneof-anyof-allof">浣跨敤 oneOf / anyOf / allOf 鏋勫缓缁勫悎妯″紡</a></li></ul></li></ul></li><li class="g-sidebar-item-category"><div to="" class="_sidebar-tree-node_f7c1n_1 cursor-pointer select-none" title="閴存潈缁勪欢"><span class="break-word">閴存潈缁勪欢</span><div class="flex-1"></div><div class="flex h-[22px] w-[22px] items-center justify-center"><span role="img" class="appicon app_icon text-disabled" style="font-size:16px"><svg viewBox="0 0 1024 1024" width="1em" height="1em" fill="currentColor" aria-hidden="true" focusable="false" role="img"><path d="M353.834667 225.834667a42.666667 42.666667 0 0 1 60.330666 0l256 256a42.666667 42.666667 0 0 1 0 60.330666l-256 256a42.666667 42.666667 0 0 1-60.330666-60.330666L579.669333 512 353.834667 286.165333a42.666667 42.666667 0 0 1 0-60.330666z"></path></svg></span></div></div><ul class="g-sidebar-item-list hidden"><li class="g-sidebar-item-link"><a href="/security-schemes">姒傝堪</a></li><li class="g-sidebar-item-link"><a href="/create-security-scheme">鍒涘缓閴存潈缁勪欢</a></li><li class="g-sidebar-item-link"><a href="/use-security-schemes">浣跨敤閴存潈缁勪欢</a></li><li class="g-sidebar-item-link"><a href="/security-schemes-in-docs">鍦ㄧ嚎鏂囨。涓殑閴存潈缁勪欢</a></li></ul></li><li class="g-sidebar-item-category"><div to="" class="_sidebar-tree-node_f7c1n_1 cursor-pointer select-none" title="楂樼骇鍔熻兘"><span class="break-word">楂樼骇鍔熻兘</span><div class="flex-1"></div><div class="flex h-[22px] w-[22px] items-center justify-center"><span role="img" class="appicon app_icon text-disabled" style="font-size:16px"><svg viewBox="0 0 1024 1024" width="1em" height="1em" fill="currentColor" aria-hidden="true" focusable="false" role="img"><path d="M353.834667 225.834667a42.666667 42.666667 0 0 1 60.330666 0l256 256a42.666667 42.666667 0 0 1 0 60.330666l-256 256a42.666667 42.666667 0 0 1-60.330666-60.330666L579.669333 512 353.834667 286.165333a42.666667 42.666667 0 0 1 0-60.330666z"></path></svg></span></div></div><ul class="g-sidebar-item-list hidden"><li class="g-sidebar-item-link"><a href="/api-fields">鎺ュ彛瀛楁</a></li><li class="g-sidebar-item-link"><a href="/api-status">鎺ュ彛鐘舵€?/a></li><li class="g-sidebar-item-link"><a href="/linked-test-scenarios">鍏宠仈娴嬭瘯鍦烘櫙</a></li><li class="g-sidebar-item-link"><a href="/parameter-list-appearance">鍙傛暟鍒楄〃澶栬</a></li><li class="g-sidebar-item-link"><a href="/api-unique-identifier">鎺ュ彛鍞竴鏍囪瘑</a></li></ul></li></ul></li><li class="g-sidebar-item-category"><div to="" class="_sidebar-tree-node_f7c1n_1 cursor-pointer select-none text-color" title="寮€鍙戝拰璋冭瘯 API"><span class="break-word">寮€鍙戝拰璋冭瘯 API</span><div class="flex-1"></div><div class="flex h-[22px] w-[22px] items-center justify-center"><span role="img" class="appicon app_icon text-disabled" style="font-size:16px"><svg viewBox="0 0 1024 1024" width="1em" height="1em" fill="currentColor" aria-hidden="true" focusable="false" role="img"><path d="M353.834667 225.834667a42.666667 42.666667 0 0 1 60.330666 0l256 256a42.666667 42.666667 0 0 1 0 60.330666l-256 256a42.666667 42.666667 0 0 1-60.330666-60.330666L579.669333 512 353.834667 286.165333a42.666667 42.666667 0 0 1 0-60.330666z"></path></svg></span></div></div><ul class="g-sidebar-item-list hidden"><li class="g-sidebar-item-link"><a href="/develop-and-debug-api">姒傝堪</a></li><li class="g-sidebar-item-link"><a href="/generate-request">鐢熸垚璇锋眰</a></li><li class="g-sidebar-item-link"><a href="/send-request">鍙戦€佽姹?/a></li><li class="g-sidebar-item-link"><a href="/request-history">璇锋眰鍘嗗彶</a></li><li class="g-sidebar-item-link"><a href="/api-test-cases">鎺ュ彛璋冭瘯鐢ㄤ緥</a></li><li class="g-sidebar-item-link"><a href="/7227782m0">鎺ュ彛娴嬭瘯鐢ㄤ緥</a></li><li class="g-sidebar-item-link"><a href="/dynamic-values">鍔ㄦ€佸€?/a></li><li class="g-sidebar-item-link"><a href="/validate-response">鏍￠獙鍝嶅簲</a></li><li class="g-sidebar-item-link"><a href="/design-and-request-mode">鏂囨。妯″紡/璋冭瘯妯″紡</a></li><li class="g-sidebar-item-link"><a href="/generate-code">鐢熸垚浠ｇ爜</a></li><li class="g-sidebar-item-category g-sidebar-item-link"><a href="">鐜鍜屽彉閲?/a><ul class="g-sidebar-item-list hidden"><li class="g-sidebar-item-link"><a href="/environments-and-variables">姒傝堪</a></li><li class="g-sidebar-item-link"><a href="/environments-and-services">鐜绠＄悊</a></li><li class="g-sidebar-item-link"><a href="/global-environment-session-variables">鍏ㄥ眬/鐜/妯″潡/涓存椂鍙橀噺</a></li><li class="g-sidebar-item-category g-sidebar-item-link"><a href="">Vault Secrets锛堝瘑閽ュ簱锛?/a><ul class="g-sidebar-item-list hidden"><li class="g-sidebar-item-link"><a href="/vault-secrets">鍔熻兘绠€浠?/a></li></ul></li></ul></li><li class="g-sidebar-item-category g-sidebar-item-link"><a href="">鍓嶅悗缃搷浣?amp;鑴氭湰</a><ul class="g-sidebar-item-list hidden"><li class="g-sidebar-item-link"><a href="/pre-post-processors">姒傝堪</a></li><li class="g-sidebar-item-link"><a href="/assertions">鏂█</a></li><li class="g-sidebar-item-link"><a href="/extract-variables">鎻愬彇鍙橀噺</a></li><li class="g-sidebar-item-link"><a href="/wait-time">绛夊緟鏃堕棿</a></li><li class="g-sidebar-item-category g-sidebar-item-link"><a href="">鏁版嵁搴撴搷浣?/a><ul class="g-sidebar-item-list hidden"><li class="g-sidebar-item-link"><a href="/database">姒傝堪</a></li><li class="g-sidebar-item-link"><a href="/mysql">MySQL</a></li><li class="g-sidebar-item-link"><a href="/mongodb">MongoDB</a></li><li class="g-sidebar-item-link"><a href="/redis">Redis</a></li><li class="g-sidebar-item-link"><a href="/oracle">Oracle</a></li></ul></li><li class="g-sidebar-item-category g-sidebar-item-link"><a href="">浣跨敤鑴氭湰</a><ul class="g-sidebar-item-list hidden"><li class="g-sidebar-item-link"><a href="/scripts">姒傝堪</a></li><li class="g-sidebar-item-link"><a href="/pre-request-scripts">鍓嶇疆鑴氭湰</a></li><li class="g-sidebar-item-link"><a href="/post-request-scripts">鍚庣疆鑴氭湰</a></li><li class="g-sidebar-item-link"><a href="/common-scripts">鍏叡鑴氭湰</a></li><li class="g-sidebar-item-link"><a href="/postman-script-api">pm 鑴氭湰 API</a></li><li class="g-sidebar-item-link"><a href="/js-libraries">浣跨敤 JS 绫诲簱</a></li><li class="g-sidebar-item-link"><a href="/response-data-visualization">鍝嶅簲鏁版嵁鍙鍖?/a></li><li class="g-sidebar-item-link"><a href="/call-external-programs">璋冪敤澶栭儴绋嬪簭</a></li></ul></li><li class="g-sidebar-item-category g-sidebar-item-link"><a href="">鑴氭湰绀轰緥</a><ul class="g-sidebar-item-list hidden"><li class="g-sidebar-item-link"><a href="/assertion-examples">鏂█绀轰緥</a></li><li class="g-sidebar-item-link"><a href="/use-variables-in-scripts">鑴氭湰浣跨敤鍙橀噺</a></li><li class="g-sidebar-item-link"><a href="/access-modify-request-data">鑴氭湰璇诲彇/淇敼鎺ュ彛璇锋眰淇℃伅</a></li></ul></li><li class="g-sidebar-item-category g-sidebar-item-link"><a href="">甯歌闂</a><ul class="g-sidebar-item-list hidden"><li class="g-sidebar-item-link"><a href="/5831263m0">濡備綍鑾峰彇鍔ㄦ€佸弬鏁扮殑鐪熷疄鍊煎苟鍔犲瘑锛?/a></li><li class="g-sidebar-item-link"><a href="/5831265m0">鑴氭湰杩愯鍚庯紝鎻愬彇鐨勬暟瀛楋紙bigint锛夌簿搴︿涪澶卞簲璇ュ浣曞鐞嗭紵</a></li></ul></li></ul></li><li class="g-sidebar-item-category g-sidebar-item-link"><a href="">API 璋冭瘯</a><ul class="g-sidebar-item-list hidden"><li class="g-sidebar-item-link"><a href="/graphql">GraphQL 璋冭瘯</a></li><li class="g-sidebar-item-link"><a href="/websocket">WebSocket 璋冭瘯</a></li><li class="g-sidebar-item-link"><a href="/socketio">Socket.IO 璋冭瘯</a></li><li class="g-sidebar-item-link"><a href="/sse">SSE 璋冭瘯</a></li><li class="g-sidebar-item-link"><a href="/soap">SOAP/WebService</a></li><li class="g-sidebar-item-link"><a href="/grpc">gRPC 璋冭瘯</a></li><li class="g-sidebar-item-link"><a href="/webhook">Webhook 璋冭瘯</a></li><li class="g-sidebar-item-link"><a href="/request-proxy-debugging">浣跨敤璇锋眰浠ｇ悊璋冭瘯</a></li><li class="g-sidebar-item-category g-sidebar-item-link"><a href="">Dubbo 璋冭瘯</a><ul class="g-sidebar-item-list hidden"><li class="g-sidebar-item-link"><a href="/create-dubbo-api">鏂板缓 Dubbo 鎺ュ彛</a></li><li class="g-sidebar-item-link"><a href="/debug-dubbo-api">璋冭瘯 Dubbo 鎺ュ彛</a></li><li class="g-sidebar-item-link"><a href="/dubbo-api-documentation">Dubbo 鎺ュ彛鏂囨。</a></li></ul></li><li class="g-sidebar-item-category g-sidebar-item-link"><a href="">TCP锛圫ocket锛?/a><ul class="g-sidebar-item-list hidden"><li class="g-sidebar-item-link"><a href="/tcp-socket">Socket 鎺ュ彛鍔熻兘绠€浠?/a></li><li class="g-sidebar-item-link"><a href="/message-data-processor">鎶ユ枃鏁版嵁澶勭悊鍣?/a></li></ul></li></ul></li></ul></li><li class="g-sidebar-item-category"><div to="" class="_sidebar-tree-node_f7c1n_1 cursor-pointer select-none text-color" title="Mock 鏁版嵁"><span class="break-word">Mock 鏁版嵁</span><div class="flex-1"></div><div class="flex h-[22px] w-[22px] items-center justify-center"><span role="img" class="appicon app_icon text-disabled" style="font-size:16px"><svg viewBox="0 0 1024 1024" width="1em" height="1em" fill="currentColor" aria-hidden="true" focusable="false" role="img"><path d="M353.834667 225.834667a42.666667 42.666667 0 0 1 60.330666 0l256 256a42.666667 42.666667 0 0 1 0 60.330666l-256 256a42.666667 42.666667 0 0 1-60.330666-60.330666L579.669333 512 353.834667 286.165333a42.666667 42.666667 0 0 1 0-60.330666z"></path></svg></span></div></div><ul class="g-sidebar-item-list hidden"><li class="g-sidebar-item-link"><a href="/mock">姒傝堪</a></li><li class="g-sidebar-item-link"><a href="/smart-mock">鏅鸿兘 Mock</a></li><li class="g-sidebar-item-link"><a href="/custom-mock">鑷畾涔?Mock</a></li><li class="g-sidebar-item-link"><a href="/mock-priority-rules">Mock 浼樺厛绾?/a></li><li class="g-sidebar-item-link"><a href="/mock-scripts">Mock 鑴氭湰</a></li><li class="g-sidebar-item-link"><a href="/cloud-mock">浜戠 Mock</a></li><li class="g-sidebar-item-link"><a href="/runner-mock">鑷墭绠?Runner Mock</a></li></ul></li><li class="g-sidebar-item-category"><div to="" class="_sidebar-tree-node_f7c1n_1 cursor-pointer select-none text-color" title="鑷姩鍖栨祴璇?><span class="break-word">鑷姩鍖栨祴璇?/span><div class="flex-1"></div><div class="flex h-[22px] w-[22px] items-center justify-center"><span role="img" class="appicon app_icon text-disabled" style="font-size:16px"><svg viewBox="0 0 1024 1024" width="1em" height="1em" fill="currentColor" aria-hidden="true" focusable="false" role="img"><path d="M353.834667 225.834667a42.666667 42.666667 0 0 1 60.330666 0l256 256a42.666667 42.666667 0 0 1 0 60.330666l-256 256a42.666667 42.666667 0 0 1-60.330666-60.330666L579.669333 512 353.834667 286.165333a42.666667 42.666667 0 0 1 0-60.330666z"></path></svg></span></div></div><ul class="g-sidebar-item-list hidden"><li class="g-sidebar-item-link"><a href="/automated-testing">姒傝堪</a></li><li class="g-sidebar-item-category g-sidebar-item-link"><a href="">缂栨帓娴嬭瘯鍦烘櫙</a><ul class="g-sidebar-item-list hidden"><li class="g-sidebar-item-link"><a href="/new-test-scenario">鏂板缓娴嬭瘯鍦烘櫙</a></li><li class="g-sidebar-item-link"><a href="/pass-data-between-test-steps">娴嬭瘯姝ラ闂翠紶閫掓暟鎹?/a></li><li class="g-sidebar-item-link"><a href="/flow-control-conditions">娴嬭瘯娴佺▼鎺у埗鏉′欢</a></li><li class="g-sidebar-item-link"><a href="/sync-from-endpoint-or-test-case">浠庢帴鍙?鐢ㄤ緥鍚屾鏁版嵁</a></li><li class="g-sidebar-item-link"><a href="/import-apis-or-cases-cross-projects">璺ㄩ」鐩鍏ユ帴鍙?鐢ㄤ緥</a></li><li class="g-sidebar-item-link"><a href="/export-test-scenario-data">瀵煎嚭娴嬭瘯鍦烘櫙鏁版嵁</a></li></ul></li><li class="g-sidebar-item-category g-sidebar-item-link"><a href="">杩愯娴嬭瘯鍦烘櫙</a><ul class="g-sidebar-item-list hidden"><li class="g-sidebar-item-link"><a href="/run-test-scenarios">杩愯娴嬭瘯鍦烘櫙</a></li><li class="g-sidebar-item-link"><a href="/batch-run-test-scenarios">鎵归噺杩愯娴嬭瘯鍦烘櫙</a></li><li class="g-sidebar-item-link"><a href="/data-driven-testing">鏁版嵁椹卞姩娴嬭瘯</a></li><li class="g-sidebar-item-link"><a href="/scheduled-tasks">瀹氭椂浠诲姟</a></li><li class="g-sidebar-item-link"><a href="/manage-cross-project-environments">绠＄悊鍏跺畠椤圭洰鎺ュ彛鐨勮繍琛岀幆澧?/a></li></ul></li><li class="g-sidebar-item-category g-sidebar-item-link"><a href="">娴嬭瘯鎶ュ憡</a><ul class="g-sidebar-item-list hidden"><li class="g-sidebar-item-link"><a href="/test-reports">娴嬭瘯鎶ュ憡</a></li></ul></li><li class="g-sidebar-item-category g-sidebar-item-link"><a href="">API 娴嬭瘯</a><ul class="g-sidebar-item-list hidden"><li class="g-sidebar-item-link"><a href="/performance-testing">鎬ц兘娴嬭瘯</a></li><li class="g-sidebar-item-link"><a href="/integration-testing">闆嗘垚娴嬭瘯</a></li><li class="g-sidebar-item-link"><a href="/end-to-end-testing">绔埌绔祴璇?/a></li><li class="g-sidebar-item-link"><a href="/regression-testing">鍥炲綊娴嬭瘯</a></li></ul></li><li class="g-sidebar-item-category g-sidebar-item-link"><a href="">Apifox CLI</a><ul class="g-sidebar-item-list hidden"><li class="g-sidebar-item-link"><a href="/apifox-cli">姒傝堪</a></li><li class="g-sidebar-item-link"><a href="/install-and-run-cli">瀹夎鍜岃繍琛?CLI</a></li><li class="g-sidebar-item-link"><a href="/cli-command-options">CLI 鍛戒护閫夐」</a></li></ul></li><li class="g-sidebar-item-category g-sidebar-item-link"><a href="">CI/CD</a><ul class="g-sidebar-item-list hidden"><li class="g-sidebar-item-link"><a href="/cicd">姒傝堪</a></li><li class="g-sidebar-item-link"><a href="/integration-with-jenkins">涓?Jenkins 闆嗘垚</a></li><li class="g-sidebar-item-link"><a href="/integration-with-gitlab">涓?Gitlab 闆嗘垚</a></li><li class="g-sidebar-item-link"><a href="/integration-with-github-actions">涓?Github Actions 闆嗘垚</a></li><li class="g-sidebar-item-link"><a href="/integration-with-other-ci-cd-platforms">涓庡叾瀹冩洿澶?CI/CD 骞冲彴闆嗘垚</a></li><li class="g-sidebar-item-link"><a href="/git-commit-triggered-testing">Git 鎻愪氦鑷姩瑙﹀彂娴嬭瘯</a></li></ul></li></ul></li><li class="g-sidebar-item-category"><div to="" class="_sidebar-tree-node_f7c1n_1 cursor-pointer select-none text-color" title="鍙戝竷 API 鏂囨。"><span class="break-word">鍙戝竷 API 鏂囨。</span><div class="flex-1"></div><div class="flex h-[22px] w-[22px] items-center justify-center"><span role="img" class="appicon app_icon text-disabled" style="font-size:16px"><svg viewBox="0 0 1024 1024" width="1em" height="1em" fill="currentColor" aria-hidden="true" focusable="false" role="img"><path d="M353.834667 225.834667a42.666667 42.666667 0 0 1 60.330666 0l256 256a42.666667 42.666667 0 0 1 0 60.330666l-256 256a42.666667 42.666667 0 0 1-60.330666-60.330666L579.669333 512 353.834667 286.165333a42.666667 42.666667 0 0 1 0-60.330666z"></path></svg></span></div></div><ul class="g-sidebar-item-list hidden"><li class="g-sidebar-item-link"><a href="/api-documentation">姒傝堪</a></li><li class="g-sidebar-item-link"><a href="/quick-share">蹇嵎鍒嗕韩</a></li><li class="g-sidebar-item-link"><a href="/view-api-documentation">鏌ョ湅 API 鏂囨。</a></li><li class="g-sidebar-item-link"><a href="/publish-documentation-site">鍙戝竷鏂囨。绔?/a></li><li class="g-sidebar-item-link"><a href="/page-layout-settings">椤甸潰甯冨眬璁剧疆</a></li><li class="g-sidebar-item-link"><a href="/custom-css-js-html">鑷畾涔夐〉闈唬鐮?/a></li><li class="g-sidebar-item-link"><a href="/custom-domain">鑷畾涔夊煙鍚?/a></li><li class="g-sidebar-item-link"><a href="/ai-features">AI 鐩稿叧鐗规€?/a></li><li class="g-sidebar-item-link"><a href="/seo-settings">SEO 璁剧疆</a></li><li class="g-sidebar-item-category g-sidebar-item-link"><a href="">楂樼骇璁剧疆</a><ul class="g-sidebar-item-list hidden"><li class="g-sidebar-item-link"><a href="/documentation-site-search-settings">鏂囨。绔欐悳  绱㈣缃?/a></li><li class="g-sidebar-item-link"><a href="/cross-origin-proxy">璺ㄥ煙浠ｇ悊</a></li><li class="g-sidebar-item-link"><a href="/google-analytics-integration">鏂囨。绔欐帴鍏?Google Analytics</a></li><li class="g-sidebar-item-link"><a href="/documentation-sidebar-settings">鏂囨。宸︿晶鐩綍璁剧疆</a></li><li class="g-sidebar-item-link"><a href="/documentation-visibility-settings">鏂囨。鍙鎬ц缃?/a></li><li class="g-sidebar-item-link"><a href="/url-link-conventions">鍦ㄧ嚎 URL 閾炬帴瑙勮寖</a></li></ul></li><li class="g-sidebar-item-category g-sidebar-item-link"><a href="">API 鐗堟湰</a><ul class="g-sidebar-item-list hidden"><li class="g-sidebar-item-link"><a href="/api-version">鍔熻兘绠€浠?/a></li><li class="g-sidebar-item-link"><a href="/create-api-version">鍒涘缓 API 鐗堟湰</a></li><li class="g-sidebar-item-link"><a href="/publish-api-version">鍙戝竷 API 鐗堟湰</a></li><li class="g-sidebar-item-link"><a href="/quick-share-api-version">蹇嵎鍒嗕韩 API 鐗堟湰</a></li></ul></li></ul></li><li class="g-sidebar-item-category"><div to="" class="_sidebar-tree-node_f7c1n_1 cursor-pointer select-none text-color" title="杩唬鍒嗘敮"><span class="break-word">杩唬鍒嗘敮</span><div class="flex-1"></div><div class="flex h-[22px] w-[22px] items-center justify-center"><span role="img" class="appicon app_icon text-disabled" style="font-size:16px"><svg viewBox="0 0 1024 1024" width="1em" height="1em" fill="currentColor" aria-hidden="true" focusable="false" role="img"><path d="M353.834667 225.834667a42.666667 42.666667 0 0 1 60.330666 0l256 256a42.666667 42.666667 0 0 1 0 60.330666l-256 256a42.666667 42.666667 0 0 1-60.330666-60.330666L579.669333 512 353.834667 286.165333a42.666667 42.666667 0 0 1 0-60.330666z"></path></svg></span></div></div><ul class="g-sidebar-item-list hidden"><li class="g-sidebar-item-link"><a href="/sprint-branch">鍔熻兘绠€浠?/a></li><li class="g-sidebar-item-link"><a href="/create-sprint-branch">鏂板缓杩唬鍒嗘敮</a></li><li class="g-sidebar-item-link"><a href="/api-changes-in-sprint-branch">鍦ㄨ凯浠ｅ垎鏀腑鏀瑰姩 API</a></li><li class="g-sidebar-item-link"><a href="/test-api-in-sprint-branch">鍦ㄨ凯浠ｅ垎鏀腑娴嬭瘯 API</a></li><li class="g-sidebar-item-link"><a href="/merge-sprint-branch">鍚堝苟杩唬鍒嗘敮</a></li><li class="g-sidebar-item-link"><a href="/manage-sprint-branch">绠＄悊杩唬鍒嗘敮</a></li></ul></li><li class="g-sidebar-item-category"><div to="" class="_sidebar-tree-node_f7c1n_1 cursor-pointer select-none text-color" title="绠＄悊涓績"><span class="break-word">绠＄悊涓績</span><div class="flex-1"></div><div class="flex h-[22px] w-[22px] items-center justify-center"><span role="img" class="appicon app_icon text-disabled" style="font-size:16px"><svg viewBox="0 0 1024 1024" width="1em" height="1em" fill="currentColor" aria-hidden="true" focusable="false" role="img"><path d="M353.834667 225.834667a42.666667 42.666667 0 0 1 60.330666 0l256 256a42.666667 42.666667 0 0 1 0 60.330666l-256 256a42.666667 42.666667 0 0 1-60.330666-60.330666L579.669333 512 353.834667 286.165333a42.666667 42.666667 0 0 1 0-60.330666z"></path></svg></span></div></div><ul class="g-sidebar-item-list hidden"><li class="g-sidebar-item-category g-sidebar-item-link"><a href="">鍏ラ┗娓呭崟</a><ul class="g-sidebar-item-list hidden"><li class="g-sidebar-item-link"><a href="/management-center">浜嗚В鍩烘湰姒傚康</a></li><li class="g-sidebar-item-link"><a href="/onboard-team">鍥㈤槦鍏ラ┗</a></li></ul></li><li class="g-sidebar-item-category g-sidebar-item-link"><a href="">绠＄悊鍥㈤槦</a><ul class="g-sidebar-item-list hidden"><li class="g-sidebar-item-link"><a href="/member-roles-and-permissions">鎴愬憳瑙掕壊涓庢潈闄愯缃?/a></li><li class="g-sidebar-item-link"><a href="/team-basic-operations">鍥㈤槦鍩烘湰鎿嶄綔</a></li><li class="g-sidebar-item-link"><a href="/team-member-management">鍥㈤槦鎴愬憳绠＄悊</a></li><li class="g-sidebar-item-category g-sidebar-item-link"><a href="">鍥㈤槦璧勬簮</a><ul class="g-sidebar-item-list hidden"><li class="g-sidebar-item-link"><a href="/universal-runner">閫氱敤 Runner</a></li><li class="g-sidebar-item-link"><a href="/request-proxy-agent">璇锋眰浠ｇ悊 Agent</a></li><li class="g-sidebar-item-link"><a href="/team-variables">鍥㈤槦鍙橀噺</a></li></ul></li><li class="g-sidebar-item-category g-sidebar-item-link"><a href="">瀹炴椂鍗忎綔</a><ul class="g-sidebar-item-list hidden"><li class="g-sidebar-item-link"><a href="/team-collaboration">鍥㈤槦鍗忎綔</a></li></ul></li></ul></li><li class="g-sidebar-item-category g-sidebar-item-link"><a href="">绠＄悊椤圭洰</a><ul class="g-sidebar-item-list hidden"><li class="g-sidebar-item-link"><a href="/project-basic-operations">椤圭洰鍩烘湰鎿嶄綔</a></li><li class="g-sidebar-item-link"><a href="/project-member-management">椤圭洰鎴愬憳绠＄悊</a></li><li class="g-sidebar-item-category g-sidebar-item-link"><a href="">閫氱煡璁剧疆</a><ul class="g-sidebar-item-list hidden"><li class="g-sidebar-item-link"><a href="/message-notifications">鍔熻兘绠€浠?/a></li><li class="g-sidebar-item-link"><a href="/notification-targets">閫氱煡瀵硅薄</a></li><li class="g-sidebar-item-link"><a href="/notification-events">閫氱煡浜嬩欢</a></li></ul></li><li class="g-sidebar-item-category g-sidebar-item-link"><a href="">椤圭洰璧勬簮</a><ul class="g-sidebar-item-list hidden"><li class="g-sidebar-item-link"><a href="/database-connections">鏁版嵁搴撹繛鎺?/a></li><li class="g-sidebar-item-link"><a href="/7230090m0">Git 浠撳簱杩炴帴</a></li></ul></li></ul></li><li class="g-sidebar-item-category g-sidebar-item-link"><a href="">绠＄悊缁勭粐</a><ul class="g-sidebar-item-list hidden"><li class="g-sidebar-item-category g-sidebar-item-link"><a href="">鍗曠偣鐧诲綍锛圫SO锛?/a><ul class="g-sidebar-item-list hidden"><li class="g-sidebar-item-link"><a href="/sso">鍔熻兘绠€浠?/a></li><li class="g-sidebar-item-link"><a href="/configure-sso-for-organization">涓虹粍缁囬厤缃崟鐐圭櫥褰?/a></li><li class="g-sidebar-item-link"><a href="/manage-user-accounts">绠＄悊鐢ㄦ埛璐﹀彿</a></li><li class="g-sidebar-item-link"><a href="/map-groups-to-teams">灏嗙粍鏄犲皠鍒板洟闃?/a></li><li class="g-sidebar-item-link"><a href="/microsoft-entra-id">Microsoft Entra ID</a></li></ul></li><li class="g-sidebar-item-category g-sidebar-item-link"><a href="">SCIM 鐢ㄦ埛绠＄悊</a><ul class="g-sidebar-item-list hidden"><li class="g-sidebar-item-link"><a href="/scim-user-management">鍔熻兘绠€浠?/a></li><li class="g-sidebar-item-link"><a href="/scmi-microsoft-entra-id">Microsoft Entra ID</a></li></ul></li><li class="g-sidebar-item-category g-sidebar-item-link"><a href="">缁勭粐璧勬簮</a><ul class="g-sidebar-item-list hidden"><li class="g-sidebar-item-link"><a href="/self-hosted-runner">鑷墭绠?Runner</a></li></ul></li><li class="g-sidebar-item-category g-sidebar-item-link"><a href="">璁㈠崟绠＄悊</a><ul class="g-sidebar-item-list hidden"><li class="g-sidebar-item-link"><a href="/7191758m0">缁勭粐浠樿垂缁忕悊</a></li></ul></li></ul></li></ul></li><li class="g-sidebar-item-category"><div to="" class="_sidebar-tree-node_f7c1n_1 cursor-pointer select-none text-color" title="绂荤嚎绌洪棿"><span class="break-word">绂荤嚎绌洪棿</span><div class="flex-1"></div><div class="flex h-[22px] w-[22px] items-center justify-center"><span role="img" class="appicon app_icon text-disabled" style="font-size:16px"><svg viewBox="0 0 1024 1024" width="1em" height="1em" fill="currentColor" aria-hidden="true" focusable="false" role="img"><path d="M353.834667 225.834667a42.666667 42.666667 0 0 1 60.330666 0l256 256a42.666667 42.666667 0 0 1 0 60.330666l-256 256a42.666667 42.666667 0 0 1-60.330666-60.330666L579.669333 512 353.834667 286.165333a42.666667 42.666667 0 0 1 0-60.330666z"></path></svg></span></div></div><ul class="g-sidebar-item-list hidden"><li class="g-sidebar-item-link"><a href="/offline-space">鍔熻兘绠€浠?/a></li></ul></li><li class="g-sidebar-item-category"><div to="" class="_sidebar-tree-node_f7c1n_1 cursor-pointer select-none text-color" title="IDEA 鎻掍欢"><span class="break-word">IDEA 鎻掍欢</span><div class="flex-1"></div><div class="flex h-[22px] w-[22px] items-center justify-center"><span role="img" class="appicon app_icon text-disabled" style="font-size:16px"><svg viewBox="0 0 1024 1024" width="1em" height="1em" fill="currentColor" aria-hidden="true" focusable="false" role="img"><path d="M353.834667 225.834667a42.666667 42.666667 0 0 1 60.330666 0l256 256a42.666667 42.666667 0 0 1 0 60.330666l-256 256a42.666667 42.666667 0 0 1-60.330666-60.330666L579.669333 512 353.834667 286.165333a42.666667 42.666667 0 0 1 0-60.330666z"></path></svg></span></div></div><ul class="g-sidebar-item-list hidden"><li class="g-sidebar-item-link"><a href="/apifox-idea-plugin">蹇€熶笂鎵?/a></li><li class="g-sidebar-item-link"><a href="/generate-api-docs-with-idea">鐢熸垚鎺ュ彛鏂囨。</a></li><li class="g-sidebar-item-link"><a href="/generate-data-schemas-with-idea">鐢熸垚鏁版嵁妯″瀷</a></li><li class="g-sidebar-item-category g-sidebar-item-link"><a href="">閰嶇疆</a><ul class="g-sidebar-item-list hidden"><li class="g-sidebar-item-link"><a href="/5801717m0">鍏ㄥ眬閰嶇疆</a></li><li class="g-sidebar-item-link"><a href="/5801720m0">椤圭洰鍐呴厤缃?/a></li><li class="g-sidebar-item-link"><a href="/5801721m0">鍙厤缃鍒?/a></li><li class="g-sidebar-item-link"><a href="/5801722m0">鑴氭湰宸ュ叿</a></li><li class="g-sidebar-item-link"><a href="/5801723m0">Groovy 鏈湴鎵╁睍</a></li></ul></li><li class="g-sidebar-item-category g-sidebar-item-link"><a href="">杩涢樁閰嶇疆</a><ul class="g-sidebar-item-list hidden"><li class="g-sidebar-item-link"><a href="/5801730m0">娉ㄩ噴瑙勮寖璇存槑</a></li><li class="g-sidebar-item-link"><a href="/5801731m0">妗嗘灦鏀寔</a></li></ul></li><li class="g-sidebar-item-category g-sidebar-item-link"><a href="">甯歌闂</a><ul class="g-sidebar-item-list hidden"><li class="g-sidebar-item-link"><a href="/apifox-idea-plugin-faqs">甯歌闂</a></li></ul></li></ul></li><li class="g-sidebar-item-category"><div to="" class="_sidebar-tree-node_f7c1n_1 cursor-pointer select-none text-color" title="娴忚鍣ㄦ墿灞?><span class="break-word">娴忚鍣ㄦ墿灞?/span><div class="flex-1"></div><div class="flex h-[22px] w-[22px] items-center justify-center"><span role="img" class="appicon app_icon text-disabled" style="font-size:16px"><svg viewBox="0 0 1024 1024" width="1em" height="1em" fill="currentColor" aria-hidden="true" focusable="false" role="img"><path d="M353.834667 225.834667a42.666667 42.666667 0 0 1 60.330666 0l256 256a42.666667 42.666667 0 0 1 0 60.330666l-256 256a42.666667 42.666667 0 0 1-60.330666-60.330666L579.669333 512 353.834667 286.165333a42.666667 42.666667 0 0 1 0-60.330666z"></path></svg></span></div></div><ul class="g-sidebar-item-list hidden"><li class="g-sidebar-item-link"><a href="/chrome-extension">Chrome</a></li><li class="g-sidebar-item-link"><a href="/microsoft-edge-extension">Microsoft Edge</a></li></ul></li><li class="g-sidebar-item-category"><div to="" class="_sidebar-tree-node_f7c1n_1 cursor-pointer select-none text-color" title="Apifox AI 鍔熻兘"><span class="break-word">Apifox AI 鍔熻兘</span><div class="flex-1"></div><div class="flex h-[22px] w-[22px] items-center justify-center"><span role="img" class="appicon app_icon text-disabled" style="font-size:16px"><svg viewBox="0 0 1024 1024" width="1em" height="1em" fill="currentColor" aria-hidden="true" focusable="false" role="img"><path d="M353.834667 225.834667a42.666667 42.666667 0 0 1 60.330666 0l256 256a42.666667 42.666667 0 0 1 0 60.330666l-256 256a42.666667 42.666667 0 0 1-60.330666-60.330666L579.669333 512 353.834667 286.165333a42.666667 42.666667 0 0 1 0-60.330666z"></path></svg></span></div></div><ul class="g-sidebar-item-list hidden"><li class="g-sidebar-item-link"><a href="/apifox-ai">鎬昏</a></li><li class="g-sidebar-item-link"><a href="/enable-ai-features">鍚敤 AI 鍔熻兘</a></li><li class="g-sidebar-item-link"><a href="/generate-data-schemas-with-ai">淇敼鏁版嵁妯″瀷</a></li><li class="g-sidebar-item-link"><a href="/api-compliance-check">鎺ュ彛瑙勮寖鎬ф娴?/a></li><li class="g-sidebar-item-link"><a href="/field-naming">瀛楁鍛藉悕</a></li><li class="g-sidebar-item-link"><a href="/apifox-ai-faqs">甯歌闂</a></li></ul></li><li class="g-sidebar-item-category"><div to="" class="_sidebar-tree-node_f7c1n_1 cursor-pointer select-none text-color" title="Apifox MCP Server"><span class="break-word">Apifox MCP Server</span><div class="flex-1"></div><div class="flex h-[22px] w-[22px] items-center justify-center"><span role="img" class="appicon app_icon text-disabled" style="font-size:16px"><svg viewBox="0 0 1024 1024" width="1em" height="1em" fill="currentColor" aria-hidden="true" focusable="false" role="img"><path d="M353.834667 225.834667a42.666667 42.666667 0 0 1 60.330666 0l256 256a42.666667 42.666667 0 0 1 0 60.330666l-256 256a42.666667 42.666667 0 0 1-60.330666-60.330666L579.669333 512 353.834667 286.165333a42.666667 42.666667 0 0 1 0-60.330666z"></path></svg></span></div></div><ul class="g-sidebar-item-list hidden"><li class="g-sidebar-item-link"><a href="/apifox-mcp-server">姒傝堪</a></li><li class="g-sidebar-item-link"><a href="/6327888m0">閫氳繃 MCP 浣跨敤 Apifox 椤圭洰鍐呯殑 API 鏂囨。</a></li><li class="g-sidebar-item-link"><a href="/6327890m0">閫氳繃 MCP 浣跨敤鍏紑鍙戝竷鐨?API 鏂囨。</a></li><li class="g-sidebar-item-link"><a href="/6327891m0">閫氳繃 MCP 浣跨敤 OpenAPI/Swagger鏂囨。</a></li></ul></li><li class="g-sidebar-item-category"><div to="" class="_sidebar-tree-node_f7c1n_1 cursor-pointer select-none text-color" title="鏈€浣冲疄璺?><span class="break-word">鏈€浣冲疄璺?/span><div class="flex-1"></div><div class="flex h-[22px] w-[22px] items-center justify-center"><span role="img" class="appicon app_icon text-disabled" style="font-size:16px"><svg viewBox="0 0 1024 1024" width="1em" height="1em" fill="currentColor" aria-hidden="true" focusable="false" role="img"><path d="M353.834667 225.834667a42.666667 42.666667 0 0 1 60.330666 0l256 256a42.666667 42.666667 0 0 1 0 60.330666l-256 256a42.666667 42.666667 0 0 1-60.330666-60.330666L579.669333 512 353.834667 286.165333a42.666667 42.666667 0 0 1 0-60.330666z"></path></svg></span></div></div><ul class="g-sidebar-item-list hidden"><li class="g-sidebar-item-link"><a href="/5743521m0">  姒傝堪</a></li><li class="g-sidebar-item-link"><a href="/5793498m0">鎺ュ彛涔嬮棿濡備綍浼犻€掓暟鎹?/a></li><li class="g-sidebar-item-link"><a href="/5802184m0">鐧诲綍鎬侊紙Auth锛夊浣曞鐞?/a></li><li class="g-sidebar-item-link"><a href="/5802226m0">鎺ュ彛绛惧悕濡備綍澶勭悊</a></li><li class="g-sidebar-item-link"><a href="/5802338m0">濡備綍鍔犲瘑/瑙ｅ瘑鎺ュ彛鏁版嵁</a></li><li class="g-sidebar-item-link"><a href="/5802381m0">Jenkins 瀹氭椂瑙﹀彂浠诲姟</a></li><li class="g-sidebar-item-link"><a href="/5835636m0">濡備綍璁＄畻 AI 闂瓟鎴愭湰</a></li><li class="g-sidebar-item-link"><a href="/6378711m0">涓庡叾浠栨垚鍛樺叡鐢ㄦ暟鎹簱杩炴帴閰嶇疆</a></li><li class="g-sidebar-item-link"><a href="/6378956m0">閫氳繃 CLI 杩愯鍖呭惈浜戠鏁版嵁搴撹繛鎺ラ厤缃殑娴嬭瘯鍦烘櫙</a></li><li class="g-sidebar-item-link"><a href="/6379146m0">閫氳繃 Runner 杩愯鍖呭惈浜戠鏁版嵁搴撹繛鎺ラ厤缃殑娴嬭瘯鍦烘櫙</a></li><li class="g-sidebar-item-link"><a href="/6822783m0">Apifox 娴嬭瘯姝ラ涔嬮棿鎬庝箞浼犻€掓暟鎹紵</a></li></ul></li><li class="g-sidebar-item-category"><div to="" class="_sidebar-tree-node_f7c1n_1 cursor-pointer select-none text-color" title="璐﹀彿&amp;搴旂敤璁剧疆"><span class="break-word">璐﹀彿&amp;搴旂敤璁剧疆</span><div class="flex-1"></div><div class="flex h-[22px] w-[22px] items-center justify-center"><span role="img" class="appicon app_icon text-disabled" style="font-size:16px"><svg viewBox="0 0 1024 1024" width="1em" height="1em" fill="currentColor" aria-hidden="true" focusable="false" role="img"><path d="M353.834667 225.834667a42.666667 42.666667 0 0 1 60.330666 0l256 256a42.666667 42.666667 0 0 1 0 60.330666l-256 256a42.666667 42.666667 0 0 1-60.330666-60.330666L579.669333 512 353.834667 286.165333a42.666667 42.666667 0 0 1 0-60.330666z"></path></svg></span></div></div><ul class="g-sidebar-item-list hidden"><li class="g-sidebar-item-link"><a href="/account-settings">璐﹀彿璁剧疆</a></li><li class="g-sidebar-item-link"><a href="/api-access-token">API 璁块棶浠ょ墝</a></li><li class="g-sidebar-item-link"><a href="/notifications">閫氱煡</a></li><li class="g-sidebar-item-link"><a href="/language-settings">璇█璁剧疆</a></li><li class="g-sidebar-item-link"><a href="/keyboard-shortcuts">蹇嵎閿?/a></li><li class="g-sidebar-item-link"><a href="/network-proxy">缃戠粶浠ｇ悊</a></li><li class="g-sidebar-item-link"><a href="/data-backup-restore">鏁版嵁澶囦唤涓庢仮澶?/a></li><li class="g-sidebar-item-link"><a href="/update-apifox">鏇存柊 Apifox</a></li><li class="g-sidebar-item-link"><a href="/experimental-features">瀹為獙鎬у姛鑳?/a></li></ul></li><li class="g-sidebar-item-category"><div to="" class="_sidebar-tree-node_f7c1n_1 cursor-pointer select-none text-color" title="韬唤楠岃瘉 &amp; Auth 閴存潈鎸囧崡"><span class="break-word">韬唤楠岃瘉 &amp; Auth 閴存潈鎸囧崡</span><div class="flex-1"></div><div class="flex h-[22px] w-[22px] items-center justify-center"><span role="img" class="appicon app_icon text-disabled" style="font-size:16px"><svg viewBox="0 0 1024 1024" width="1em" height="1em" fill="currentColor" aria-hidden="true" focusable="false" role="img"><path d="M353.834667 225.834667a42.666667 42.666667 0 0 1 60.330666 0l256 256a42.666667 42.666667 0 0 1 0 60.330666l-256 256a42.666667 42.666667 0 0 1-60.330666-60.330666L579.669333 512 353.834667 286.165333a42.666667 42.666667 0 0 1 0-60.330666z"></path></svg></span></div></div><ul class="g-sidebar-item-list hidden"><li class="g-sidebar-item-link"><a href="/what-is-api-key">浠€涔堟槸 API Key</a></li><li class="g-sidebar-item-link"><a href="/what-is-bearer-token">浠€涔堟槸 Bearer Token</a></li><li class="g-sidebar-item-link"><a href="/what-is-jwt">浠€涔堟槸 JWT</a></li><li class="g-sidebar-item-link"><a href="/what-is-basic-auth">浠€涔堟槸 Basic Auth</a></li><li class="g-sidebar-item-link"><a href="/what-is-digest-auth">浠€涔堟槸 Digest Auth</a></li><li class="g-sidebar-item-link"><a href="/what-is-oauth-1">浠€涔堟槸 OAuth 1.0</a></li><li class="g-sidebar-item-category g-sidebar-item-link"><a href="">浠€涔堟槸 OAuth 2.0</a><ul class="g-sidebar-item-list hidden"><li class="g-sidebar-item-link"><a href="/what-is-oauth-2">浠€涔堟槸 OAuth 2.0</a></li><li class="g-sidebar-item-link"><a href="/authorization-code-grant">鎺堟潈鐮佹巿鏉冪被鍨?/a></li><li class="g-sidebar-item-link"><a href="/authorization-code-grant-with-pkce">鎺堟潈鐮佹巿鏉冪被鍨嬶紝甯︽湁 PKCE</a></li><li class="g-sidebar-item-link"><a href="/implicit-grant">闅愬紡鎺堟潈绫诲瀷</a></li><li class="g-sidebar-item-link"><a href="/password-credentials-grant">瀵嗙爜鍑瘉鎺堟潈绫诲瀷</a></li><li class="g-sidebar-item-link"><a href="/client-credentials-grant">瀹㈡埛绔嚟璇佹巿鏉冪被鍨?/a></li></ul></li></ul></li><li class="g-sidebar-item-category"><div to="" class="_sidebar-tree-node_f7c1n_1 cursor-pointer select-none text-color" title="鏈嶅姟涓庨殣绉佸崗璁?><span class="break-word">鏈嶅姟涓庨殣绉佸崗璁?/span><div class="flex-1"></div><div class="flex h-[22px] w-[22px] items-center justify-center"><span role="img" class="appicon app_icon text-disabled" style="font-size:16px"><svg viewBox="0 0 1024 1024" width="1em" height="1em" fill="currentColor" aria-hidden="true" focusable="false" role="img"><path d="M353.834667 225.834667a42.666667 42.666667 0 0 1 60.330666 0l256 256a42.666667 42.666667 0 0 1 0 60.330666l-256 256a42.666667 42.666667 0 0 1-60.330666-60.330666L579.669333 512 353.834667 286.165333a42.666667 42.666667 0 0 1 0-60.330666z"></path></svg></span></div></div><ul class="g-sidebar-item-list hidden"><li class="g-sidebar-item-link"><a href="/service-agreement">鏈嶅姟鍗忚</a></li><li class="g-sidebar-item-link"><a href="/privacy-policy">闅愮鍗忚</a></li><li class="g-sidebar-item-link"><a href="/sla">鏈嶅姟绛夌骇鍗忚</a></li></ul></li><li class="g-sidebar-item-category"><div to="" class="_sidebar-tree-node_f7c1n_1 cursor-pointer select-none text-color" title="鍙傝€冭祫鏂?><span class="break-word">鍙傝€冭祫鏂?/span><div class="flex-1"></div><div class="flex h-[22px] w-[22px] items-center justify-center"><span role="img" class="appicon app_icon text-disabled" style="font-size:16px"><svg viewBox="0 0 1024 1024" width="1em" height="1em" fill="currentColor" aria-hidden="true" focusable="false" role="img"><path d="M353.834667 225.834667a42.666667 42.666667 0 0 1 60.330666 0l256 256a42.666667 42.666667 0 0 1 0 60.330666l-256 256a42.666667 42.666667 0 0 1-60.330666-60.330666L579.669333 512 353.834667 286.165333a42.666667 42.666667 0 0 1 0-60.330666z"></path></svg></span></div></div><ul class="g-sidebar-item-list hidden"><li class="g-sidebar-item-link"><a href="/api-first-design">API 璁捐浼樺厛鐞嗗康</a></li><li class="g-sidebar-item-link"><a href="/json-schema">JSON Schema 浠嬬粛</a></li><li class="g-sidebar-item-link"><a href="/jsonpath">JSONPath 浠嬬粛</a></li><li class="g-sidebar-item-link"><a href="/xpath">XPath 浠嬬粛</a></li><li class="g-sidebar-item-link"><a href="/apifox-markdown">Apifox Markdown 璇硶</a></li><li class="g-sidebar-item-link"><a href="/csv">CSV 鏍煎紡瑙勮寖</a></li><li class="g-sidebar-item-link"><a href="/regex">姝ｅ垯琛ㄨ揪寮?/a></li><li class="g-sidebar-item-link"><a href="/install-java">瀹夎 Java 鐜</a></li><li class="g-sidebar-item-link"><a href="/runner-environment">Runner 杩愯鐜</a></li><li class="g-sidebar-item-link"><a href="/programming-data-types">甯歌缂栫▼璇█瀵瑰簲鐨勬暟鎹被鍨?/a></li><li class="g-sidebar-item-link"><a href="/socket-packet-fragmentation-and-merging">Socket 绮樺寘鍜屽垎鍖呴棶棰?/a></li><li class="g-sidebar-item-link"><a href="/glossary">璇嶆眹琛?/a></li><li class="g-sidebar-item-category g-sidebar-item-link"><a href="">Apifox Swagger 鎵╁睍</a><ul class="g-sidebar-item-list hidden"><li class="g-sidebar-item-link"><a href="/apifox-openapi-swagger-extension">姒傝堪</a></li><li class="g-sidebar-item-link"><a href="/x-apifox-folder">x-apifox-folder</a></li><li class="g-sidebar-item-link"><a href="/x-apifox-status">x-apifox-status</a></li><li class="g-sidebar-item-link"><a href="/x-apifox-name">x-apifox-name</a></li><li class="g-sidebar-item-link"><a href="/x-apifox-maintainer">x-apifox-maintainer</a></li></ul></li><li class="g-sidebar-item-category g-sidebar-item-link"><a href="">Apifox JSON Schema 鎵╁睍</a><ul class="g-sidebar-item-list hidden"><li class="g-sidebar-item-link"><a href="/apifox-json-schema-extension">姒傝堪</a></li><li class="g-sidebar-item-link"><a href="/x-apifox-mock">x-apifox-mock</a></li><li class="g-sidebar-item-link"><a href="/x-apifox-orders">x-apifox-orders</a></li><li class="g-sidebar-item-link"><a href="/x-apifox-enum">x-apifox-enum</a></li></ul></li><li class="g-sidebar-item-category g-sidebar-item-link"><a href="/46262793f0">鍔ㄦ€佸€艰〃杈惧紡</a></li></ul></li><li class="g-sidebar-item-category g-sidebar-item-link"><a class="_sidebar-tree-node_f7c1n_1 text-color" title="甯歌闂" data-discover="true" href="/5835927m0"><span class="break-word">甯歌闂</span><div class="flex-1"></div></a></li></ul></div></div></div></div><span role="presentation" class="hidden os:flex Resizer vertical" data-active="false"><span data-active="false" class="Resizer-line hover:bg-border-color-base"></span></span><div class="Pane vertical Pane2" style="flex:1"><div class="w-full px-5 pt-5 os:pl-10"><div><div class="w-full"><div class="flex w-full @container/doc-content"><div class="w-0 flex-1 os:pt-5 _container-one_1sj7b_1"><div class="flex items-center justify-between mb-2 hidden os:flex"><nav class="ui-breadcrumb w-0 flex-1 text-[14px] leading-base _docs-breadcrumb_1hvzp_1"><ol><li><span class="ui-breadcrumb-link">甯姪鏂囨。</span><span class="ui-breadcrumb-separator"><span role="img" class="appicon app_icon align-middle" style="font-size:14px"><svg viewBox="0 0 1024 1024" width="1em" height="1em" fill="currentColor" aria-hidden="true" focusable="false" role="img"><path d="M396.501333 311.168a42.666667 42.666667 0 0 1 60.330667 0l170.666667 170.666667a42.666667 42.666667 0 0 1 0 60.330666l-170.666667 170.666667a42.666667 42.666667 0 0 1-60.330667-60.330667L537.002667 512 396.501333 371.498667a42.666667 42.666667 0 0 1 0-60.330667z"></path></svg></span></span></li></ol></nav><div class="flex items-center gap-2"><div class="ui-space-compact ui-dropdown-button flex items-center"><button type="button" class="ui-btn ui-btn-default ui-btn-sm ui-btn-compact-item ui-btn-compact-first-item"><span><span role="img" class="appicon app_icon" style="font-size:12px"><svg viewBox="0 0 1024 1024" width="1em" height="1em" fill="currentColor" aria-hidden="true" focusable="false" role="img"><path d="M328.533333 512c0-25.6 17.066667-42.666667 42.666667-42.666667s42.666667 17.066667 42.666667 42.666667-17.066667 42.666667-42.666667 42.666667-42.666667-17.066667-42.666667-42.666667zM469.333333 512c0-25.6 17.066667-42.666667 42.666667-42.666667h140.8c25.6 0 42.666667 17.066667 42.666667 42.666667s-17.066667 42.666667-42.666667 42.666667H512c-25.6 0-42.666667-17.066667-42.666667-42.666667zM328.533333 699.733333c0-25.6 17.066667-42.666667 42.666667-42.666666s42.666667 17.066667 42.666667 42.666666-17.066667 42.666667-42.666667 42.666667-42.666667-17.066667-42.666667-42.666667zM469.333333 699.733333c0-25.6 17.066667-42.666667 42.666667-42.666666h140.8c25.6 0 42.666667 17.066667 42.666667 42.666666s-17.066667 42.666667-42.666667 42.666667H512c-25.6 0-42.666667-17.066667-42.666667-42.666667z"></path><path d="M366.933333 81.066667c25.6-25.6 59.733333-38.4 98.133334-38.4h93.866666c38.4 0 72.533333 12.8 98.133334 38.4 17.066667 17.066667 25.6 34.133333 34.133333 55.466666h59.733333c38.4 0 72.533333 12.8 98.133334 38.4 25.6 25.6 38.4 59.733333 38.4 98.133334v567.466666c0 38.4-12.8 72.533333-38.4 98.133334-25.6 25.6-59.733333 38.4-98.133334 38.4H273.066667c-38.4 0-72.533333-12.8-98.133334-38.4-25.6-25.6-38.4-59.733333-38.4-98.133334V273.066667c0-38.4 12.8-72.533333 38.4-98.133334 25.6-25.6 59.733333-38.4 98.133334-38.4h59.733333c8.533333-21.333333 17.066667-38.4 34.133333-55.466666zM465.066667 128c-12.8 0-25.6 4.266667-38.4 17.066667-8.533333 8.533333-12.8 21.333333-12.8 34.133333 0 12.8 4.266667 25.6 17.066666 38.4 8.533333 8.533333 21.333333 17.066667 38.4 17.066667h93.866667c12.8 0 25.6-4.266667 38.4-17.066667 8.533333-8.533333 17.066667-21.333333 17.066667-38.4 0-12.8-4.266667-25.6-17.066667-38.4-17.066667-8.533333-29.866667-12.8-42.666667-12.8h-93.866666zM332.8 221.866667H273.066667c-12.8 0-25.6 4.266667-38.4 17.066666-4.266667 8.533333-12.8 21.333333-12.8 34.133334v567.466666c0 12.8 4.266667 25.6 17.066666 38.4s21.333333 17.066667 34.133334 17.066667h473.6c12.8 0 25.6-4.266667 38.4-17.066667 8.533333-8.533333 17.066667-21.333333 17.066666-38.4V273.066667c0-12.8-4.266667-25.6-17.066666-38.4-8.533333-8.533333-21.333333-17.066667-38.4-17.066667h-59.733334c-8.533333 21.333333-17.066667 38.4-34.133333 55.466667-25.6 25.6-59.733333 38.4-98.133333 38.4h-93.866667c-38.4 0-72.533333-12.8-98.133333-38.4-12.8-12.8-21.333333-29.866667-29.866667-51.2z"></path></svg></span><span class="ml-1">澶嶅埗椤甸潰</span></span></button><button type="button" class="ui-btn ui-btn-default ui-btn-flex ui-btn-sm ui-btn-icon-only ui-btn-compact-item ui-btn-compact-last-item ui-dropdown-trigger"><span role="img" class="appicon app_icon"><svg viewBox="0 0 1024 1024" width="1em" height="1em" fill="currentColor" aria-hidden="true" focusable="false" role="img"><path d="M225.834667 353.834667a42.666667 42.666667 0 0 1 60.330666 0L512 579.669333l225.834667-225.834666a42.666667 42.666667 0 1 1 60.330666 60.330666l-256 256a42.666667 42.666667 0 0 1-60.330666 0l-256-256a42.666667 42.666667 0 0 1 0-60.330666z"></path></svg></span></button></div></div></div><div class="flex items-start justify-between gap-2 mb-4"><div class="flex w-0 flex-1 gap-2"><h1 class="text-[32px] font-600 leading-10 text-color" title="甯姪涓績">甯姪涓績</h1></div></div><div class="_markdown-container_txmk1_1 mb-8"><div class="markdown-body remark-mdx"><section data-first="true">娆㈣繋鏉ュ埌 <a href="https://apifox.com/" title="" data-href="https://apifox.com/" target="_blank">Apifox</a> 甯姪鏂囨。锛佽繖浠芥寚鍗楁棬鍦ㄥ府鍔╀綘楂樻晥鎺屾彙鍜屼娇鐢?Apifox锛屼綘鍙互閫氳繃浠ヤ笅涓婚蹇€熷畾浣嶆墍闇€淇℃伅銆?/section><h2 id="鍏ラ棬" class="markdown-doc-viewer-heading">鍏ラ棬<a class="anchor" aria-hidden="true" tabindex="-1" href="#鍏ラ棬"><span class="icon-link">#</span></a></h2><div class="_cardGroup_o6dzj_70" data-markdown-component="x-card-group" style="grid-template-columns:repeat(3, minmax(0, 1fr))"><div class="_card_o6dzj_12 undefined" data-markdown-component="x-card" data-first="true"><a class="hidden" rel="noreferrer"></a><div class="_cardContainer_o6dzj_61"><div class="_cardTitle_o6dzj_40"></div><span class="_cardContent_o6dzj_49 markdown-child-root"><img src="/raiz5jee8eiph0eeFooV/api/v1/projects/5097254/resources/505412/image-preview?onlineShareType=apidoc&amp;locale=zh-CN" style="background-color:transparent;width:64px" data-first="true" loading="lazy" class=""/><section><section style="font-size:24px;font-weight:600;display:block;margin:20px 0;line-height:1.2" data-first="true" data-last="true">浠嬬粛</section></section><section>瀛︿範 Apifox 鐨勫熀鏈敤娉曞拰鍩虹鐭ヨ瘑銆?/section><div data-type="list" class="markdown-child-root" data-last="true"><div data-type="list-item" data-index="0" data-orderer="false" class="markdown-child-root flex" data-first="true"><div class="_unOrderContainer_1v6rr_18"><div class="_dash_1v6rr_28"></div></div><div class="markdown-child-root"><section data-first="true" data-last="true"><a href="/introduction" title="" data-first="true" data-last="true" data-href="/introduction" data-discover="true">浠嬬粛 Apifox</a></section></div></div><div data-type="list-item" data-index="1" data-orderer="false" class="markdown-child-root flex"><div class="_unOrderContainer_1v6rr_18"><div class="_dash_1v6rr_28"></div></div><div class="markdown-child-root"><section data-first="true" data-last="true"><a href="/page-layout" title="" data-first="true" data-last="true" data-href="/page-layout" data-discover="true">椤甸潰甯冨眬</a></section></div></div><div data-type="list-item" data-index="2" data-orderer="false" class="markdown-child-root flex" data-last="true"><div class="_unOrderContainer_1v6rr_18"><div class="_dash_1v6rr_28"></div></div><div class="markdown-child-root"><section data-first="true" data-last="true"><a href="/apifox-basic-concepts" title="" data-first="true" data-last="true" data-href="/apifox-basic-concepts" data-discover="true">鍩烘湰姒傚康</a></section></div></div></div></span></div></div><div class="_card_o6dzj_12 undefined" data-markdown-component="x-card"><a class="hidden" rel="noreferrer"></a><div class="_cardContainer_o6dzj_61"><div class="_cardTitle_o6dzj_40"></div><span class="_cardContent_o6dzj_49 markdown-child-root"><img src="/raiz5jee8eiph0eeFooV/api/v1/projects/5097254/resources/488414/image-preview?onlineShareType=apidoc&amp;locale=zh-CN" style="background-color:transparent;width:64px" data-first="true" loading="lazy" class=""/><section><section style="font-size:24px;font-weight:600;display:block;margin:20px 0;line-height:1.2" data-first="true" data-last="true">蹇€熶笂鎵?/section></section><section>浣跨敤 Apifox 璁捐鍜屾祴璇曚綘鐨勭涓€涓帴鍙ｃ€?/section><div data-type="list" class="markdown-child-root" data-last="true"><div data-type="list-item" data-index="0" data-orderer="false" class="markdown-child-root flex" data-first="true"><div class="_unOrderContainer_1v6rr_18"><div class="_dash_1v6rr_28"></div></div><div class="markdown-child-root"><section data-first="true" data-last="true"><a href="/create-api" title="" data-first="true" data-last="true" data-href="/create-api" data-discover="true">鏂板缓鎺ュ彛</a></section></div></div><div data-type="list-item" data-index="1" data-orderer="false" class="markdown-child-root flex"><div class="_unOrderContainer_1v6rr_18"><div class="_dash_1v6rr_28"></div></div><div class="markdown-child-root"><section data-first="true" data-last="true"><a href="/send-api-request" title="" data-first="true" data-last="true" data-href="/send-api-request" data-discover="true">鍙戦€佹帴鍙ｈ姹?/a></section></div></div><div data-type="list-item" data-index="2" data-orderer="false" class="markdown-child-root flex" data-last="true"><div class="_unOrderContainer_1v6rr_18"><div class="_dash_1v6rr_28"></div></div><div class="markdown-child-root"><section data-first="true" data-last="true"><a href="/create-test-scenario" title="" data-first="true" data-last="true" data-href="/create-test-scenario" data-discover="true">鏂板缓娴嬭瘯鍦烘櫙</a></section></div></div></div></span></div></div><div class="_card_o6dzj_12 undefined" data-markdown-component="x-card" data-last="true"><a class="hidden" rel="noreferrer"></a><div class="_cardContainer_o6dzj_61"><div class="_cardTitle_o6dzj_40"></div><span class="_cardContent_o6dzj_49 markdown-child-root"><img src="/raiz5jee8eiph0eeFooV/api/v1/projects/5097254/resources/488416/image-preview?onlineShareType=apidoc&amp;locale=zh-CN" style="background-color:transparent;width:64px" data-first="true" loading="lazy" class=""/><section><section style="font-size:24px;font-weight:600;display:block;margin:20px 0;line-height:1.2" data-first="true" data-last="true">鍥㈤槦鍏ラ┗鎸囧崡</section></section><section>浠庡叾浠栧伐鍏疯縼绉绘暟鎹埌 Apifox锛屼互鍙婂洟闃熺鐞嗐€?/section><div data-type="list" class="markdown-child-root" data-last="true"><div data-type="list-item" data-index="0" data-orderer="false" class="markdown-child-root flex" data-first="true"><div class="_unOrderContainer_1v6rr_18"><div class="_dash_1v6rr_28"></div></div><div class="markdown-child-root"><section data-first="true" data-last="true"><a href="/import-postman" title="" data-first="true" data-last="true" data-href="/import-postman" data-discover="true">浠?Postman 杩佺Щ</a></section></div></div><div data-type="list-item" data-index="1" data-orderer="false" class="markdown-child-root flex"><div class="_unOrderContainer_1v6rr_18"><div class="_dash_1v6rr_28"></div></div><div class="markdown-child-root"><section data-first="true" data-last="true"><a href="/import-openapi-swagger" title="" data-first="true" data-last="true" data-href="/import-openapi-swagger" data-discover="true">瀵煎叆 OAS/Swagger</a></section></div></div><div data-type="list-item" data-index="2" data-orderer="false" class="markdown-child-root flex" data-last="true"><div class="_unOrderContainer_1v6rr_18"><div class="_dash_1v6rr_28"></div></div><div class="markdown-child-root"><section data-first="true" data-last="true"><a href="/onboard-team" title="" data-first="true" data-last="true" data-href="/onboard-team" data-discover="true">鍥㈤槦鍏ラ┗涓庡崗浣?/a></section></div></div></div></span></div></div></div><h2 id="鎺㈢储-apifox" class="markdown-doc-viewer-heading">鎺㈢储 Apifox<a class="anchor" aria-hidden="true" tabindex="-1" href="#鎺㈢储-apifox"><span class="icon-link">#</span></a></h2><div class="_cardGroup_o6dzj_70" data-markdown-component="x-card-group" style="grid-template-columns:repeat(3, minmax(0, 1fr))"><div class="_card_o6dzj_12 undefined" data-markdown-component="x-card" data-first="true"><a class="hidden" rel="noreferrer"></a><div class="_cardContainer_o6dzj_61"><div class="_cardTitle_o6dzj_40"></div><span class="_cardContent_o6dzj_49 markdown-child-root"><img src="/raiz5jee8eiph0eeFooV/api/v1/projects/5097254/resources/488424/image-preview?onlineShareType=apidoc&amp;locale=zh-CN" style="background-color:transparent;width:64px" data-first="true" loading="lazy" class=""/><section><section style="font-size:24px;font-weight:600;display:block;margin:20px 0;line-height:1.2" data-first="true" data-last="true">鍙戦€佽姹?/section></section><section>鍙戦€?HTTP銆乬RPC銆丏ubbo銆乄ebSocket 绛夎姹傘€?/section><div data-type="list" class="markdown-child-root" data-last="true"><div data-type="list-item" data-index="0" data-orderer="false" class="markdown-child-root flex" data-first="true"><div class="_unOrderContainer_1v6rr_18"><div class="_dash_1v6rr_28"></div></div><div class="markdown-child-root"><section data-first="true" data-last="true"><a href="/quick-request" title="" data-first="true" data-last="true" data-href="/quick-request" data-discover="true">HTTP 璇锋眰</a></section></div></div><div data-type="list-item" data-index="1" data-orderer="false" class="markdown-child-root flex"><div class="_unOrderContainer_1v6rr_18"><div class="_dash_1v6rr_28"></div></div><div class="markdown-child-root"><section data-first="true" data-last="true"><a href="/graphql" title="" data-first="true" data-last="true" data-href="/graphql" data-discover="true">GraphQL</a></section></div></div><div data-type="list-item" data-index="2" data-orderer="false" class="markdown-child-root flex" data-last="true"><div class="_unOrderContainer_1v6rr_18"><div class="_dash_1v6rr_28"></div></div><div class="markdown-child-root"><section data-first="true" data-last="true"><a href="/grpc" title="" data-first="true" data-last="true" data-href="/grpc" data-discover="true">gRPC</a></section></div></div></div></span></div></div><div class="_card_o6dzj_12 undefined" data-markdown-component="x-card"><a class="hidden" rel="noreferrer"></a><div class="_cardContainer_o6dzj_61"><div class="_cardTitle_o6dzj_40"></div><span class="_cardContent_o6dzj_49 markdown-child-root"><img src="/raiz5jee8eiph0eeFooV/api/v1/projects/5097254/resources/488425/image-preview?onlineShareType=apidoc&amp;locale=zh-CN" style="background-color:transparent;width:64px" data-first="true" loading="lazy" class=""/><section><section style="font-size:24px;font-weight:600;display:block;margin:20px 0;line-height:1.2" data-first="true" data-last="true">璁捐 API</section></section><section>鍦ㄦ敮鎸?JSON Schema 鐨勫己澶у彲瑙嗗寲缁勪欢涓璁?API銆?/section><div data-type="list" class="markdown-child-root" data-last="true"><div data-type="list-item" data-index="0" data-orderer="false" class="markdown-child-root flex" data-first="true"><div class="_unOrderContainer_1v6rr_18"><div class="_dash_1v6rr_28"></div></div><div class="markdown-child-root"><section data-first="true" data-last="true"><a href="/create-an-api" title="" data-first="true" data-last="true" data-href="/create-an-api" data-discover="true">鍙鍖?OAS 璁捐</a></section></div></div><div data-type="list-item" data-index="1" data-orderer="false" class="markdown-child-root flex"><div class="_unOrderContainer_1v6rr_18"><div class="_dash_1v6rr_28"></div></div><div class="markdown-child-root"><section data-first="true" data-last="true"><a href="/data-schemas" title="" data-first="true" data-last="true" data-href="/data-schemas" data-discover="true">浣跨敤鏁版嵁妯″瀷</a></section></div></div><div data-type="list-item" data-index="2" data-orderer="false" class="markdown-child-root flex" data-last="true"><div class="_unOrderContainer_1v6rr_18"><div class="_dash_1v6rr_28"></div></div><div class="markdown-child-root"><section data-first="true" data-last="true"><a href="/api-history" title="" data-first="true" data-last="true" data-href="/api-history" data-discover="true">鎺ュ彛鍘嗗彶璁板綍</a></section></div></div></div></span></div></div><div class="_card_o6dzj_12 undefined" data-markdown-component="x-card"><a class="hidden" rel="noreferrer"></a><div class="_cardContainer_o6dzj_61"><div class="_cardTitle_o6dzj_40"></div><span class="_cardContent_o6dzj_49 markdown-child-root"><img src="/raiz5jee8eiph0eeFooV/api/v1/projects/5097254/resources/488426/image-preview?onlineShareType=apidoc&amp;locale=zh-CN" style="background-color:transparent;width:64px" data-first="true" loading="lazy" class=""/><section><section style="font-size:24px;font-weight:600;display:block;margin:20px 0;line-height:1.2" data-first="true" data-last="true">璋冭瘯 API</section></section><section>鍙戦€?API 璇锋眰锛岃嚜鍔ㄦ牎楠岃繑鍥炲搷搴斻€?/section><div data-type="list" class="markdown-child-root" data-last="true"><div data-type="list-item" data-index="0" data-orderer="false" class="markdown-child-root flex" data-first="true"><div class="_unOrderContainer_1v6rr_18"><div class="_dash_1v6rr_28"></div></div><div class="markdown-child-root"><section data-first="true" data-last="true"><a href="/validate-response" title="" data-first="true" data-last="true" data-href="/validate-response" data-discover="true">鏍￠獙鍝嶅簲</a></section></div></div><div data-type="list-item" data-index="1" data-orderer="false" class="markdown-child-root flex"><div class="_unOrderContainer_1v6rr_18"><div class="_dash_1v6rr_28"></div></div><div class="markdown-child-root"><section data-first="true" data-last="true"><a href="/pre-post-processors" title="" data-first="true" data-last="true" data-href="/pre-post-processors" data-discover="true">鍓嶅悗缃搷浣?/a></section></div></div><div data-type="list-item" data-index="2" data-orderer="false" class="markdown-child-root flex" data-last="true"><div class="_unOrderContainer_1v6rr_18"><div class="_dash_1v6rr_28"></div></div><div class="markdown-child-root"><section data-first="true" data-last="true"><a href="/database" title="" data-first="true" data-last="true" data-href="/database" data-discover="true">鏁版嵁搴撴搷浣?/a></section></div></div></div></span></div></div><div class="_card_o6dzj_12 undefined" data-markdown-component="x-card"><a class="hidden" rel="noreferrer"></a><div class="_cardContainer_o6dzj_61"><div class="_cardTitle_o6dzj_40"></div><span class="_cardContent_o6dzj_49 markdown-child-root"><img src="/raiz5jee8eiph0eeFooV/api/v1/projects/5097254/resources/488427/image-preview?onlineShareType=apidoc&amp;locale=zh-CN" style="background-color:transparent;width:64px" data-first="true" loading="lazy" class=""/><section><section style="font-size:24px;font-weight:600;display:block;margin:20px 0;line-height:1.2" data-first="true" data-last="true">娴嬭瘯 API</section></section><section>鏋勫缓鏀寔寰幆銆佹潯浠跺垎鏀瓑鍔熻兘鐨勬祴璇曞満鏅€?/section><div data-type="list" class="markdown-child-root" data-last="true"><div data-type="list-item" data-index="0" data-orderer="false" class="markdown-child-root flex" data-first="true"><div class="_unOrderContainer_1v6rr_18"><div class="_dash_1v6rr_28"></div></div><div class="markdown-child-root"><section data-first="true" data-last="true"><a href="/new-test-scenario" title="" data-first="true" data-last="true" data-href="/new-test-scenario" data-discover="true">鍙鍖栫紪鎺?/a></section></div></div><div data-type="list-item" data-index="1" data-orderer="false" class="markdown-child-root flex"><div class="_unOrderContainer_1v6rr_18"><div class="_dash_1v6rr_28"></div></div><div class="markdown-child-root"><section data-first="true" data-last="true"><a href="/scheduled-tasks" title="" data-first="true" data-last="true" data-href="/scheduled-tasks" data-discover="true">瀹氭椂浠诲姟</a></section></div></div><div data-type="list-item" data-index="2" data-orderer="false" class="markdown-child-root flex" data-last="true"><div class="_unOrderContainer_1v6rr_18"><div class="_dash_1v6rr_28"></div></div><div class="markdown-child-root"><section data-first="true" data-last="true"><a href="/performance-testing" title="" data-first="true" data-last="true" data-href="/performance-testing" data-discover="true">鎬ц兘娴嬭瘯</a></section></div></div></div></span></div></div><div class="_card_o6dzj_12 undefined" data-markdown-component="x-card"><a class="hidden" rel="noreferrer"></a><div class="_cardContainer_o6dzj_61"><div class="_cardTitle_o6dzj_40"></div><span class="_cardContent_o6dzj_49 markdown-child-root"><img src="/raiz5jee8eiph0eeFooV/api/v1/projects/5097254/resources/488428/image-preview?onlineShareType=apidoc&amp;locale=zh-CN" style="background-color:transparent;width:64px" data-first="true" loading="lazy" class=""/><section><section style="font-size:24px;font-weight:600;display:block;margin:20px 0;line-height:1.2" data-first="true" data-last="true">Mock API</section></section><section>鐢ㄧ湡瀹?Mock 鏁版嵁鍦ㄦ帴鍙ｆ湭瀹屾垚鍓嶅紑鍙戝墠绔姛鑳姐€?/section><div data-type="list" class="markdown-child-root" data-last="true"><div data-type="list-item" data-index="0" data-orderer="false" class="markdown-child-root flex" data-first="true"><div class="_unOrderContainer_1v6rr_18"><div class="_dash_1v6rr_28"></div></div><div class="markdown-child-root"><section data-first="true" data-last="true"><a href="/smart-mock" title="" data-first="true" data-last="true" data-href="/smart-mock" data-discover="true">鏅鸿兘 Mock</a></section></div></div><div data-type="list-item" data-index="1" data-orderer="false" class="markdown-child-root flex"><div class="_unOrderContainer_1v6rr_18"><div class="_dash_1v6rr_28"></div></div><div class="markdown-child-root"><section data-first="true" data-last="true"><a href="/cloud-mock" title="" data-first="true" data-last="true" data-href="/cloud-mock" data-discover="true">浜戠 Mock</a></section></div></div><div data-type="list-item" data-index="2" data-orderer="false" class="markdown-child-root flex" data-last="true"><div class="_unOrderContainer_1v6rr_18"><div class="_dash_1v6rr_28"></div></div><div class="markdown-child-root"><section data-first="true" data-last="true"><a href="/custom-mock" title="" data-first="true" data-last="true" data-href="/custom-mock" data-discover="true">Mock 鏈熸湜</a></section></div></div></div></span></div></div><div class="_card_o6dzj_12 undefined" data-markdown-component="x-card"><a class="hidden" rel="noreferrer"></a><div class="_cardContainer_o6dzj_61"><div class="_cardTitle_o6dzj_40"></div><span class="_cardContent_o6dzj_49 markdown-child-root"><img src="/raiz5jee8eiph0eeFooV/api/v1/projects/5097254/resources/488429/image-preview?onlineShareType=apidoc&amp;locale=zh-CN" style="background-color:transparent;width:64px" data-first="true" loading="lazy" class=""/><section><section style="font-size:24px;font-weight:600;display:block;margin:20px 0;line-height:1.2" data-first="true" data-last="true">鍙戝竷 API 鏂囨。</section></section><section>涓€閿垎浜?鍙戝竷缇庤鐨?API 鏂囨。銆?/section><div data-type="list" class="markdown-child-root" data-last="true"><div data-type="list-item" data-index="0" data-orderer="false" class="markdown-child-root flex" data-first="true"><div class="_unOrderContainer_1v6rr_18"><div class="_dash_1v6rr_28"></div></div><div class="markdown-child-root"><section data-first="true" data-last="true"><a href="/quick-share" title="" data-first="true" data-last="true" data-href="/quick-share" data-discover="true">鍒嗕韩 API 鏂囨。</a></section></div></div><div data-type="list-item" data-index="1" data-orderer="false" class="markdown-child-root flex"><div class="_unOrderContainer_1v6rr_18"><div class="_dash_1v6rr_28"></div></div><div class="markdown-child-root"><section data-first="true" data-last="true"><a href="/publish-documentation-site" title="" data-first="true" data-last="true" data-href="/publish-documentation-site" data-discover="true">鍙戝竷鏂囨。绔?/a></section></div></div><div data-type="list-item" data-index="2" data-orderer="false" class="markdown-child-root flex" data-last="true"><div class="_unOrderContainer_1v6rr_18"><div class="_dash_1v6rr_28"></div></div><div class="markdown-child-root"><section data-first="true" data-last="true"><a href="/page-layout-settings" title="" data-first="true" data-last="true" data-href="/page-layout-settings" data-discover="true">鑷畾涔夐〉闈㈠竷灞€</a></section></div></div></div></span></div></div><div class="_card_o6dzj_12 undefined" data-markdown-component="x-card"><a class="hidden" rel="noreferrer"></a><div class="_cardContainer_o6dzj_61"><div class="_cardTitle_o6dzj_40"></div><span class="_cardContent_o6dzj_49 markdown-child-root"><img src="/raiz5jee8eiph0eeFooV/api/v1/projects/5097254/resources/488430/image-preview?onlineShareType=apidoc&amp;locale=zh-CN" style="background-color:transparent;width:64px" data-first="true" loading="lazy" class=""/><section><section style="font-size:24px;font-weight:600;display:block;margin:20px 0;line-height:1.2" data-first="true" data-last="true">杩唬鍒嗘敮</section></section><section>杩唬鍒嗘敮鍔熻兘鍔╁姏鍥㈤槦杩涜鍒嗘敮鍗忎綔銆?/section><div data-type="list" class="markdown-child-root" data-last="true"><div data-type="list-item" data-index="0" data-orderer="false" class="markdown-child-root flex" data-first="true"><div class="_unOrderContainer_1v6rr_18"><div class="_dash_1v6rr_28"></div></div><div class="markdown-child-root"><section data-first="true" data-last="true"><a href="/create-sprint-branch" title="" data-first="true" data-last="true" data-href="/create-sprint-branch" data-discover="true">鏂板缓杩唬鍒嗘敮</a></section></div></div><div data-type="list-item" data-index="1" data-orderer="false" class="markdown-child-root flex"><div class="_unOrderContainer_1v6rr_18"><div class="_dash_1v6rr_28"></div></div><div class="markdown-child-root"><section data-first="true" data-last="true"><a href="/test-api-in-sprint-branch" title="" data-first="true" data-last="true" data-href="/test-api-in-sprint-branch" data-discover="true">鍦ㄥ垎鏀腑娴嬭瘯鎺ュ彛</a></section></div></div><div data-type="list-item" data-index="2" data-orderer="false" class="markdown-child-root flex" data-last="true"><div class="_unOrderContainer_1v6rr_18"><div class="_dash_1v6rr_28"></div></div><div class="markdown-child-root"><section data-first="true" data-last="true"><a href="/merge-sprint-branch" title="" data-first="true" data-last="true" data-href="/merge-sprint-branch" data-discover="true">鍚堝苟杩唬鍒嗘敮</a></section></div></div></div></span></div></div><div class="_card_o6dzj_12 undefined" data-markdown-component="x-card"><a class="hidden" rel="noreferrer"></a><div class="_cardContainer_o6dzj_61"><div class="_cardTitle_o6dzj_40"></div><span class="_cardContent_o6dzj_49 markdown-child-root"><img src="/raiz5jee8eiph0eeFooV/api/v1/projects/5097254/resources/488431/image-preview?onlineShareType=apidoc&amp;locale=zh-CN" style="background-color:transparent;width:64px" data-first="true" loading="lazy" class=""/><section><section style="font-size:24px;font-weight:600;display:block;margin:20px 0;line-height:1.2" data-first="true" data-last="true">鍥㈤槦涓庨」鐩?/section></section><section>鏀寔缁勭粐銆佸洟闃熴€侀」鐩殑绠＄悊锛屽苟鎻愪緵鐏垫椿鐨勬潈闄愰厤缃€?/section><div data-type="list" class="markdown-child-root" data-last="true"><div data-type="list-item" data-index="0" data-orderer="false" class="markdown-child-root flex" data-first="true"><div class="_unOrderContainer_1v6rr_18"><div class="_dash_1v6rr_28"></div></div><div class="markdown-child-root"><section data-first="true" data-last="true"><a href="/management-center" title="" data-first="true" data-last="true" data-href="/management-center" data-discover="true">鍥㈤槦涓庨」鐩?/a></section></div></div><div data-type="list-item" data-index="1" data-orderer="false" class="markdown-child-root flex"><div class="_unOrderContainer_1v6rr_18"><div class="_dash_1v6rr_28"></div></div><div class="markdown-child-root"><section data-first="true" data-last="true"><a href="/member-roles-and-permissions" title="" data-first="true" data-last="true" data-href="/member-roles-and-permissions" data-discover="true">鎴愬憳鏉冮檺</a></section></div></div><div data-type="list-item" data-index="2" data-orderer="false" class="markdown-child-root flex" data-last="true"><div class="_unOrderContainer_1v6rr_18"><div class="_dash_1v6rr_28"></div></div><div class="markdown-child-root"><section data-first="true" data-last="true"><a href="/sso" title="" data-first="true" data-last="true" data-href="/sso" data-discover="true">缁勭粐鍜屽崟鐐圭櫥褰?/a></section></div></div></div></span></div></div><div class="_card_o6dzj_12 undefined" data-markdown-component="x-card" data-last="true"><a class="hidden" rel="noreferrer"></a><div class="_cardContainer_o6dzj_61"><div class="_cardTitle_o6dzj_40"></div><span class="_cardContent_o6dzj_49 markdown-child-root"><img src="/raiz5jee8eiph0eeFooV/api/v1/projects/5097254/resources/488432/image-preview?onlineShareType=apidoc&amp;locale=zh-CN" style="background-color:transparent;width:64px" data-first="true" loading="lazy" class=""/><section><section style="font-size:24px;font-weight:600;display:block;margin:20px 0;line-height:1.2" data-first="true" data-last="true">IDEA 鎻掍欢</section></section><section>閫氳繃 IDEA 鎻掍欢鑷姩鐢熸垚 API 鏂囨。骞朵竴閿悓姝ュ埌 Apifox銆?/section><div data-type="list" class="markdown-child-root" data-last="true"><div data-type="list-item" data-index="0" data-orderer="false" class="markdown-child-root flex" data-first="true"><div class="_unOrderContainer_1v6rr_18"><div class="_dash_1v6rr_28"></div></div><div class="markdown-child-root"><section data-first="true" data-last="true"><a href="/apifox-idea-plugin" title="" data-first="true" data-last="true" data-href="/apifox-idea-plugin" data-discover="true">瀹夎 IDEA 鎻掍欢</a></section></div></div><div data-type="list-item" data-index="1" data-orderer="false" class="markdown-child-root flex"><div class="_unOrderContainer_1v6rr_18"><div class="_dash_1v6rr_28"></div></div><div class="markdown-child-root"><section data-first="true" data-last="true"><a href="/generate-api-docs-with-idea" title="" data-first="true" data-last="true" data-href="/generate-api-docs-with-idea" data-discover="true">鐢熸垚鎺ュ彛鏂囨。</a></section></div></div><div data-type="list-item" data-index="2" data-orderer="false" class="markdown-child-root flex" data-last="true"><div class="_unOrderContainer_1v6rr_18"><div class="_dash_1v6rr_28"></div></div><div class="markdown-child-root"><section data-first="true" data-last="true"><a href="/generate-data-schemas-with-idea" title="" data-first="true" data-last="true" data-href="/generate-data-schemas-with-idea" data-discover="true">鐢熸垚鏁版嵁妯″瀷</a></section></div></div></div></span></div></div></div><h2 id="鏇村" class="markdown-doc-viewer-heading">鏇村<a class="anchor" aria-hidden="true" tabindex="-1" href="#鏇村"><span class="icon-link">#</span></a></h2><div class="_cardGroup_o6dzj_70" data-markdown-component="x-card-group" style="grid-template-columns:repeat(3, minmax(0, 1fr))" data-last="true"><div class="_card_o6dzj_12 undefined" data-href="https://apifox.com/siyouhua" data-markdown-component="x-card" data-first="true"><a class="hidden" href="https://apifox.com/siyouhua" rel="noreferrer"></a><div class="_cardContainer_o6dzj_61"><div class="_cardTitle_o6dzj_40"></div><span class="_cardContent_o6dzj_49 markdown-child-root"><img src="/raiz5jee8eiph0eeFooV/api/v1/projects/5097254/resources/488433/image-preview?onlineShareType=apidoc&amp;locale=zh-CN" style="background-color:transparent;width:64px" data-first="true" loading="lazy" class=""/><section data-last="true"><strong data-first="true" data-last="true">Apifox 绉佹湁鍖栭儴缃?/strong></section></span></div></div><div class="_card_o6dzj_12 undefined" data-href="https://apifox-openapi.apifox.cn/" data-markdown-component="x-card"><a class="hidden" href="https://apifox-openapi.apifox.cn/" rel="noreferrer"></a><div class="_cardContainer_o6dzj_61"><div class="_cardTitle_o6dzj_40"></div><span class="_cardContent_o6dzj_49 markdown-child-root"><img src="/raiz5jee8eiph0eeFooV/api/v1/projects/5097254/resources/488434/image-preview?onlineShareType=apidoc&amp;locale=zh-CN" style="background-color:transparent;width:64px;border-radius:14px" data-first="true" loading="lazy" class=""/><section data-last="true"><strong data-first="true" data-last="true">Apifox 寮€鏀?API</strong></section></span></div></div><div class="_card_o6dzj_12 undefined" data-href="https://markdown.apifox.cn/" data-markdown-component="x-card" data-last="true"><a class="hidden" href="https://markdown.apifox.cn/" rel="noreferrer"></a><div class="_cardContainer_o6dzj_61"><div class="_cardTitle_o6dzj_40"></div><span class="_cardContent_o6dzj_49 markdown-child-root"><img src="/raiz5jee8eiph0eeFooV/api/v1/projects/5097254/resources/488435/image-preview?onlineShareType=apidoc&amp;locale=zh-CN" style="background-color:transparent;width:64px" data-first="true" loading="lazy" class=""/><section data-last="true"><strong data-first="true" data-last="true">Apifox Markdown</strong></section></span></div></div></div></div></div><div class="flex gap-4 mb-10"><a class="relative flex items-center gap-4 rounded-xl border border-color-split px-4 py-3 hover:shadow-sm _paginator-item_109co_1 w-0 flex-1" data-discover="true" href="/changelog"><div class="w-0 flex-1"><div class="mb-1 text-sm font-400 text-secondary">涓嬩竴椤?/div><div class="truncate text-[16px] font-500 leading-6 text-color">鏇存柊鏃ュ織</div></div><span role="img" class="appicon app_icon _paginator-item-icon_109co_3 _paginator-item-icon-right_109co_6" style="font-size:24px"><svg viewBox="0 0 1024 1024" width="1em" height="1em" fill="currentColor" aria-hidden="true" focusable="false" role="img"><path d="M567.168 225.834667a42.666667 42.666667 0 0 1 60.330667 0l256 256a42.666667 42.666667 0 0 1 0 60.330666l-256 256a42.666667 42.666667 0 0 1-60.330667-60.330666L750.336 554.666667H170.666667a42.666667 42.666667 0 1 1 0-85.333334h579.669333l-183.168-183.168a42.666667 42.666667 0 0 1 0-60.330666z"></path></svg></span></a></div><div class="@container/footer"><footer class="flex flex-col items-center gap-2 border-t border-t-color-split py-10 @[390px]/footer:flex-row"><div class="flex flex-wrap items-center justify-center text-base font-500 @[390px]/footer:justify-start"><a data-discover="true" href="/llms.txt" target="_blank"><button type="button" class="ui-btn ui-btn-text px-3 text-secondary"><span>LLMs.txt</span></button></a></div><div class="flex-1 flex-shrink-0 text-right max-os:text-right"><div class="inline-flex items-center text-base font-400 text-secondary _footer-logo-wrapper_1kbjg_1"><a class="_footer-logo_1kbjg_1 flex-shrink-0" aria-label="homepage link" href="https://apifox.com"><span class="inline-flex items-center"><span class="mr-[-4px]">Built with</span><svg width="90" height="28" viewBox="0 0 90 28" fill="none" xmlns="http://www.w3.org/2000/svg" class="h-[18px] w-[80px]"><path d="M24.027 18.2458C23.8857 18.2556 23.7426 18.2599 23.6008 18.2599C23.5533 18.2599 23.5058 18.2599 23.4589 18.2599C23.5461 17.9192 23.6071 17.5723 23.6416 17.2222C23.6635 16.9962 23.6751 16.7671 23.6751 16.5349C23.6751 16.2756 23.6607 16.0165 23.6318 15.7588C22.9633 16.0926 22.2448 16.3136 21.5051 16.413C21.2993 16.4412 21.0906 16.4596 20.8792 16.4681C20.9465 16.1314 20.9884 15.7901 21.0046 15.447C21.0101 15.3331 21.0131 15.2185 21.0131 15.1034C21.0136 14.6106 20.9617 14.1192 20.8585 13.6375C20.3351 13.9481 19.7728 14.1868 19.1865 14.3475C18.9746 14.4055 18.7589 14.4535 18.5393 14.4914L18.5338 14.4841C18.5977 14.1626 18.6384 13.8369 18.6556 13.5095C18.6629 13.387 18.666 13.2645 18.666 13.1359C18.666 12.7192 18.6287 12.3035 18.5545 11.8936C17.8325 12.6556 16.9639 13.2619 16.0017 13.6758C15.0395 14.0896 14.0037 14.3023 12.9573 14.3009C11.7217 14.3029 10.5038 14.0058 9.40638 13.4348C9.90572 13.2399 10.3806 12.9869 10.8214 12.6807C9.65074 12.1151 8.66279 11.2281 7.97142 10.1222C7.28005 9.01617 6.91339 7.73615 6.9137 6.42962C6.91348 6.01094 6.95096 5.5931 7.02572 5.18123C7.0991 4.773 7.20936 4.37236 7.35513 3.9843C7.49872 3.60153 7.67621 3.23252 7.88544 2.8817C8.12235 2.48155 8.39897 2.10658 8.71106 1.76257C7.65741 2.24456 6.6705 2.86229 5.77513 3.60023C3.50633 5.46561 1.91046 8.03008 1.23363 10.8982C0.994418 11.915 0.87407 12.9565 0.875017 14.0014C0.871421 16.2511 1.43201 18.4654 2.50493 20.4393C2.57763 19.791 2.74164 19.1564 2.99202 18.5545C3.80039 20.5197 5.0817 22.2519 6.72067 23.5952C8.35964 24.9385 10.3049 25.8508 12.3813 26.25C12.0245 25.7827 11.7289 25.2711 11.5021 24.7278C12.6075 25.0886 13.759 25.2865 14.9208 25.3152C15.0237 25.3152 15.1272 25.3189 15.2307 25.3189C17.0765 25.3218 18.898 24.8967 20.554 24.0767C20.2783 23.9201 20.0138 23.7443 19.7625 23.5505C21.6016 22.8415 23.2288 21.669 24.4879 20.1453C25.0715 19.4399 25.5692 18.6669 25.9705 17.8427C25.3455 18.0713 24.6908 18.2071 24.027 18.2458Z" fill="url(#paint0_linear_4230_4739)"></path><path d="M27.0973 11.8712C27.0304 10.9913 26.8631 10.1221 26.5987 9.28073C26.4935 9.56605 26.3601 9.84002 26.2005 10.0985C25.994 8.72428 25.5149 7.40589 24.7917 6.22171C24.0685 5.03754 23.116 4.01178 21.9908 3.20543C21.2099 2.64353 20.3544 2.19483 19.4494 1.87251C19.3276 1.82882 19.2045 1.78798 19.0798 1.75C19.9331 2.85786 20.4718 4.1778 20.6385 5.56928C20.6446 5.61767 20.6501 5.66605 20.655 5.71506C20.6553 5.71873 20.6553 5.72242 20.655 5.72609C20.6598 5.77632 20.6647 5.82655 20.6684 5.87739V5.90373C20.6726 5.95396 20.6757 6.00418 20.6787 6.05441C20.6818 6.10464 20.6842 6.15548 20.686 6.20571C20.686 6.24124 20.686 6.27738 20.6897 6.31291C20.6894 6.31862 20.6894 6.32435 20.6897 6.33006C20.6897 6.3515 20.6897 6.37356 20.6897 6.395C20.6897 6.43359 20.6897 6.47278 20.6897 6.51138C20.6919 8.10164 20.2081 9.65415 19.3039 10.9585C19.4034 11.432 19.4536 11.9147 19.4537 12.3986C19.4539 12.7251 19.4311 13.0512 19.3855 13.3744C19.8234 13.2702 20.2499 13.1223 20.6586 12.9328C20.9622 12.7931 21.255 12.6309 21.5348 12.4476C21.592 12.6479 21.6407 12.8519 21.6791 13.0602C21.7608 13.4908 21.8016 13.9283 21.8008 14.3668C21.8011 14.7153 21.7752 15.0634 21.7235 15.4081C22.3284 15.345 22.9223 15.2007 23.4892 14.9793C23.7867 14.8633 24.0757 14.7262 24.3538 14.5689C24.3955 14.8032 24.4252 15.0396 24.4427 15.277C24.4557 15.4494 24.4622 15.6233 24.4622 15.7989C24.4625 16.2789 24.4133 16.7576 24.3154 17.2274C25.0278 17.1979 25.7312 17.0565 26.4002 16.8084C26.88 15.5163 27.125 14.1479 27.1235 12.7686C27.1288 12.466 27.1201 12.1669 27.0973 11.8712Z" fill="url(#paint1_linear_4230_4739)"></path><path d="M66.6402 11.4425H68.4465V9.43984H66.6402V8.92178C66.6402 8.36967 66.7621 8.03567 66.9547 7.86075L66.958 7.85765C67.1557 7.66866 67.5286 7.55072 68.1298 7.55072H68.3048V5.5125H68.1298C66.8365 5.5125 65.8477 5.77592 65.1995 6.33711C64.5562 6.89241 64.2536 7.76872 64.2536 8.92178V9.43984H63.1025V11.4425H64.2536V19.5815H66.6402V11.4425Z" fill="currentColor"></path><path fill-rule="evenodd" clip-rule="evenodd" d="M56.7184 17.194L56.7179 17.195C56.3026 17.9917 55.7265 18.6199 54.9902 19.0755C54.265 19.5196 53.4591 19.7415 52.5782 19.7415C51.8107 19.7415 51.1216 19.5885 50.515 19.278L50.5125 19.2767C50.0668 19.0394 49.6798 18.7597 49.3531 18.4372V24.2375H46.9842V9.43984H49.3531V10.5872C49.6671 10.27 50.0489 9.99517 50.4961 9.7618C51.1142 9.43926 51.8096 9.27991 52.5782 9.27991C53.4585 9.27991 54.2632 9.50106 54.988 9.94449C55.725 10.3763 56.3018 10.987 56.7174 11.7721C57.134 12.559 57.3399 13.4586 57.3399 14.4663C57.3399 15.474 57.134 16.3846 56.7184 17.194ZM54.532 12.8086C54.2752 12.3381 53.9326 11.9905 53.5044 11.7591L53.4997 11.7566C53.0778 11.5115 52.6241 11.3892 52.1355 11.3892C51.6597 11.3892 51.2055 11.5109 50.7704 11.757C50.352 12.0002 50.0076 12.3551 49.7383 12.8277C49.4837 13.2948 49.3531 13.8508 49.3531 14.5018C49.3531 15.1523 49.4835 15.7148 49.7387 16.1944C50.008 16.6666 50.3523 17.0213 50.7705 17.2644C51.2055 17.5105 51.6597 17.6322 52.1355 17.6322C52.6239 17.6322 53.0774 17.5101 53.4991 17.2653C53.9295 17.0104 54.2736 16.6442 54.5312 16.161L54.5327 16.1583C54.7994 15.6787 54.9356 15.1165 54.9356 14.4663C54.9356 13.8157 54.7993 13.267 54.5344 12.8129L54.532 12.8086Z" fill="currentColor"></path><path d="M61.4426 9.43984V19.5815H59.0738V9.43984H61.4426Z" fill="currentColor"></path><path fill-rule="evenodd" clip-rule="evenodd" d="M71.6234 19.0974L71.6216 19.0963C70.85 18.6539 70.2431 18.0384 69.8028 17.253L69.8022 17.252C69.361 16.4525 69.1429 15.5341 69.1429 14.5018C69.1429 13.4802 69.3674 12.5672 69.8209 11.7676C70.2733 10.9701 70.8927 10.3542 71.6776 9.92347C72.4611 9.49351 73.3345 9.27991 74.2942 9.27991C75.2538 9.27991 76.1273 9.49351 76.9108 9.92347C77.6956 10.3542 78.315 10.9701 78.7674 11.7676C79.221 12.5672 79.4454 13.4802 79.4454 14.5018C79.4454 15.5242 79.2147 16.4379 78.7487 17.2378C78.2841 18.0352 77.647 18.6562 76.8398 19.098L76.8389 19.0985C76.0442 19.5279 75.1653 19.7415 74.2056 19.7415C73.2574 19.7415 72.3954 19.5277 71.6234 19.0974ZM75.5729 17.2809L75.5746 17.28C76.0035 17.0483 76.3526 16.6997 76.6214 16.2278C76.8865 15.7623 77.0235 15.1895 77.0235 14.5018C77.0235 13.8128 76.8918 13.2457 76.6384 12.7924L76.6375 12.7908C76.3802 12.3192 76.0426 11.9715 75.626 11.7404C75.2037 11.5062 74.7489 11.3892 74.2588 11.3892C73.7689 11.3892 73.3144 11.5061 72.8923 11.74C72.4884 11.9703 72.1628 12.3169 71.9169 12.7879L71.9161 12.7895C71.6733 13.2435 71.5472 13.8116 71.5472 14.5018C71.5472 15.5267 71.8048 16.2961 72.2969 16.8347C72.8055 17.3665 73.4375 17.6322 74.2056 17.6322C74.6958 17.6322 75.1506 17.5152 75.5729 17.2809Z" fill="currentColor"></path><path d="M82.2333 9.43984L84.2399 12.6041L86.1285 9.43984H88.6583L85.3911 14.4497L88.6539 19.5815H85.9545L83.9479 16.4173L82.0593 19.5815H79.5289L82.7966 14.5891L79.5345 9.43984H82.2333Z" fill="currentColor"></path><path d="M60.2759 8.49257C59.8656 8.49257 59.5134 8.35117 59.2311 8.06792C58.9488 7.78469 58.8082 7.43152 58.8082 7.02031C58.8082 6.60909 58.9488 6.25592 59.2311 5.9727C59.5134 5.68944 59.8656 5.54804 60.2759 5.54804C60.6762 5.54804 61.022 5.69064 61.303 5.9727C61.5853 6.25592 61.726 6.60909 61.726 7.02031C61.726 7.43152 61.5853 7.78469 61.303 8.06792C61.022 8.34997 60.6762 8.49257 60.2759 8.49257Z" fill="currentColor"></path><path fill-rule="evenodd" clip-rule="evenodd" d="M36.3214 19.5697L37.2068 17.0462H42.112L42.9975 19.5697H45.4948L40.9605 6.85123H38.3761L33.8417 19.5697H36.3214ZM41.3933 15.0959L39.6378 10.0604L37.8667 15.0959H41.3933Z" fill="currentColor"></path><defs><linearGradient id="paint0_linear_4230_4739" x1="0.875" y1="1.75" x2="25.3168" y2="27.9376" gradientUnits="userSpaceOnUse"><stop offset="0.01" stop-color="#FF4385"></stop><stop offset="0.276042" stop-color="#FF4274"></stop><stop offset="0.723958" stop-color="#FF5C65"></stop><stop offset="1" stop-color="#FF5C35"></stop></linearGradient><linearGradient id="paint1_linear_4230_4739" x1="0.875" y1="1.75" x2="25.3168" y2="27.9376" gradientUnits="userSpaceOnUse"><stop offset="0.01" stop-color="#FF4385"></stop><stop offset="0.276042" stop-color="#FF4274"></stop><stop offset="0.723958" stop-color="#FF5C65"></stop><stop offset="1" stop-color="#FF5C35"></stop></linearGradient></defs></svg></span></a></div></div></footer></div></div><div class="sticky hidden h-[100px] w-[300px] p-5 pt-0 @[960px]/doc-content:block pointer-events-none _anchor_4xe2g_1" style="top:78px;height:calc(100vh - 78px)"></div></div></div></div></div></div></div></div></div></div><script>
            if (typeof window.__updateThemeElement === 'function') {
              window.__updateThemeElement();
            }
          </script><script>((STORAGE_KEY, restoreKey) => {
    if (!window.history.state || !window.history.state.key) {
      let key = Math.random().toString(32).slice(2);
      window.history.replaceState({
        key
      }, "");
    }
    try {
      let positions = JSON.parse(sessionStorage.getItem(STORAGE_KEY) || "{}");
      let storedY = positions[restoreKey || window.history.state.key];
      if (typeof storedY === "number") {
        window.scrollTo(0, storedY);
      }
    } catch (error) {
      console.error(error);
      sessionStorage.removeItem(STORAGE_KEY);
    }
  })("positions", null)</script><link rel="modulepreload" href="https://cdn.apifox.com/docs-site/assets/manifest-cac1e3c4.js"/><link rel="modulepreload" href="https://cdn.apifox.com/docs-site/assets/entry.client-Dxxdbhbz.js"/><link rel="modulepreload" href="https://cdn.apifox.com/docs-site/assets/jsx-runtime-yvcgGv6i.js"/><link rel="modulepreload" href="https://cdn.apifox.com/docs-site/assets/client-CyJ6Fzl9.js"/><link rel="modulepreload" href="https://cdn.apifox.com/docs-site/assets/components-BZdCk-bh.js"/><link rel="modulepreload" href="https://cdn.apifox.com/docs-site/assets/root-Dzc5rLzy.js"/><link rel="modulepreload" href="https://cdn.apifox.com/docs-site/assets/root-CIea2jo0.js"/><link rel="modulepreload" href="https://cdn.apifox.com/docs-site/assets/route-atXlY65o.js"/><script>window.__remixContext = {"basename":"/","future":{"v3_fetcherPersist":true,"v3_relativeSplatPath":true,"v3_throwAbortReason":true,"v3_routeConfig":false,"v3_singleFetch":true,"v3_lazyRouteDiscovery":false,"unstable_optimizeDeps":false},"isSpaMode":false};window.__remixContext.stream = new ReadableStream({start(controller){window.__remixContext.streamController = controller;}}).pipeThrough(new TextEncoderStream());</script><script type="module" async="">import "https://cdn.apifox.com/docs-site/assets/manifest-cac1e3c4.js";
import * as route0 from "https://cdn.apifox.com/docs-site/assets/root-CIea2jo0.js";
import * as route1 from "https://cdn.apifox.com/docs-site/assets/route-atXlY65o.js";

window.__remixRouteModules = {"root":route0,"routes/_index/route":route1};

import("https://cdn.apifox.com/docs-site/assets/entry.client-Dxxdbhbz.js");</script></body></html><!--$?--><template id="B:0"></template><!--/$--><div hidden id="S:0"><script>window.__remixContext.streamController.enqueue("[{\"_1\":2,\"_4070\":-5,\"_4071\":-5},\"loaderData\",{\"_3\":4,\"_4069\":-5},\"root\",{\"_5\":6,\"_27\":28,\"_31\":-7,\"_32\":33,\"_50\":51,\"_132\":133,\"_175\":176,\"_4065\":4066},\"meta\",[7,14,17,22,25],{\"_8\":9,\"_10\":11,\"_12\":13},\"tagName\",\"link\",\"rel\",\"icon\",\"href\",\"https://cdn.apifox.com/app/apidoc-image/custom/20250109/3eb87359-988c-4309-be0e-5e5c212a8922.png\",{\"_8\":9,\"_10\":15,\"_12\":16},\"canonical\",\"https://docs.apifox.com/help\",{\"_18\":19,\"_20\":21},\"name\",\"description\",\"content\",\"甯姪涓績 - Apifox 甯姪鏂囨。\",{\"_18\":23,\"_20\":24},\"keywords\",\"甯姪涓績, Apifox 甯姪鏂囨。, API 鍗忎綔宸ュ叿,鎺ュ彛鏂囨。,API 璋冭瘯,API Mock,API 鑷姩鍖栨祴璇?API 鏂囨。绠＄悊,鎺ュ彛鏂囨。鑷姩鐢熸垚宸ュ叿,鎺ュ彛璋冭瘯\",{\"_26\":21},\"title\",\"i18nState\",{\"_29\":30},\"clientLocale\",\"zh-CN\",\"errorCodeMessage\",\"clientConfig\",{\"_34\":35,\"_36\":37,\"_38\":39,\"_40\":41,\"_46\":47,\"_48\":49},\"apiBaseUrl\",\"https://api.apifox.cn\",\"appWebUrl\",\"https://app.apifox.com\",\"apidocBuiltinPrefixPath\",\"https://cdn.apifox.com/app/static/apidoc\",\"cloudMockBaseUrls\",{\"_42\":43,\"_44\":45},\"pathMode\",\"https://m1.apifoxmock.com/m1/{projectId}-{version}-{service}\",\"idMode\",\"https://m1.apifoxmock.com/m2/{projectId}-{version}-{service}\",\"apidocIsShowLogo\",true,\"webhookGenerateCodeDefaultUrl\",\"https://your-api-server.com\",\"urlConfig\",{\"_52\":53,\"_11\":56,\"_119\":120,\"_128\":129},\"home\",{\"_54\":55},\"index\",\"https://apifox.com\",{\"_57\":58,\"_59\":60,\"_61\":62,\"_63\":64,\"_65\":66,\"_67\":68,\"_69\":70,\"_71\":72,\"_73\":74,\"_75\":76,\"_77\":78,\"_79\":80,\"_81\":82,\"_83\":84,\"_85\":86,\"_87\":88,\"_89\":90,\"_91\":92,\"_93\":94,\"_95\":96,\"_97\":98,\"_99\":100,\"_101\":102,\"_103\":104,\"_105\":106,\"_107\":108,\"_109\":110,\"_111\":112,\"_113\":114,\"_115\":116,\"_117\":118},\"apidoc\",\"https://cdn.apifox.com/app/static/brand/apidoc.png\",\"apifox\",\"https://cdn.apifox.com/app/static/brand/apifox.png\",\"apipost\",\"https://cdn.apifox.com/app/static/brand/apipost.png\",\"apizza\",\"https://cdn.apifox.com/app/static/brand/apizza.png\",\"doclever\",\"https://cdn.apifox.com/app/static/brand/doclever.png\",\"docway\",\"https://cdn.apifox.com/app/static/brand/docway.png\",\"easydoc\",\"https://cdn.apifox.com/app/static/brand/easydoc.png\",\"eolink\",\"https://cdn.apifox.com/app/static/brand/eolink.png\",\"googleDiscovery\",\"https://cdn.apifox.com/app/static/brand/google-discovery.png\",\"har\",\"https://cdn.apifox.com/app/static/brand/har.png\",\"iodocs\",\"https://cdn.apifox.com/app/static/brand/io-doc.png\",\"jmeter\",\"https://cdn.apifox.com/app/static/brand/jmeter.png\",\"knife4j\",\"https://cdn.apifox.com/app/static/brand/knife4j.png\",\"nei\",\"https://cdn.apifox.com/app/static/brand/nei.png\",\"openapi\",\"https://cdn.apifox.com/app/static/brand/openapi.png\",\"postman\",\"https://cdn.apifox.com/app/static/brand/postman.png\",\"raml\",\"https://cdn.apifox.com/app/static/brand/raml.png\",\"rap2\",\"https://cdn.apifox.com/app/static/brand/rap2.png\",\"showdoc\",\"https://cdn.apifox.com/app/static/brand/showdoc.png\",\"sosoapi\",\"https://cdn.apifox.com/app/static/brand/sosoapi.png\",\"wadl\",\"https://cdn.apifox.com/app/static/brand/wadl.png\",\"yapi\",\"https://cdn.apifox.com/app/static/brand/yapi.png\",\"curl\",\"https://cdn.apifox.com/app/static/brand/curl.png\",\"insomnia\",\"https://cdn.apifox.com/app/static/brand/insomnia.png\",\"wsdl\",\"https://cdn.apifox.com/app/static/brand/wsdl.png\",\"markdown\",\"https://cdn.apifox.com/app/static/brand/markdown.png\",\"html\",\"https://cdn.apifox.com/app/static/brand/html.png\",\"protobuf\",\"https://cdn.apifox.com/app/static/brand/protobuf.png\",\"javaProject\",\"https://cdn.apifox.com/app/static/brand/java-project.png\",\"xiaoyaoji\",\"https://cdn.apifox.com/app/static/brand/xiaoyaoji.png\",\"soapui\",\"https://cdn.apifox.com/app/static/brand/soapui.svg\",\"help\",{\"_54\":121,\"_122\":123,\"_124\":125,\"_126\":127},\"https://apifox.com/help\",\"browserExtension\",\"https://apifox.com/help/applications-and-plugins/browser-extensions/chrome\",\"csv\",\"https://apifox.com/help/reference/csv\",\"appMcpServer\",\"https://docs.apifox.com/apifox-mcp-server\",\"assets\",{\"_130\":131},\"logo512Png\",\"https://cdn.apifox.com/logo/apifox-logo-512.png\",\"envConfig\",{\"_134\":57,\"_135\":136,\"_137\":136,\"_138\":139,\"_140\":141,\"_142\":143,\"_144\":55,\"_145\":59,\"_146\":30,\"_147\":148,\"_149\":150,\"_153\":136,\"_154\":155,\"_156\":157,\"_158\":159,\"_160\":161,\"_162\":163,\"_170\":171,\"_172\":47,\"_173\":174},\"RELEASE_BASE\",\"DEBUG_API_BASE\",\"\",\"DEBUG_WEB_URL_BASE\",\"AGENT_SERVER_API_BASE\",\"https://web-proxy.apifox.com\",\"APP_REGION\",\"CN\",\"APP_NAME\",\"Apifox\",\"APP_HOMEPAGE\",\"APP_NAME_LOWER\",\"DEFAULT_LOCALE\",\"APP_BROWSER_EXTENSION_ADAPTER\",\"foxAgentCrossRequest\",\"APP_API_SERVER_HOSTNAMES\",[151,152],\"api.apifox.com\",\"api.apifox.cn\",\"APP_SUPPORT_EMAIL\",\"APP_SCRIPT_MAIN_OBJECTS\",\"$,fox\",\"SERVER_PROTOCOL\",\"http\",\"SERVER_HOST\",\"apifox-api-fordoc-svc\",\"DEFAULT_DOC_LAYOUT\",\"OneColumn\",\"NOT_CUSTOM_DOMAIN_HOSTNAMES\",[164,165,166,167,168,169],\"www.apifox.com\",\"apifox.com\",\"www.apifox.cn\",\"apifox.cn\",\"share.apifox.cn\",\"s.apifox.cn\",\"SERVER_REQUEST_TIMEOUT\",100000,\"IS_SHARED_DOC_INDEPENDENT_DOMAIN\",\"APIDOC_CUSTOM_DOMAIN_CNAME_REGEX\",[\"R\",\"\\\\d+\\\\.n[12x]\\\\.apifox\\\\.cn\",\"\"],\"docsDataState\",{\"_177\":178,\"_254\":255,\"_2475\":2476,\"_2547\":2548,\"_2552\":2553,\"_2586\":2587,\"_2600\":2601,\"_2602\":2603,\"_2604\":2605,\"_2637\":2638,\"_2647\":2648,\"_2667\":2668,\"_2676\":2677,\"_4054\":4055,\"_4059\":4060,\"_4062\":4063},\"navigation\",{\"_179\":180,\"_181\":182,\"_249\":186,\"_250\":251,\"_252\":253},\"type\",\"FIRST_LEVEL_NAVIGATION\",\"firstLevelNavbarItems\",[183,193,197,202,206,229],{\"_179\":184,\"_185\":186,\"_26\":187,\"_11\":-7,\"_188\":47,\"_189\":190,\"_191\":192},\"folder\",\"id\",\"01JP9QXA2N6HXSQ5JTPZYM9976\",\"甯姪鏂囨。\",\"selected\",\"to\",\"/help\",\"disabled\",false,{\"_179\":184,\"_185\":194,\"_26\":195,\"_11\":-7,\"_188\":192,\"_189\":196,\"_191\":192},\"01JHMQ9DFDQ3G5JYCPTZE4EH3J\",\"甯歌闂\",\"/5835927m0?nav=01JHMQ9DFDQ3G5JYCPTZE4EH3J\",{\"_179\":9,\"_185\":198,\"_26\":199,\"_11\":-7,\"_200\":201,\"_189\":55,\"_191\":192},\"01JH00TEGQ0YJS8WBTQFVK260T\",\"Apifox 瀹樼綉\",\"target\",\"blank\",{\"_179\":9,\"_185\":203,\"_26\":204,\"_11\":-7,\"_200\":201,\"_189\":205,\"_191\":192},\"01JH4YCVGR0JH4K142TENXTMG5\",\"绉佹湁鍖栭儴缃瞈",\"https://apifox.com/siyouhua\",{\"_179\":207,\"_185\":208,\"_26\":209,\"_191\":192,\"_11\":-7,\"_210\":211,\"_188\":192},\"menu\",\"01JNQ66KAAZD1FZKM4GY69T65W\",\"寮€鍙戣€呬腑蹇僜",\"children\",[212,216,221,225],{\"_179\":9,\"_185\":213,\"_26\":214,\"_11\":-7,\"_200\":201,\"_189\":215,\"_191\":192},\"01JNQ6789RE1EXBME6N5ZT6CJ8\",\"寮€鏀?API\",\"https://apifox-openapi.apifox.cn/\",{\"_179\":9,\"_185\":217,\"_26\":218,\"_11\":-7,\"_200\":219,\"_189\":220,\"_191\":192},\"01JP9QTE4W8GBAJDTQTBAH74YP\",\"鏇存柊鏃ュ織\",\"self\",\"https://docs.apifox.com/changelog\",{\"_179\":9,\"_185\":222,\"_26\":223,\"_11\":-7,\"_200\":201,\"_189\":224,\"_191\":192},\"01JP9QTZRNAEVH4DN8AXEAYKT5\",\"Road Map\",\"https://apifox666.feishu.cn/base/bascnA8ZHro9a3k73zpI93f5DKf?table=tblRLUMAyHhyQ4XQ\u0026view=vewUvyaZ4L\",{\"_179\":9,\"_185\":226,\"_26\":227,\"_11\":-7,\"_200\":201,\"_189\":228,\"_191\":192},\"01JNQ68BDSKPAMHMEQV5PJCPNM\",\"Apifox Markdown\",\"https://markdown.apifox.cn/\",{\"_179\":207,\"_185\":230,\"_26\":231,\"_191\":192,\"_11\":-7,\"_210\":232,\"_188\":192},\"01JH2ENEJSPS2WWRG7Z3G8EQY8\",\"涓嬭浇\",[233,237,241,245],{\"_179\":9,\"_185\":234,\"_26\":235,\"_11\":-7,\"_200\":219,\"_189\":236,\"_191\":192},\"01JH2EPNMSBTYFMS296PJE79Z4\",\"涓嬭浇 Apifox\",\"https://docs.apifox.com/download\",{\"_179\":9,\"_185\":238,\"_26\":239,\"_11\":-7,\"_200\":219,\"_189\":240,\"_191\":192},\"01JH2EQMCB5QM5BEC1R42AB4B4\",\"涓嬭浇 IDEA 鎻掍欢\",\"https://docs.apifox.com/5743620m0\",{\"_179\":9,\"_185\":242,\"_26\":243,\"_11\":-7,\"_200\":219,\"_189\":244,\"_191\":192},\"01JH2ERWZBDHZBJQ5G1953FPHP\",\"涓嬭浇娴忚鍣ㄦ墿灞昞",\"https://docs.apifox.com/5807583m0\",{\"_179\":9,\"_185\":246,\"_26\":247,\"_11\":-7,\"_200\":201,\"_189\":248,\"_191\":192},\"01JNQ6C5NNCW5SA7A6YHDCEAVK\",\"Apifox Web 鐗圽",\"https://app.apifox.com/\",\"navId\",\"align\",\"right\",\"navRightLinkItems\",[],\"sidebarTree\",{\"_256\":-7,\"_257\":-7,\"_258\":259,\"_260\":261,\"_2468\":2469,\"_2470\":2471,\"_2472\":-7,\"_2473\":2474},\"goBackSidebarTreeApiFolderNode\",\"rootSidebarTreeApiFolderNode\",\"homeLink\",\"/\",\"sidebarTreeList\",[262,273,278,310,696,886,1245,1292,1485,1617,1658,1883,1894,1981,2001,2042,2072,2143,2203,2286,2310,2462],{\"_179\":263,\"_185\":264,\"_18\":265,\"_266\":267,\"_268\":269,\"_270\":-7,\"_271\":272,\"_11\":-7,\"_189\":190,\"_188\":47},\"doc\",5802858,\"甯姪涓績\",\"key\",\"doc.5802858\",\"parentId\",0,\"resourceName\",\"tags\",[],{\"_179\":263,\"_185\":274,\"_18\":218,\"_266\":275,\"_268\":269,\"_270\":-7,\"_271\":276,\"_11\":-7,\"_189\":277,\"_188\":192},5807637,\"doc.5807637\",[],\"/changelog\",{\"_179\":279,\"_280\":269,\"_185\":281,\"_18\":282,\"_266\":283,\"_284\":285,\"_210\":290,\"_268\":269,\"_270\":-7,\"_19\":-7,\"_308\":279,\"_11\":-7,\"_189\":136,\"_188\":192,\"_309\":47},\"apiDetailFolder\",\"docId\",41143239,\"鍏ラ棬\",\"apiDetailFolder.41143239\",\"shareSettings\",{\"_286\":287,\"_288\":289},\"directoryTreeDisplayStyle\",\"GENERAL_FOLDER\",\"folderContentsDisplayingSettings\",\"HIDE\",[291,297,303],{\"_179\":263,\"_185\":292,\"_18\":293,\"_266\":294,\"_268\":281,\"_270\":-7,\"_271\":295,\"_11\":-7,\"_189\":296,\"_188\":192},5092525,\"浜у搧浠嬬粛\",\"doc.5092525\",[],\"/introduction\",{\"_179\":263,\"_185\":298,\"_18\":299,\"_266\":300,\"_268\":281,\"_270\":-7,\"_271\":301,\"_11\":-7,\"_189\":302,\"_188\":192},5751209,\"鑱旂郴鎴戜滑\",\"doc.5751209\",[],\"/contact-us\",{\"_179\":263,\"_185\":304,\"_18\":204,\"_266\":305,\"_268\":281,\"_270\":-7,\"_271\":306,\"_11\":-7,\"_189\":307,\"_188\":192},5751301,\"doc.5751301\",[],\"/siyouhua\",\"folderType\",\"expanded\",{\"_179\":279,\"_280\":269,\"_185\":311,\"_18\":312,\"_266\":313,\"_284\":314,\"_210\":315,\"_268\":269,\"_270\":-7,\"_19\":-7,\"_308\":279,\"_11\":-7,\"_189\":136,\"_188\":192,\"_309\":47},41719280,\"寮€濮嬩娇鐢╘",\"apiDetailFolder.41719280\",{\"_286\":287,\"_288\":289},[316,321,327,333,339,394,571],{\"_179\":263,\"_185\":317,\"_18\":235,\"_266\":318,\"_268\":311,\"_270\":-7,\"_271\":319,\"_11\":-7,\"_189\":320,\"_188\":192},5135961,\"doc.5135961\",[],\"/download\",{\"_179\":263,\"_185\":322,\"_18\":323,\"_266\":324,\"_268\":311,\"_270\":-7,\"_271\":325,\"_11\":-7,\"_189\":326,\"_188\":192},5136040,\"娉ㄥ唽涓庣櫥褰昞",\"doc.5136040\",[],\"/sign-up-and-login\",{\"_179\":263,\"_185\":328,\"_18\":329,\"_266\":330,\"_268\":311,\"_270\":-7,\"_271\":331,\"_11\":-7,\"_189\":332,\"_188\":192},5146285,\"椤甸潰甯冨眬\",\"doc.5146285\",[],\"/page-layout\",{\"_179\":263,\"_185\":334,\"_18\":335,\"_266\":336,\"_268\":311,\"_270\":-7,\"_271\":337,\"_11\":-7,\"_189\":338,\"_188\":192},5146286,\"鍩烘湰姒傚康\",\"doc.5146286\",[],\"/apifox-basic-concepts\",{\"_179\":279,\"_280\":269,\"_185\":340,\"_18\":341,\"_266\":342,\"_284\":343,\"_210\":344,\"_268\":311,\"_270\":-7,\"_19\":-7,\"_308\":279,\"_11\":-7,\"_189\":136,\"_188\":192,\"_309\":192},41719368,\"蹇€熶笂鎵媆",\"apiDetailFolder.41719368\",{\"_286\":287,\"_288\":289},[345,351,357,363,370,376,382,388],{\"_179\":263,\"_185\":346,\"_18\":347,\"_266\":348,\"_268\":340,\"_270\":341,\"_271\":349,\"_11\":-7,\"_189\":350,\"_188\":192},5135833,\"姒傝堪\",\"doc.5135833\",[],\"/getting-started\",{\"_179\":263,\"_185\":352,\"_18\":353,\"_266\":354,\"_268\":340,\"_270\":-7,\"_271\":355,\"_11\":-7,\"_189\":356,\"_188\":192},5139642,\"鏂板缓鎺ュ彛\",\"doc.5139642\",[],\"/create-api\",{\"_179\":263,\"_185\":358,\"_18\":359,\"_266\":360,\"_268\":340,\"_270\":-7,\"_271\":361,\"_11\":-7,\"_189\":362,\"_188\":192},5140339,\"鍙戦€佹帴鍙ｈ姹俓",\"doc.5140339\",[],\"/send-api-request\",{\"_179\":263,\"_185\":364,\"_18\":365,\"_266\":366,\"_268\":340,\"_270\":367,\"_271\":368,\"_11\":-7,\"_189\":369,\"_188\":192},5218722,\"蹇嵎璇锋眰\",\"doc.5218722\",\"鍙戦€佸揩鎹疯姹傚苟淇濆瓨涓烘帴鍙",[],\"/quick-request\",{\"_179\":263,\"_185\":371,\"_18\":372,\"_266\":373,\"_268\":340,\"_270\":-7,\"_271\":374,\"_11\":-7,\"_189\":375,\"_188\":192},5140346,\"娣诲姞鏂█\",\"doc.5140346\",[],\"/add-assertions\",{\"_179\":263,\"_185\":377,\"_18\":378,\"_266\":379,\"_268\":340,\"_270\":-7,\"_271\":380,\"_11\":-7,\"_189\":381,\"_188\":192},5140350,\"鏂板缓娴嬭瘯鍦烘櫙\",\"doc.5140350\",[],\"/create-test-scenario\",{\"_179\":263,\"_185\":383,\"_18\":384,\"_266\":385,\"_268\":340,\"_270\":-7,\"_271\":386,\"_11\":-7,\"_189\":387,\"_188\":192},5140353,\"鍒嗕韩 API 鏂囨。\",\"doc.5140353\",[],\"/share-api-documentation\",{\"_179\":263,\"_185\":389,\"_18\":390,\"_266\":391,\"_268\":340,\"_270\":-7,\"_271\":392,\"_11\":-7,\"_189\":393,\"_188\":192},5140360,\"浜嗚В鏇村\",\"doc.5140360\",[],\"/learn-more\",{\"_179\":279,\"_280\":269,\"_185\":395,\"_18\":396,\"_266\":397,\"_284\":398,\"_210\":399,\"_268\":311,\"_270\":-7,\"_19\":-7,\"_308\":279,\"_11\":-7,\"_189\":136,\"_188\":192,\"_309\":192},41719384,\"鍩虹鐭ヨ瘑\",\"apiDetailFolder.41719384\",{\"_286\":287,\"_288\":289},[400,436,501,536,560],{\"_179\":279,\"_280\":269,\"_185\":401,\"_18\":402,\"_266\":403,\"_284\":404,\"_210\":405,\"_268\":395,\"_270\":-7,\"_19\":-7,\"_308\":279,\"_11\":-7,\"_189\":136,\"_188\":192,\"_309\":192},42648988,\"鎺ュ彛鍩烘湰淇℃伅\",\"apiDetailFolder.42648988\",{\"_286\":287,\"_288\":289},[406,412,418,424,430],{\"_179\":263,\"_185\":407,\"_18\":408,\"_266\":409,\"_268\":401,\"_270\":-7,\"_271\":410,\"_11\":-7,\"_189\":411,\"_188\":192},5220199,\"璇锋眰 URL 涓庢柟娉昞",\"doc.5220199\",[],\"/request-url-and-method\",{\"_179\":263,\"_185\":413,\"_18\":414,\"_266\":415,\"_268\":401,\"_270\":-7,\"_271\":416,\"_11\":-7,\"_189\":417,\"_188\":192},5220200,\"璇锋眰鍙傛暟涓庤姹備綋\",\"doc.5220200\",[],\"/request-params-and-body\",{\"_179\":263,\"_185\":419,\"_18\":420,\"_266\":421,\"_268\":401,\"_270\":-7,\"_271\":422,\"_11\":-7,\"_189\":423,\"_188\":192},5220201,\"璇锋眰澶碶",\"doc.5220201\",[],\"/request-headers\",{\"_179\":263,\"_185\":425,\"_18\":426,\"_266\":427,\"_268\":401,\"_270\":-7,\"_271\":428,\"_11\":-7,\"_189\":429,\"_188\":192},5220202,\"璇锋眰鍙傛暟缂栫爜瑙ｇ爜\",\"doc.5220202\",[],\"/request-param-encoding-decoding\",{\"_179\":263,\"_185\":431,\"_18\":432,\"_266\":433,\"_268\":401,\"_270\":-7,\"_271\":434,\"_11\":-7,\"_189\":435,\"_188\":192},5220203,\"HTTP/2\",\"doc.5220203\",[],\"/http2\",{\"_179\":279,\"_280\":269,\"_185\":437,\"_18\":438,\"_266\":439,\"_284\":440,\"_210\":441,\"_268\":395,\"_270\":-7,\"_19\":-7,\"_308\":279,\"_11\":-7,\"_189\":136,\"_188\":192,\"_309\":192},42649068,\"璁よ瘉涓庢巿鏉僜",\"apiDetailFolder.42649068\",{\"_286\":287,\"_288\":289},[442,447,453,459,465,471,477,483,489,495],{\"_179\":263,\"_185\":443,\"_18\":347,\"_266\":444,\"_268\":437,\"_270\":438,\"_271\":445,\"_11\":-7,\"_189\":446,\"_188\":192},5220207,\"doc.5220207\",[],\"/authentication-and-authorization\",{\"_179\":263,\"_185\":448,\"_18\":449,\"_266\":450,\"_268\":437,\"_270\":-7,\"_271\":451,\"_11\":-7,\"_189\":452,\"_188\":192},5220209,\"鏀寔鐨勬巿鏉冪被鍨媆",\"doc.5220209\",[],\"/authorization-types\",{\"_179\":263,\"_185\":454,\"_18\":455,\"_266\":456,\"_268\":437,\"_270\":-7,\"_271\":457,\"_11\":-7,\"_189\":458,\"_188\":192},5352149,\"Digest Auth\",\"doc.5352149\",[],\"/digest-auth\",{\"_179\":263,\"_185\":460,\"_18\":461,\"_266\":462,\"_268\":437,\"_270\":-7,\"_271\":463,\"_11\":-7,\"_189\":464,\"_188\":192},5352166,\"OAuth 1.0\",\"doc.5352166\",[],\"/oauth1\",{\"_179\":263,\"_185\":466,\"_18\":467,\"_266\":468,\"_268\":437,\"_270\":-7,\"_271\":469,\"_11\":-7,\"_189\":470,\"_188\":192},5352170,\"OAuth 2.0\",\"doc.5352170\",[],\"/oauth2\",{\"_179\":263,\"_185\":472,\"_18\":473,\"_266\":474,\"_268\":437,\"_270\":-7,\"_271\":475,\"_11\":-7,\"_189\":476,\"_188\":192},5352172,\"Hawk Authentication\",\"doc.5352172\",[],\"/hawk-auth\",{\"_179\":263,\"_185\":478,\"_18\":479,\"_266\":480,\"_268\":437,\"_270\":-7,\"_271\":481,\"_11\":-7,\"_189\":482,\"_188\":192},5352174,\"Kerberos\",\"doc.5352174\",[],\"/kerberos\",{\"_179\":263,\"_185\":484,\"_18\":485,\"_266\":486,\"_268\":437,\"_270\":-7,\"_271\":487,\"_11\":-7,\"_189\":488,\"_188\":192},5352176,\"NTLM\",\"doc.5352176\",[],\"/ntlm\",{\"_179\":263,\"_185\":490,\"_18\":491,\"_266\":492,\"_268\":437,\"_270\":-7,\"_271\":493,\"_11\":-7,\"_189\":494,\"_188\":192},5352177,\"Akamai EdgeGrid\",\"doc.5352177\",[],\"/akamai-edgegrid\",{\"_179\":263,\"_185\":496,\"_18\":497,\"_266\":498,\"_268\":437,\"_270\":-7,\"_271\":499,\"_11\":-7,\"_189\":500,\"_188\":192},5220208,\"CA 鍜屽鎴风璇佷功\",\"doc.5220208\",[],\"/ca-and-client-certificates\",{\"_179\":279,\"_280\":269,\"_185\":502,\"_18\":503,\"_266\":504,\"_284\":505,\"_210\":506,\"_268\":395,\"_270\":-7,\"_19\":-7,\"_308\":279,\"_11\":-7,\"_189\":136,\"_188\":192,\"_309\":192},42649128,\"鍝嶅簲涓?Cookie\",\"apiDetailFolder.42649128\",{\"_286\":287,\"_288\":289},[507,512,518,524,530],{\"_179\":263,\"_185\":508,\"_18\":347,\"_266\":509,\"_268\":502,\"_270\":503,\"_271\":510,\"_11\":-7,\"_189\":511,\"_188\":192},5220211,\"doc.5220211\",[],\"/response-and-cookie\",{\"_179\":263,\"_185\":513,\"_18\":514,\"_266\":515,\"_268\":502,\"_270\":-7,\"_271\":516,\"_11\":-7,\"_189\":517,\"_188\":192},5220212,\"API 鍝嶅簲\",\"doc.5220212\",[],\"/api-response\",{\"_179\":263,\"_185\":519,\"_18\":520,\"_266\":521,\"_268\":502,\"_270\":-7,\"_271\":522,\"_11\":-7,\"_189\":523,\"_188\":192},5220214,\"鍒涘缓鍜屽彂閫?Cookie\",\"doc.5220214\",[],\"/create-and-send-cookie\",{\"_179\":263,\"_185\":525,\"_18\":526,\"_266\":527,\"_268\":502,\"_270\":-7,\"_271\":528,\"_11\":-7,\"_189\":529,\"_188\":192},5220215,\"瀹為檯璇锋眰\",\"doc.5220215\",[],\"/actual-request\",{\"_179\":263,\"_185\":531,\"_18\":532,\"_266\":533,\"_268\":502,\"_270\":-7,\"_271\":534,\"_11\":-7,\"_189\":535,\"_188\":192},5220219,\"鎻愬彇鍝嶅簲绀轰緥\",\"doc.5220219\",[],\"/extract-response-example\",{\"_179\":279,\"_280\":269,\"_185\":537,\"_18\":538,\"_266\":539,\"_284\":540,\"_210\":541,\"_268\":395,\"_270\":-7,\"_19\":-7,\"_308\":279,\"_11\":-7,\"_189\":136,\"_188\":192,\"_309\":192},49663097,\"璇锋眰浠ｇ悊\",\"apiDetailFolder.49663097\",{\"_286\":287,\"_288\":289},[542,548,554],{\"_179\":263,\"_185\":543,\"_18\":544,\"_266\":545,\"_268\":537,\"_270\":-7,\"_271\":546,\"_11\":-7,\"_189\":547,\"_188\":192},5146289,\"缃戦〉绔腑鐨勮姹備唬鐞哱",\"doc.5146289\",[],\"/request-proxy-in-web\",{\"_179\":263,\"_185\":549,\"_18\":550,\"_266\":551,\"_268\":537,\"_270\":-7,\"_271\":552,\"_11\":-7,\"_189\":553,\"_188\":192},5903157,\"鍒嗕韩鏂囨。涓殑璇锋眰浠ｇ悊\",\"doc.5903157\",[],\"/share-document-proxy\",{\"_179\":263,\"_185\":555,\"_18\":556,\"_266\":557,\"_268\":537,\"_270\":-7,\"_271\":558,\"_11\":-7,\"_189\":559,\"_188\":192},5903163,\"瀹㈡埛绔腑鐨勮姹備唬鐞哱",\"doc.5903163\",[],\"/client-side-request-proxy\",{\"_179\":279,\"_280\":269,\"_185\":561,\"_18\":562,\"_266\":563,\"_284\":564,\"_210\":565,\"_268\":395,\"_270\":-7,\"_19\":-7,\"_308\":279,\"_11\":-7,\"_189\":136,\"_188\":192,\"_309\":192},49650935,\"API Hub\",\"apiDetailFolder.49650935\",{\"_286\":287,\"_288\":289},[566],{\"_179\":263,\"_185\":567,\"_18\":562,\"_266\":568,\"_268\":561,\"_270\":-7,\"_271\":569,\"_11\":-7,\"_189\":570,\"_188\":192},5146296,\"doc.5146296\",[],\"/apihub\",{\"_179\":279,\"_280\":269,\"_185\":572,\"_18\":573,\"_266\":574,\"_284\":575,\"_210\":576,\"_268\":311,\"_270\":-7,\"_19\":-7,\"_308\":279,\"_11\":-7,\"_189\":136,\"_188\":192,\"_309\":192},41719445,\"瀵煎叆瀵煎嚭鏁版嵁\",\"apiDetailFolder.41719445\",{\"_286\":287,\"_288\":289},[577,582,588,594,600,606],{\"_179\":263,\"_185\":578,\"_18\":347,\"_266\":579,\"_268\":572,\"_270\":573,\"_271\":580,\"_11\":-7,\"_189\":581,\"_188\":192},5173624,\"doc.5173624\",[],\"/import-and-export\",{\"_179\":263,\"_185\":583,\"_18\":584,\"_266\":585,\"_268\":572,\"_270\":-7,\"_271\":586,\"_11\":-7,\"_189\":587,\"_188\":192},5173626,\"鎵嬪姩瀵煎叆\",\"doc.5173626\",[],\"/manual-import\",{\"_179\":263,\"_185\":589,\"_18\":590,\"_266\":591,\"_268\":572,\"_270\":-7,\"_271\":592,\"_11\":-7,\"_189\":593,\"_188\":192},5173630,\"瀹氭椂瀵煎叆\",\"doc.5173630\",[],\"/scheduled-import\",{\"_179\":263,\"_185\":595,\"_18\":596,\"_266\":597,\"_268\":572,\"_270\":-7,\"_271\":598,\"_11\":-7,\"_189\":599,\"_188\":192},5173637,\"瀵煎叆璁剧疆\",\"doc.5173637\",[],\"/import-settings\",{\"_179\":263,\"_185\":601,\"_18\":602,\"_266\":603,\"_268\":572,\"_270\":-7,\"_271\":604,\"_11\":-7,\"_189\":605,\"_188\":192},5173639,\"瀵煎嚭鏁版嵁\",\"doc.5173639\",[],\"/export-data\",{\"_179\":279,\"_280\":269,\"_185\":607,\"_18\":608,\"_266\":609,\"_284\":610,\"_210\":611,\"_268\":572,\"_270\":-7,\"_19\":-7,\"_308\":279,\"_11\":-7,\"_189\":136,\"_188\":192,\"_309\":192},42137993,\"鍏跺畠鏂瑰紡瀵煎叆\",\"apiDetailFolder.42137993\",{\"_286\":287,\"_288\":289},[612,618,624,630,636,642,648,654,660,666,672,678,684,690],{\"_179\":263,\"_185\":613,\"_18\":614,\"_266\":615,\"_268\":607,\"_270\":-7,\"_271\":616,\"_11\":-7,\"_189\":617,\"_188\":192},5174534,\"瀵煎叆 OpenAPI/Swagger\",\"doc.5174534\",[],\"/import-openapi-swagger\",{\"_179\":263,\"_185\":619,\"_18\":620,\"_266\":621,\"_268\":607,\"_270\":-7,\"_271\":622,\"_11\":-7,\"_189\":623,\"_188\":192},5174528,\"瀵煎叆 Postman\",\"doc.5174528\",[],\"/import-postman\",{\"_179\":263,\"_185\":625,\"_18\":626,\"_266\":627,\"_268\":607,\"_270\":-7,\"_271\":628,\"_11\":-7,\"_189\":629,\"_188\":192},5174546,\"瀵煎叆 Apipost\",\"doc.5174546\",[],\"/import-apipost\",{\"_179\":263,\"_185\":631,\"_18\":632,\"_266\":633,\"_268\":607,\"_270\":-7,\"_271\":634,\"_11\":-7,\"_189\":635,\"_188\":192},5174555,\"瀵煎叆 Eolink\",\"doc.5174555\",[],\"/import-eolink\",{\"_179\":263,\"_185\":637,\"_18\":638,\"_266\":639,\"_268\":607,\"_270\":-7,\"_271\":640,\"_11\":-7,\"_189\":641,\"_188\":192},5174539,\"瀵煎叆 cURL\",\"doc.5174539\",[],\"/import-curl\",{\"_179\":263,\"_185\":643,\"_18\":644,\"_266\":645,\"_268\":607,\"_270\":-7,\"_271\":646,\"_11\":-7,\"_189\":647,\"_188\":192},5174540,\"瀵煎叆 Markdown\",\"doc.5174540\",[],\"/import-markdown\",{\"_179\":263,\"_185\":649,\"_18\":650,\"_266\":651,\"_268\":607,\"_270\":-7,\"_271\":652,\"_11\":-7,\"_189\":653,\"_188\":192},5174542,\"瀵煎叆 Insomnia\",\"doc.5174542\",[],\"/import-insomnia\",{\"_179\":263,\"_185\":655,\"_18\":656,\"_266\":657,\"_268\":607,\"_270\":-7,\"_271\":658,\"_11\":-7,\"_189\":659,\"_188\":192},5174544,\"瀵煎叆 apiDoc\",\"doc.5174544\",[],\"/import-apidoc\",{\"_179\":263,\"_185\":661,\"_18\":662,\"_266\":663,\"_268\":607,\"_270\":-7,\"_271\":664,\"_11\":-7,\"_189\":665,\"_188\":192},5174545,\"瀵煎叆 .har 鏂囦欢\",\"doc.5174545\",[],\"/import-har\",{\"_179\":263,\"_185\":667,\"_18\":668,\"_266\":669,\"_268\":607,\"_270\":-7,\"_271\":670,\"_11\":-7,\"_189\":671,\"_188\":192},5811273,\"瀵煎叆 knife4j\",\"doc.5811273\",[],\"/import-knife4j\",{\"_179\":263,\"_185\":673,\"_18\":674,\"_266\":675,\"_268\":607,\"_270\":-7,\"_271\":676,\"_11\":-7,\"_189\":677,\"_188\":192},5811290,\"瀵煎叆 NEI\",\"doc.5811290\",[],\"/import-nei\",{\"_179\":263,\"_185\":679,\"_18\":680,\"_266\":681,\"_268\":607,\"_270\":-7,\"_271\":682,\"_11\":-7,\"_189\":683,\"_188\":192},5811306,\"瀵煎叆灏忓购楦★紙docway锛塡",\"doc.5811306\",[],\"/import-docway\",{\"_179\":263,\"_185\":685,\"_18\":686,\"_266\":687,\"_268\":607,\"_270\":-7,\"_271\":688,\"_11\":-7,\"_189\":689,\"_188\":192},5811315,\"瀵煎叆 Apizza\",\"doc.5811315\",[],\"/import-apizza\",{\"_179\":263,\"_185\":691,\"_18\":692,\"_266\":693,\"_268\":607,\"_270\":-7,\"_271\":694,\"_11\":-7,\"_189\":695,\"_188\":192},5811330,\"瀵煎叆 WSDL \",\"doc.5811330\",[],\"/import-wsdl\",{\"_179\":279,\"_280\":269,\"_185\":697,\"_18\":698,\"_266\":699,\"_284\":700,\"_210\":701,\"_268\":269,\"_270\":-7,\"_19\":-7,\"_308\":279,\"_11\":-7,\"_189\":136,\"_188\":192,\"_309\":47},42649309,\"璁捐 API\",\"apiDetailFolder.42649309\",{\"_286\":287,\"_288\":289},[702,707,713,719,725,731,737,743,749,755,761,767,773,821,850],{\"_179\":263,\"_185\":703,\"_18\":347,\"_266\":704,\"_268\":697,\"_270\":698,\"_271\":705,\"_11\":-7,\"_189\":706,\"_188\":192},5220221,\"doc.5220221\",[],\"/design-api\",{\"_179\":263,\"_185\":708,\"_18\":709,\"_266\":710,\"_268\":697,\"_270\":-7,\"_271\":711,\"_11\":-7,\"_189\":712,\"_188\":192},5220222,\"鏂板缓 API 椤圭洰\",\"doc.5220222\",[],\"/create-api-project\",{\"_179\":263,\"_185\":714,\"_18\":715,\"_266\":716,\"_268\":697,\"_270\":353,\"_271\":717,\"_11\":-7,\"_189\":718,\"_188\":192},5220223,\"鎺ュ彛鍩虹鐭ヨ瘑\",\"doc.5220223\",[],\"/create-an-api\",{\"_179\":263,\"_185\":720,\"_18\":721,\"_266\":722,\"_268\":697,\"_270\":-7,\"_271\":723,\"_11\":-7,\"_189\":724,\"_188\":192},7151314,\"鎺ュ彛璁捐瑙勮寖\",\"doc.7151314\",[],\"/api-design-guidelines\",{\"_179\":263,\"_185\":726,\"_18\":727,\"_266\":728,\"_268\":697,\"_270\":-7,\"_271\":729,\"_11\":-7,\"_189\":730,\"_188\":192},6970194,\"妯″潡\",\"doc.6970194\",[],\"/module\",{\"_179\":263,\"_185\":732,\"_18\":733,\"_266\":734,\"_268\":697,\"_270\":-7,\"_271\":735,\"_11\":-7,\"_189\":736,\"_188\":192},6121960,\"璇锋眰浣撳绀轰緥閰嶇疆\",\"doc.6121960\",[],\"/multi-example-request-body\",{\"_179\":263,\"_185\":738,\"_18\":739,\"_266\":740,\"_268\":697,\"_270\":-7,\"_271\":741,\"_11\":-7,\"_189\":742,\"_188\":192},5220225,\"鍝嶅簲缁勪欢\",\"doc.5220225\",[],\"/response-components\",{\"_179\":263,\"_185\":744,\"_18\":745,\"_266\":746,\"_268\":697,\"_270\":-7,\"_271\":747,\"_11\":-7,\"_189\":748,\"_188\":192},5220226,\"甯哥敤瀛楁\",\"doc.5220226\",[],\"/common-fields\",{\"_179\":263,\"_185\":750,\"_18\":751,\"_266\":752,\"_268\":697,\"_270\":-7,\"_271\":753,\"_11\":-7,\"_189\":754,\"_188\":192},5802626,\"鍏ㄥ眬鍙傛暟\",\"doc.5802626\",[],\"/global-parameters\",{\"_179\":263,\"_185\":756,\"_18\":757,\"_266\":758,\"_268\":697,\"_270\":-7,\"_271\":759,\"_11\":-7,\"_189\":760,\"_188\":192},5220227,\"鍘嗗彶璁板綍\",\"doc.5220227\",[],\"/api-history\",{\"_179\":263,\"_185\":762,\"_18\":763,\"_266\":764,\"_268\":697,\"_270\":-7,\"_271\":765,\"_11\":-7,\"_189\":766,\"_188\":192},7380975,\"鎺ュ彛璇勮\",\"doc.7380975\",[],\"/api-comments\",{\"_179\":263,\"_185\":768,\"_18\":769,\"_266\":770,\"_268\":697,\"_270\":-7,\"_271\":771,\"_11\":-7,\"_189\":772,\"_188\":192},5220228,\"鎵归噺绠＄悊\",\"doc.5220228\",[],\"/bulk-operations\",{\"_179\":279,\"_280\":269,\"_185\":774,\"_18\":775,\"_266\":776,\"_284\":777,\"_210\":778,\"_268\":697,\"_270\":-7,\"_19\":-7,\"_308\":279,\"_11\":-7,\"_189\":136,\"_188\":192,\"_309\":192},42649413,\"鏁版嵁妯″瀷\",\"apiDetailFolder.42649413\",{\"_286\":287,\"_288\":289},[779,785,791,797,803,809],{\"_179\":263,\"_185\":780,\"_18\":347,\"_266\":781,\"_268\":774,\"_270\":782,\"_271\":783,\"_11\":-7,\"_189\":784,\"_188\":192},5220231,\"doc.5220231\",\"鏁版嵁妯″瀷 (Schema)\",[],\"/data-schemas\",{\"_179\":263,\"_185\":786,\"_18\":787,\"_266\":788,\"_268\":774,\"_270\":-7,\"_271\":789,\"_11\":-7,\"_189\":790,\"_188\":192},5220234,\"鏂板缓鏁版嵁妯″瀷\",\"doc.5220234\",[],\"/create-data-schema\",{\"_179\":263,\"_185\":792,\"_18\":793,\"_266\":794,\"_268\":774,\"_270\":-7,\"_271\":795,\"_11\":-7,\"_189\":796,\"_188\":192},5220237,\"鏋勫缓鏁版嵁妯″瀷\",\"doc.5220237\",[],\"/build-data-schemas\",{\"_179\":263,\"_185\":798,\"_18\":799,\"_266\":800,\"_268\":774,\"_270\":-7,\"_271\":801,\"_11\":-7,\"_189\":802,\"_188\":192},5220243,\"閫氳繃 JSON 绛夌敓鎴怽",\"doc.5220243\",[],\"/generate-from-json\",{\"_179\":263,\"_185\":804,\"_18\":805,\"_266\":806,\"_268\":774,\"_270\":-7,\"_271\":807,\"_11\":-7,\"_189\":808,\"_188\":192},5808428,\"楂樼骇鏁版嵁绫诲瀷\",\"doc.5808428\",[],\"/advanced-data-types\",{\"_179\":279,\"_280\":269,\"_185\":810,\"_18\":811,\"_266\":812,\"_284\":813,\"_210\":814,\"_268\":774,\"_270\":-7,\"_19\":-7,\"_308\":279,\"_11\":-7,\"_189\":136,\"_188\":192,\"_309\":192},63109706,\"鏁版嵁妯″瀷杩涢樁\",\"apiDetailFolder.63109706\",{\"_286\":287,\"_288\":289},[815],{\"_179\":263,\"_185\":816,\"_18\":817,\"_266\":818,\"_268\":810,\"_270\":-7,\"_271\":819,\"_11\":-7,\"_189\":820,\"_188\":192},7158896,\"浣跨敤 oneOf / anyOf / allOf 鏋勫缓缁勫悎妯″紡\",\"doc.7158896\",[],\"/oneof-anyof-allof\",{\"_179\":279,\"_280\":269,\"_185\":822,\"_18\":823,\"_266\":824,\"_284\":825,\"_210\":826,\"_268\":697,\"_270\":-7,\"_19\":-7,\"_308\":279,\"_11\":-7,\"_189\":136,\"_188\":192,\"_309\":192},53568718,\"閴存潈缁勪欢\",\"apiDetailFolder.53568718\",{\"_286\":287,\"_288\":289},[827,832,838,844],{\"_179\":263,\"_185\":828,\"_18\":347,\"_266\":829,\"_268\":822,\"_270\":823,\"_271\":830,\"_11\":-7,\"_189\":831,\"_188\":192},6367782,\"doc.6367782\",[],\"/security-schemes\",{\"_179\":263,\"_185\":833,\"_18\":834,\"_266\":835,\"_268\":822,\"_270\":-7,\"_271\":836,\"_11\":-7,\"_189\":837,\"_188\":192},6367853,\"鍒涘缓閴存潈缁勪欢\",\"doc.6367853\",[],\"/create-security-scheme\",{\"_179\":263,\"_185\":839,\"_18\":840,\"_266\":841,\"_268\":822,\"_270\":-7,\"_271\":842,\"_11\":-7,\"_189\":843,\"_188\":192},6368405,\"浣跨敤閴存潈缁勪欢\",\"doc.6368405\",[],\"/use-security-schemes\",{\"_179\":263,\"_185\":845,\"_18\":846,\"_266\":847,\"_268\":822,\"_270\":-7,\"_271\":848,\"_11\":-7,\"_189\":849,\"_188\":192},6368428,\"鍦ㄧ嚎鏂囨。涓殑閴存潈缁勪欢\",\"doc.6368428\",[],\"/security-schemes-in-docs\",{\"_179\":279,\"_280\":269,\"_185\":851,\"_18\":852,\"_266\":853,\"_284\":854,\"_210\":855,\"_268\":697,\"_270\":-7,\"_19\":-7,\"_308\":279,\"_11\":-7,\"_189\":136,\"_188\":192,\"_309\":192},42649503,\"楂樼骇鍔熻兘\",\"apiDetailFolder.42649503\",{\"_286\":287,\"_288\":289},[856,862,868,874,880],{\"_179\":263,\"_185\":857,\"_18\":858,\"_266\":859,\"_268\":851,\"_270\":-7,\"_271\":860,\"_11\":-7,\"_189\":861,\"_188\":192},5220249,\"鎺ュ彛瀛楁\",\"doc.5220249\",[],\"/api-fields\",{\"_179\":263,\"_185\":863,\"_18\":864,\"_266\":865,\"_268\":851,\"_270\":-7,\"_271\":866,\"_11\":-7,\"_189\":867,\"_188\":192},5220252,\"鎺ュ彛鐘舵€乗",\"doc.5220252\",[],\"/api-status\",{\"_179\":263,\"_185\":869,\"_18\":870,\"_266\":871,\"_268\":851,\"_270\":-7,\"_271\":872,\"_11\":-7,\"_189\":873,\"_188\":192},5830326,\"鍏宠仈娴嬭瘯鍦烘櫙\",\"doc.5830326\",[],\"/linked-test-scenarios\",{\"_179\":263,\"_185\":875,\"_18\":876,\"_266\":877,\"_268\":851,\"_270\":-7,\"_271\":878,\"_11\":-7,\"_189\":879,\"_188\":192},5220254,\"鍙傛暟鍒楄〃澶栬\",\"doc.5220254\",[],\"/parameter-list-appearance\",{\"_179\":263,\"_185\":881,\"_18\":882,\"_266\":883,\"_268\":851,\"_270\":-7,\"_271\":884,\"_11\":-7,\"_189\":885,\"_188\":192},5220255,\"鎺ュ彛鍞竴鏍囪瘑\",\"doc.5220255\",[],\"/api-unique-identifier\",{\"_179\":279,\"_280\":269,\"_185\":887,\"_18\":888,\"_266\":889,\"_284\":890,\"_210\":891,\"_268\":269,\"_270\":-7,\"_19\":-7,\"_308\":279,\"_11\":-7,\"_189\":136,\"_188\":192,\"_309\":192},42649564,\"寮€鍙戝拰璋冭瘯 API\",\"apiDetailFolder.42649564\",{\"_286\":287,\"_288\":289},[892,897,903,909,915,921,927,933,939,945,951,990,1149],{\"_179\":263,\"_185\":893,\"_18\":347,\"_266\":894,\"_268\":887,\"_270\":888,\"_271\":895,\"_11\":-7,\"_189\":896,\"_188\":192},5220261,\"doc.5220261\",[],\"/develop-and-debug-api\",{\"_179\":263,\"_185\":898,\"_18\":899,\"_266\":900,\"_268\":887,\"_270\":-7,\"_271\":901,\"_11\":-7,\"_189\":902,\"_188\":192},5220264,\"鐢熸垚璇锋眰\",\"doc.5220264\",[],\"/generate-request\",{\"_179\":263,\"_185\":904,\"_18\":905,\"_266\":906,\"_268\":887,\"_270\":-7,\"_271\":907,\"_11\":-7,\"_189\":908,\"_188\":192},5220266,\"鍙戦€佽姹俓",\"doc.5220266\",[],\"/send-request\",{\"_179\":263,\"_185\":910,\"_18\":911,\"_266\":912,\"_268\":887,\"_270\":-7,\"_271\":913,\"_11\":-7,\"_189\":914,\"_188\":192},5830564,\"璇锋眰鍘嗗彶\",\"doc.5830564\",[],\"/request-history\",{\"_179\":263,\"_185\":916,\"_18\":917,\"_266\":918,\"_268\":887,\"_270\":-7,\"_271\":919,\"_11\":-7,\"_189\":920,\"_188\":192},5220267,\"鎺ュ彛璋冭瘯鐢ㄤ緥\",\"doc.5220267\",[],\"/api-test-cases\",{\"_179\":263,\"_185\":922,\"_18\":923,\"_266\":924,\"_268\":887,\"_270\":-7,\"_271\":925,\"_11\":-7,\"_189\":926,\"_188\":192},7227782,\"鎺ュ彛娴嬭瘯鐢ㄤ緥\",\"doc.7227782\",[],\"/7227782m0\",{\"_179\":263,\"_185\":928,\"_18\":929,\"_266\":930,\"_268\":887,\"_270\":-7,\"_271\":931,\"_11\":-7,\"_189\":932,\"_188\":192},5220268,\"鍔ㄦ€佸€糪",\"doc.5220268\",[],\"/dynamic-values\",{\"_179\":263,\"_185\":934,\"_18\":935,\"_266\":936,\"_268\":887,\"_270\":-7,\"_271\":937,\"_11\":-7,\"_189\":938,\"_188\":192},5220269,\"鏍￠獙鍝嶅簲\",\"doc.5220269\",[],\"/validate-response\",{\"_179\":263,\"_185\":940,\"_18\":941,\"_266\":942,\"_268\":887,\"_270\":-7,\"_271\":943,\"_11\":-7,\"_189\":944,\"_188\":192},5220270,\"鏂囨。妯″紡/璋冭瘯妯″紡\",\"doc.5220270\",[],\"/design-and-request-mode\",{\"_179\":263,\"_185\":946,\"_18\":947,\"_266\":948,\"_268\":887,\"_270\":-7,\"_271\":949,\"_11\":-7,\"_189\":950,\"_188\":192},5220271,\"鐢熸垚浠ｇ爜\",\"doc.5220271\",[],\"/generate-code\",{\"_179\":279,\"_280\":269,\"_185\":952,\"_18\":953,\"_266\":954,\"_284\":955,\"_210\":956,\"_268\":887,\"_270\":-7,\"_19\":-7,\"_308\":279,\"_11\":-7,\"_189\":136,\"_188\":192,\"_309\":192},45791989,\"鐜鍜屽彉閲廫",\"apiDetailFolder.45791989\",{\"_286\":287,\"_288\":289},[957,962,968,978],{\"_179\":263,\"_185\":958,\"_18\":347,\"_266\":959,\"_268\":952,\"_270\":953,\"_271\":960,\"_11\":-7,\"_189\":961,\"_188\":192},5537408,\"doc.5537408\",[],\"/environments-and-variables\",{\"_179\":263,\"_185\":963,\"_18\":964,\"_266\":965,\"_268\":952,\"_270\":-7,\"_271\":966,\"_11\":-7,\"_189\":967,\"_188\":192},5537410,\"鐜绠＄悊\",\"doc.5537410\",[],\"/environments-and-services\",{\"_179\":263,\"_185\":969,\"_18\":970,\"_266\":971,\"_268\":952,\"_270\":-7,\"_271\":972,\"_11\":-7,\"_189\":977,\"_188\":192},5537409,\"鍏ㄥ眬/鐜/妯″潡/涓存椂鍙橀噺\",\"doc.5537409\",[973,974,975,976],\"鍏ㄥ眬鍙橀噺\",\"鐜鍙橀噺\",\"涓存椂鍙橀噺\",\"娴嬭瘯鏁版嵁鍙橀噺\",\"/global-environment-session-variables\",{\"_179\":279,\"_280\":269,\"_185\":979,\"_18\":980,\"_266\":981,\"_284\":982,\"_210\":983,\"_268\":952,\"_270\":-7,\"_19\":-7,\"_308\":279,\"_11\":-7,\"_189\":136,\"_188\":192,\"_309\":192},49016814,\"Vault Secrets锛堝瘑閽ュ簱锛塡",\"apiDetailFolder.49016814\",{\"_286\":287,\"_288\":289},[984],{\"_179\":263,\"_185\":985,\"_18\":986,\"_266\":987,\"_268\":979,\"_270\":980,\"_271\":988,\"_11\":-7,\"_189\":989,\"_188\":192},5831220,\"鍔熻兘绠€浠媆",\"doc.5831220\",[],\"/vault-secrets\",{\"_179\":279,\"_280\":269,\"_185\":991,\"_18\":992,\"_266\":993,\"_284\":994,\"_210\":995,\"_268\":887,\"_270\":-7,\"_19\":-7,\"_308\":279,\"_11\":-7,\"_189\":136,\"_188\":192,\"_309\":192},46181981,\"鍓嶅悗缃搷浣淺u0026鑴氭湰\",\"apiDetailFolder.46181981\",{\"_286\":287,\"_288\":289},[996,1002,1008,1014,1020,1055,1108,1132],{\"_179\":263,\"_185\":997,\"_18\":347,\"_266\":998,\"_268\":991,\"_270\":999,\"_271\":1000,\"_11\":-7,\"_189\":1001,\"_188\":192},5580531,\"doc.5580531\",\"鍓嶅悗缃搷浣淺",[],\"/pre-post-processors\",{\"_179\":263,\"_185\":1003,\"_18\":1004,\"_266\":1005,\"_268\":991,\"_270\":-7,\"_271\":1006,\"_11\":-7,\"_189\":1007,\"_188\":192},5580821,\"鏂█\",\"doc.5580821\",[],\"/assertions\",{\"_179\":263,\"_185\":1009,\"_18\":1010,\"_266\":1011,\"_268\":991,\"_270\":-7,\"_271\":1012,\"_11\":-7,\"_189\":1013,\"_188\":192},5580967,\"鎻愬彇鍙橀噺\",\"doc.5580967\",[],\"/extract-variables\",{\"_179\":263,\"_185\":1015,\"_18\":1016,\"_266\":1017,\"_268\":991,\"_270\":-7,\"_271\":1018,\"_11\":-7,\"_189\":1019,\"_188\":192},5580968,\"绛夊緟鏃堕棿\",\"doc.5580968\",[],\"/wait-time\",{\"_179\":279,\"_280\":269,\"_185\":1021,\"_18\":1022,\"_266\":1023,\"_284\":1024,\"_210\":1025,\"_268\":991,\"_270\":-7,\"_19\":-7,\"_308\":279,\"_11\":-7,\"_189\":136,\"_188\":192,\"_309\":192},46192506,\"鏁版嵁搴撴搷浣淺",\"apiDetailFolder.46192506\",{\"_286\":287,\"_288\":289},[1026,1031,1037,1043,1049],{\"_179\":263,\"_185\":1027,\"_18\":347,\"_266\":1028,\"_268\":1021,\"_270\":1022,\"_271\":1029,\"_11\":-7,\"_189\":1030,\"_188\":192},5580972,\"doc.5580972\",[],\"/database\",{\"_179\":263,\"_185\":1032,\"_18\":1033,\"_266\":1034,\"_268\":1021,\"_270\":-7,\"_271\":1035,\"_11\":-7,\"_189\":1036,\"_188\":192},6797991,\"MySQL\",\"doc.6797991\",[],\"/mysql\",{\"_179\":263,\"_185\":1038,\"_18\":1039,\"_266\":1040,\"_268\":1021,\"_270\":-7,\"_271\":1041,\"_11\":-7,\"_189\":1042,\"_188\":192},5580974,\"MongoDB\",\"doc.5580974\",[],\"/mongodb\",{\"_179\":263,\"_185\":1044,\"_18\":1045,\"_266\":1046,\"_268\":1021,\"_270\":-7,\"_271\":1047,\"_11\":-7,\"_189\":1048,\"_188\":192},5580976,\"Redis\",\"doc.5580976\",[],\"/redis\",{\"_179\":263,\"_185\":1050,\"_18\":1051,\"_266\":1052,\"_268\":1021,\"_270\":-7,\"_271\":1053,\"_11\":-7,\"_189\":1054,\"_188\":192},5580981,\"Oracle\",\"doc.5580981\",[],\"/oracle\",{\"_179\":279,\"_280\":269,\"_185\":1056,\"_18\":1057,\"_266\":1058,\"_284\":1059,\"_210\":1060,\"_268\":991,\"_270\":-7,\"_19\":-7,\"_308\":279,\"_11\":-7,\"_189\":136,\"_188\":192,\"_309\":192},46192559,\"浣跨敤鑴氭湰\",\"apiDetailFolder.46192559\",{\"_286\":287,\"_288\":289},[1061,1066,1072,1078,1084,1090,1096,1102],{\"_179\":263,\"_185\":1062,\"_18\":347,\"_266\":1063,\"_268\":1056,\"_270\":1057,\"_271\":1064,\"_11\":-7,\"_189\":1065,\"_188\":192},5580993,\"doc.5580993\",[],\"/scripts\",{\"_179\":263,\"_185\":1067,\"_18\":1068,\"_266\":1069,\"_268\":1056,\"_270\":-7,\"_271\":1070,\"_11\":-7,\"_189\":1071,\"_188\":192},5580999,\"鍓嶇疆鑴氭湰\",\"doc.5580999\",[],\"/pre-request-scripts\",{\"_179\":263,\"_185\":1073,\"_18\":1074,\"_266\":1075,\"_268\":1056,\"_270\":-7,\"_271\":1076,\"_11\":-7,\"_189\":1077,\"_188\":192},5581000,\"鍚庣疆鑴氭湰\",\"doc.5581000\",[],\"/post-request-scripts\",{\"_179\":263,\"_185\":1079,\"_18\":1080,\"_266\":1081,\"_268\":1056,\"_270\":-7,\"_271\":1082,\"_11\":-7,\"_189\":1083,\"_188\":192},5581002,\"鍏叡鑴氭湰\",\"doc.5581002\",[],\"/common-scripts\",{\"_179\":263,\"_185\":1085,\"_18\":1086,\"_266\":1087,\"_268\":1056,\"_270\":-7,\"_271\":1088,\"_11\":-7,\"_189\":1089,\"_188\":192},5580997,\"pm 鑴氭湰 API\",\"doc.5580997\",[],\"/postman-script-api\",{\"_179\":263,\"_185\":1091,\"_18\":1092,\"_266\":1093,\"_268\":1056,\"_270\":-7,\"_271\":1094,\"_11\":-7,\"_189\":1095,\"_188\":192},5586307,\"浣跨敤 JS 绫诲簱\",\"doc.5586307\",[],\"/js-libraries\",{\"_179\":263,\"_185\":1097,\"_18\":1098,\"_266\":1099,\"_268\":1056,\"_270\":-7,\"_271\":1100,\"_11\":-7,\"_189\":1101,\"_188\":192},5586317,\"鍝嶅簲鏁版嵁鍙鍖朶",\"doc.5586317\",[],\"/response-data-visualization\",{\"_179\":263,\"_185\":1103,\"_18\":1104,\"_266\":1105,\"_268\":1056,\"_270\":-7,\"_271\":1106,\"_11\":-7,\"_189\":1107,\"_188\":192},5586304,\"璋冪敤澶栭儴绋嬪簭\",\"doc.5586304\",[],\"/call-external-programs\",{\"_179\":279,\"_280\":269,\"_185\":1109,\"_18\":1110,\"_266\":1111,\"_284\":1112,\"_210\":1113,\"_268\":991,\"_270\":-7,\"_19\":-7,\"_308\":279,\"_11\":-7,\"_189\":136,\"_188\":192,\"_309\":192},46234794,\"鑴氭湰绀轰緥\",\"apiDetailFolder.46234794\",{\"_286\":287,\"_288\":289},[1114,1120,1126],{\"_179\":263,\"_185\":1115,\"_18\":1116,\"_266\":1117,\"_268\":1109,\"_270\":-7,\"_271\":1118,\"_11\":-7,\"_189\":1119,\"_188\":192},5589198,\"鏂█绀轰緥\",\"doc.5589198\",[],\"/assertion-examples\",{\"_179\":263,\"_185\":1121,\"_18\":1122,\"_266\":1123,\"_268\":1109,\"_270\":-7,\"_271\":1124,\"_11\":-7,\"_189\":1125,\"_188\":192},5589203,\"鑴氭湰浣跨敤鍙橀噺\",\"doc.5589203\",[],\"/use-variables-in-scripts\",{\"_179\":263,\"_185\":1127,\"_18\":1128,\"_266\":1129,\"_268\":1109,\"_270\":-7,\"_271\":1130,\"_11\":-7,\"_189\":1131,\"_188\":192},5589215,\"鑴氭湰璇诲彇/淇敼鎺ュ彛璇锋眰淇℃伅\",\"doc.5589215\",[],\"/access-modify-request-data\",{\"_179\":279,\"_280\":269,\"_185\":1133,\"_18\":195,\"_266\":1134,\"_284\":1135,\"_210\":1136,\"_268\":991,\"_270\":-7,\"_19\":-7,\"_308\":279,\"_11\":-7,\"_189\":136,\"_188\":192,\"_309\":192},49018227,\"apiDetailFolder.49018227\",{\"_286\":287,\"_288\":289},[1137,1143],{\"_179\":263,\"_185\":1138,\"_18\":1139,\"_266\":1140,\"_268\":1133,\"_270\":-7,\"_271\":1141,\"_11\":-7,\"_189\":1142,\"_188\":192},5831263,\"濡備綍鑾峰彇鍔ㄦ€佸弬鏁扮殑鐪熷疄鍊煎苟鍔犲瘑锛焅",\"doc.5831263\",[],\"/5831263m0\",{\"_179\":263,\"_185\":1144,\"_18\":1145,\"_266\":1146,\"_268\":1133,\"_270\":-7,\"_271\":1147,\"_11\":-7,\"_189\":1148,\"_188\":192},5831265,\"鑴氭湰杩愯鍚庯紝鎻愬彇鐨勬暟瀛楋紙bigint锛夌簿搴︿涪澶卞簲璇ュ浣曞鐞嗭紵\",\"doc.5831265\",[],\"/5831265m0\",{\"_179\":279,\"_280\":269,\"_185\":1150,\"_18\":1151,\"_266\":1152,\"_284\":1153,\"_210\":1154,\"_268\":887,\"_270\":-7,\"_19\":-7,\"_308\":279,\"_11\":-7,\"_189\":136,\"_188\":192,\"_309\":192},42638006,\"API 璋冭瘯\",\"apiDetailFolder.42638006\",{\"_286\":287,\"_288\":289},[1155,1161,1167,1173,1179,1185,1191,1197,1203,1227],{\"_179\":263,\"_185\":1156,\"_18\":1157,\"_266\":1158,\"_268\":1150,\"_270\":-7,\"_271\":1159,\"_11\":-7,\"_189\":1160,\"_188\":192},5218920,\"GraphQL 璋冭瘯\",\"doc.5218920\",[],\"/graphql\",{\"_179\":263,\"_185\":1162,\"_18\":1163,\"_266\":1164,\"_268\":1150,\"_270\":-7,\"_271\":1165,\"_11\":-7,\"_189\":1166,\"_188\":192},5220190,\"WebSocket 璋冭瘯\",\"doc.5220190\",[],\"/websocket\",{\"_179\":263,\"_185\":1168,\"_18\":1169,\"_266\":1170,\"_268\":1150,\"_270\":-7,\"_271\":1171,\"_11\":-7,\"_189\":1172,\"_188\":192},6107994,\"Socket.IO 璋冭瘯\",\"doc.6107994\",[],\"/socketio\",{\"_179\":263,\"_185\":1174,\"_18\":1175,\"_266\":1176,\"_268\":1150,\"_270\":-7,\"_271\":1177,\"_11\":-7,\"_189\":1178,\"_188\":192},5219195,\"SSE 璋冭瘯\",\"doc.5219195\",[],\"/sse\",{\"_179\":263,\"_185\":1180,\"_18\":1181,\"_266\":1182,\"_268\":1150,\"_270\":-7,\"_271\":1183,\"_11\":-7,\"_189\":1184,\"_188\":192},5219198,\"SOAP/WebService\",\"doc.5219198\",[],\"/soap\",{\"_179\":263,\"_185\":1186,\"_18\":1187,\"_266\":1188,\"_268\":1150,\"_270\":-7,\"_271\":1189,\"_11\":-7,\"_189\":1190,\"_188\":192},5219191,\"gRPC 璋冭瘯\",\"doc.5219191\",[],\"/grpc\",{\"_179\":263,\"_185\":1192,\"_18\":1193,\"_266\":1194,\"_268\":1150,\"_270\":-7,\"_271\":1195,\"_11\":-7,\"_189\":1196,\"_188\":192},7036471,\"Webhook 璋冭瘯\",\"doc.7036471\",[],\"/webhook\",{\"_179\":263,\"_185\":1198,\"_18\":1199,\"_266\":1200,\"_268\":1150,\"_270\":-7,\"_271\":1201,\"_11\":-7,\"_189\":1202,\"_188\":192},5830401,\"浣跨敤璇锋眰浠ｇ悊璋冭瘯\",\"doc.5830401\",[],\"/request-proxy-debugging\",{\"_179\":279,\"_280\":269,\"_185\":1204,\"_18\":1205,\"_266\":1206,\"_284\":1207,\"_210\":1208,\"_268\":1150,\"_270\":-7,\"_19\":-7,\"_308\":279,\"_11\":-7,\"_189\":136,\"_188\":192,\"_309\":192},47727522,\"Dubbo 璋冭瘯\",\"apiDetailFolder.47727522\",{\"_286\":287,\"_288\":289},[1209,1215,1221],{\"_179\":263,\"_185\":1210,\"_18\":1211,\"_266\":1212,\"_268\":1204,\"_270\":-7,\"_271\":1213,\"_11\":-7,\"_189\":1214,\"_188\":192},5725400,\"鏂板缓 Dubbo 鎺ュ彛\",\"doc.5725400\",[],\"/create-dubbo-api\",{\"_179\":263,\"_185\":1216,\"_18\":1217,\"_266\":1218,\"_268\":1204,\"_270\":-7,\"_271\":1219,\"_11\":-7,\"_189\":1220,\"_188\":192},5725405,\"璋冭瘯 Dubbo 鎺ュ彛\",\"doc.5725405\",[],\"/debug-dubbo-api\",{\"_179\":263,\"_185\":1222,\"_18\":1223,\"_266\":1224,\"_268\":1204,\"_270\":-7,\"_271\":1225,\"_11\":-7,\"_189\":1226,\"_188\":192},5725407,\"Dubbo 鎺ュ彛鏂囨。\",\"doc.5725407\",[],\"/dubbo-api-documentation\",{\"_179\":279,\"_280\":269,\"_185\":1228,\"_18\":1229,\"_266\":1230,\"_284\":1231,\"_210\":1232,\"_268\":1150,\"_270\":-7,\"_19\":-7,\"_308\":279,\"_11\":-7,\"_189\":136,\"_188\":192,\"_309\":192},49087999,\"TCP锛圫ocket锛塡",\"apiDetailFolder.49087999\",{\"_286\":287,\"_288\":289},[1233,1239],{\"_179\":263,\"_185\":1234,\"_18\":1235,\"_266\":1236,\"_268\":1228,\"_270\":-7,\"_271\":1237,\"_11\":-7,\"_189\":1238,\"_188\":192},5838988,\"Socket 鎺ュ彛鍔熻兘绠€浠媆",\"doc.5838988\",[],\"/tcp-socket\",{\"_179\":263,\"_185\":1240,\"_18\":1241,\"_266\":1242,\"_268\":1228,\"_270\":-7,\"_271\":1243,\"_11\":-7,\"_189\":1244,\"_188\":192},5838989,\"鎶ユ枃鏁版嵁澶勭悊鍣╘",\"doc.5838989\",[],\"/message-data-processor\",{\"_179\":279,\"_280\":269,\"_185\":1246,\"_18\":1247,\"_266\":1248,\"_284\":1249,\"_210\":1250,\"_268\":269,\"_270\":-7,\"_19\":-7,\"_308\":279,\"_11\":-7,\"_189\":136,\"_188\":192,\"_309\":192},46264458,\"Mock 鏁版嵁\",\"apiDetailFolder.46264458\",{\"_286\":287,\"_288\":289},[1251,1256,1262,1268,1274,1280,1286],{\"_179\":263,\"_185\":1252,\"_18\":347,\"_266\":1253,\"_268\":1246,\"_270\":1247,\"_271\":1254,\"_11\":-7,\"_189\":1255,\"_188\":192},5589818,\"doc.5589818\",[],\"/mock\",{\"_179\":263,\"_185\":1257,\"_18\":1258,\"_266\":1259,\"_268\":1246,\"_270\":-7,\"_271\":1260,\"_11\":-7,\"_189\":1261,\"_188\":192},5589821,\"鏅鸿兘 Mock\",\"doc.5589821\",[],\"/smart-mock\",{\"_179\":263,\"_185\":1263,\"_18\":1264,\"_266\":1265,\"_268\":1246,\"_270\":-7,\"_271\":1266,\"_11\":-7,\"_189\":1267,\"_188\":192},5589823,\"鑷畾涔?Mock\",\"doc.5589823\",[],\"/custom-mock\",{\"_179\":263,\"_185\":1269,\"_18\":1270,\"_266\":1271,\"_268\":1246,\"_270\":-7,\"_271\":1272,\"_11\":-7,\"_189\":1273,\"_188\":192},5589825,\"Mock 浼樺厛绾",\"doc.5589825\",[],\"/mock-priority-rules\",{\"_179\":263,\"_185\":1275,\"_18\":1276,\"_266\":1277,\"_268\":1246,\"_270\":-7,\"_271\":1278,\"_11\":-7,\"_189\":1279,\"_188\":192},5589827,\"Mock 鑴氭湰\",\"doc.5589827\",[],\"/mock-scripts\",{\"_179\":263,\"_185\":1281,\"_18\":1282,\"_266\":1283,\"_268\":1246,\"_270\":-7,\"_271\":1284,\"_11\":-7,\"_189\":1285,\"_188\":192},5589829,\"浜戠 Mock\",\"doc.5589829\",[],\"/cloud-mock\",{\"_179\":263,\"_185\":1287,\"_18\":1288,\"_266\":1289,\"_268\":1246,\"_270\":-7,\"_271\":1290,\"_11\":-7,\"_189\":1291,\"_188\":192},5589830,\"鑷墭绠?Runner Mock\",\"doc.5589830\",[],\"/runner-mock\",{\"_179\":279,\"_280\":269,\"_185\":1293,\"_18\":1294,\"_266\":1295,\"_284\":1296,\"_210\":1297,\"_268\":269,\"_270\":-7,\"_19\":-7,\"_308\":279,\"_11\":-7,\"_189\":136,\"_188\":192,\"_309\":192},46470056,\"鑷姩鍖栨祴璇昞",\"apiDetailFolder.46470056\",{\"_286\":287,\"_288\":289},[1298,1303,1344,1379,1390,1420,1443],{\"_179\":263,\"_185\":1299,\"_18\":347,\"_266\":1300,\"_268\":1293,\"_270\":1294,\"_271\":1301,\"_11\":-7,\"_189\":1302,\"_188\":192},5615148,\"doc.5615148\",[],\"/automated-testing\",{\"_179\":279,\"_280\":269,\"_185\":1304,\"_18\":1305,\"_266\":1306,\"_284\":1307,\"_210\":1308,\"_268\":1293,\"_270\":-7,\"_19\":-7,\"_308\":279,\"_11\":-7,\"_189\":136,\"_188\":192,\"_309\":192},46613672,\"缂栨帓娴嬭瘯鍦烘櫙\",\"apiDetailFolder.46613672\",{\"_286\":287,\"_288\":289},[1309,1314,1320,1326,1332,1338],{\"_179\":263,\"_185\":1310,\"_18\":378,\"_266\":1311,\"_268\":1304,\"_270\":-7,\"_271\":1312,\"_11\":-7,\"_189\":1313,\"_188\":192},5629565,\"doc.5629565\",[],\"/new-test-scenario\",{\"_179\":263,\"_185\":1315,\"_18\":1316,\"_266\":1317,\"_268\":1304,\"_270\":-7,\"_271\":1318,\"_11\":-7,\"_189\":1319,\"_188\":192},5629567,\"娴嬭瘯姝ラ闂翠紶閫掓暟鎹甛",\"doc.5629567\",[],\"/pass-data-between-test-steps\",{\"_179\":263,\"_185\":1321,\"_18\":1322,\"_266\":1323,\"_268\":1304,\"_270\":-7,\"_271\":1324,\"_11\":-7,\"_189\":1325,\"_188\":192},5629569,\"娴嬭瘯娴佺▼鎺у埗鏉′欢\",\"doc.5629569\",[],\"/flow-control-conditions\",{\"_179\":263,\"_185\":1327,\"_18\":1328,\"_266\":1329,\"_268\":1304,\"_270\":-7,\"_271\":1330,\"_11\":-7,\"_189\":1331,\"_188\":192},5629571,\"浠庢帴鍙?鐢ㄤ緥鍚屾鏁版嵁\",\"doc.5629571\",[],\"/sync-from-endpoint-or-test-case\",{\"_179\":263,\"_185\":1333,\"_18\":1334,\"_266\":1335,\"_268\":1304,\"_270\":-7,\"_271\":1336,\"_11\":-7,\"_189\":1337,\"_188\":192},5629575,\"璺ㄩ」鐩鍏ユ帴鍙?鐢ㄤ緥\",\"doc.5629575\",[],\"/import-apis-or-cases-cross-projects\",{\"_179\":263,\"_185\":1339,\"_18\":1340,\"_266\":1341,\"_268\":1304,\"_270\":-7,\"_271\":1342,\"_11\":-7,\"_189\":1343,\"_188\":192},5629572,\"瀵煎嚭娴嬭瘯鍦烘櫙鏁版嵁\",\"doc.5629572\",[],\"/export-test-scenario-data\",{\"_179\":279,\"_280\":269,\"_185\":1345,\"_18\":1346,\"_266\":1347,\"_284\":1348,\"_210\":1349,\"_268\":1293,\"_270\":-7,\"_19\":-7,\"_308\":279,\"_11\":-7,\"_189\":136,\"_188\":192,\"_309\":192},46647095,\"杩愯娴嬭瘯鍦烘櫙\",\"apiDetailFolder.46647095\",{\"_286\":287,\"_288\":289},[1350,1355,1361,1367,1373],{\"_179\":263,\"_185\":1351,\"_18\":1346,\"_266\":1352,\"_268\":1345,\"_270\":-7,\"_271\":1353,\"_11\":-7,\"_189\":1354,\"_188\":192},5632037,\"doc.5632037\",[],\"/run-test-scenarios\",{\"_179\":263,\"_185\":1356,\"_18\":1357,\"_266\":1358,\"_268\":1345,\"_270\":-7,\"_271\":1359,\"_11\":-7,\"_189\":1360,\"_188\":192},5635578,\"鎵归噺杩愯娴嬭瘯鍦烘櫙\",\"doc.5635578\",[],\"/batch-run-test-scenarios\",{\"_179\":263,\"_185\":1362,\"_18\":1363,\"_266\":1364,\"_268\":1345,\"_270\":-7,\"_271\":1365,\"_11\":-7,\"_189\":1366,\"_188\":192},5635110,\"鏁版嵁椹卞姩娴嬭瘯\",\"doc.5635110\",[],\"/data-driven-testing\",{\"_179\":263,\"_185\":1368,\"_18\":1369,\"_266\":1370,\"_268\":1345,\"_270\":-7,\"_271\":1371,\"_11\":-7,\"_189\":1372,\"_188\":192},5635614,\"瀹氭椂浠诲姟\",\"doc.5635614\",[],\"/scheduled-tasks\",{\"_179\":263,\"_185\":1374,\"_18\":1375,\"_266\":1376,\"_268\":1345,\"_270\":-7,\"_271\":1377,\"_11\":-7,\"_189\":1378,\"_188\":192},5635652,\"绠＄悊鍏跺畠椤圭洰鎺ュ彛鐨勮繍琛岀幆澧僜",\"doc.5635652\",[],\"/manage-cross-project-environments\",{\"_179\":279,\"_280\":269,\"_185\":1380,\"_18\":1381,\"_266\":1382,\"_284\":1383,\"_210\":1384,\"_268\":1293,\"_270\":-7,\"_19\":-7,\"_308\":279,\"_11\":-7,\"_189\":136,\"_188\":192,\"_309\":192},48490292,\"娴嬭瘯鎶ュ憡\",\"apiDetailFolder.48490292\",{\"_286\":287,\"_288\":289},[1385],{\"_179\":263,\"_185\":1386,\"_18\":1381,\"_266\":1387,\"_268\":1380,\"_270\":-7,\"_271\":1388,\"_11\":-7,\"_189\":1389,\"_188\":192},5615180,\"doc.5615180\",[],\"/test-reports\",{\"_179\":279,\"_280\":269,\"_185\":1391,\"_18\":1392,\"_266\":1393,\"_284\":1394,\"_210\":1395,\"_268\":1293,\"_270\":-7,\"_19\":-7,\"_308\":279,\"_11\":-7,\"_189\":136,\"_188\":192,\"_309\":192},46708766,\"API 娴嬭瘯\",\"apiDetailFolder.46708766\",{\"_286\":287,\"_288\":289},[1396,1402,1408,1414],{\"_179\":263,\"_185\":1397,\"_18\":1398,\"_266\":1399,\"_268\":1391,\"_270\":-7,\"_271\":1400,\"_11\":-7,\"_189\":1401,\"_188\":192},5637213,\"鎬ц兘娴嬭瘯\",\"doc.5637213\",[],\"/performance-testing\",{\"_179\":263,\"_185\":1403,\"_18\":1404,\"_266\":1405,\"_268\":1391,\"_270\":-7,\"_271\":1406,\"_11\":-7,\"_189\":1407,\"_188\":192},5637211,\"闆嗘垚娴嬭瘯\",\"doc.5637211\",[],\"/integration-testing\",{\"_179\":263,\"_185\":1409,\"_18\":1410,\"_266\":1411,\"_268\":1391,\"_270\":-7,\"_271\":1412,\"_11\":-7,\"_189\":1413,\"_188\":192},5637215,\"绔埌绔祴璇昞",\"doc.5637215\",[],\"/end-to-end-testing\",{\"_179\":263,\"_185\":1415,\"_18\":1416,\"_266\":1417,\"_268\":1391,\"_270\":-7,\"_271\":1418,\"_11\":-7,\"_189\":1419,\"_188\":192},5637217,\"鍥炲綊娴嬭瘯\",\"doc.5637217\",[],\"/regression-testing\",{\"_179\":279,\"_280\":269,\"_185\":1421,\"_18\":1422,\"_266\":1423,\"_284\":1424,\"_210\":1425,\"_268\":1293,\"_270\":-7,\"_19\":-7,\"_308\":279,\"_11\":-7,\"_189\":136,\"_188\":192,\"_309\":192},46714047,\"Apifox CLI\",\"apiDetailFolder.46714047\",{\"_286\":287,\"_288\":289},[1426,1431,1437],{\"_179\":263,\"_185\":1427,\"_18\":347,\"_266\":1428,\"_268\":1421,\"_270\":1422,\"_271\":1429,\"_11\":-7,\"_189\":1430,\"_188\":192},5637749,\"doc.5637749\",[],\"/apifox-cli\",{\"_179\":263,\"_185\":1432,\"_18\":1433,\"_266\":1434,\"_268\":1421,\"_270\":-7,\"_271\":1435,\"_11\":-7,\"_189\":1436,\"_188\":192},5637752,\"瀹夎鍜岃繍琛?CLI\",\"doc.5637752\",[],\"/install-and-run-cli\",{\"_179\":263,\"_185\":1438,\"_18\":1439,\"_266\":1440,\"_268\":1421,\"_270\":-7,\"_271\":1441,\"_11\":-7,\"_189\":1442,\"_188\":192},5637756,\"CLI 鍛戒护閫夐」\",\"doc.5637756\",[],\"/cli-command-options\",{\"_179\":279,\"_280\":269,\"_185\":1444,\"_18\":1445,\"_266\":1446,\"_284\":1447,\"_210\":1448,\"_268\":1293,\"_270\":-7,\"_19\":-7,\"_308\":279,\"_11\":-7,\"_189\":136,\"_188\":192,\"_309\":192},46915489,\"CI/CD\",\"apiDetailFolder.46915489\",{\"_286\":287,\"_288\":289},[1449,1455,1461,1467,1473,1479],{\"_179\":263,\"_185\":1450,\"_18\":347,\"_266\":1451,\"_268\":1444,\"_270\":1452,\"_271\":1453,\"_11\":-7,\"_189\":1454,\"_188\":192},5651112,\"doc.5651112\",\"CI/CD 闆嗘垚\",[],\"/cicd\",{\"_179\":263,\"_185\":1456,\"_18\":1457,\"_266\":1458,\"_268\":1444,\"_270\":-7,\"_271\":1459,\"_11\":-7,\"_189\":1460,\"_188\":192},5651150,\"涓?Jenkins 闆嗘垚\",\"doc.5651150\",[],\"/integration-with-jenkins\",{\"_179\":263,\"_185\":1462,\"_18\":1463,\"_266\":1464,\"_268\":1444,\"_270\":-7,\"_271\":1465,\"_11\":-7,\"_189\":1466,\"_188\":192},5651151,\"涓?Gitlab 闆嗘垚\",\"doc.5651151\",[],\"/integration-with-gitlab\",{\"_179\":263,\"_185\":1468,\"_18\":1469,\"_266\":1470,\"_268\":1444,\"_270\":-7,\"_271\":1471,\"_11\":-7,\"_189\":1472,\"_188\":192},5651156,\"涓?Github Actions 闆嗘垚\",\"doc.5651156\",[],\"/integration-with-github-actions\",{\"_179\":263,\"_185\":1474,\"_18\":1475,\"_266\":1476,\"_268\":1444,\"_270\":-7,\"_271\":1477,\"_11\":-7,\"_189\":1478,\"_188\":192},5651160,\"涓庡叾瀹冩洿澶?CI/CD 骞冲彴闆嗘垚\",\"doc.5651160\",[],\"/integration-with-other-ci-cd-platforms\",{\"_179\":263,\"_185\":1480,\"_18\":1481,\"_266\":1482,\"_268\":1444,\"_270\":-7,\"_271\":1483,\"_11\":-7,\"_189\":1484,\"_188\":192},6917191,\"Git 鎻愪氦鑷姩瑙﹀彂娴嬭瘯\",\"doc.6917191\",[],\"/git-commit-triggered-testing\",{\"_179\":279,\"_280\":269,\"_185\":1486,\"_18\":1487,\"_266\":1488,\"_284\":1489,\"_210\":1490,\"_268\":269,\"_270\":-7,\"_19\":-7,\"_308\":279,\"_11\":-7,\"_189\":136,\"_188\":192,\"_309\":192},46920893,\"鍙戝竷 API 鏂囨。\",\"apiDetailFolder.46920893\",{\"_286\":287,\"_288\":289},[1491,1497,1503,1509,1515,1521,1528,1534,1540,1546,1588],{\"_179\":263,\"_185\":1492,\"_18\":347,\"_266\":1493,\"_268\":1486,\"_270\":1494,\"_271\":1495,\"_11\":-7,\"_189\":1496,\"_188\":192},5651467,\"doc.5651467\",\"鍒嗕韩鏂囨。\",[],\"/api-documentation\",{\"_179\":263,\"_185\":1498,\"_18\":1499,\"_266\":1500,\"_268\":1486,\"_270\":-7,\"_271\":1501,\"_11\":-7,\"_189\":1502,\"_188\":192},5652692,\"蹇嵎鍒嗕韩\",\"doc.5652692\",[],\"/quick-share\",{\"_179\":263,\"_185\":1504,\"_18\":1505,\"_266\":1506,\"_268\":1486,\"_270\":-7,\"_271\":1507,\"_11\":-7,\"_189\":1508,\"_188\":192},5701987,\"鏌ョ湅 API 鏂囨。\",\"doc.5701987\",[],\"/view-api-documentation\",{\"_179\":263,\"_185\":1510,\"_18\":1511,\"_266\":1512,\"_268\":1486,\"_270\":-7,\"_271\":1513,\"_11\":-7,\"_189\":1514,\"_188\":192},5701992,\"鍙戝竷鏂囨。绔橽",\"doc.5701992\",[],\"/publish-documentation-site\",{\"_179\":263,\"_185\":1516,\"_18\":1517,\"_266\":1518,\"_268\":1486,\"_270\":-7,\"_271\":1519,\"_11\":-7,\"_189\":1520,\"_188\":192},5702004,\"椤甸潰甯冨眬璁剧疆\",\"doc.5702004\",[],\"/page-layout-settings\",{\"_179\":263,\"_185\":1522,\"_18\":1523,\"_266\":1524,\"_268\":1486,\"_270\":1525,\"_271\":1526,\"_11\":-7,\"_189\":1527,\"_188\":192},6997986,\"鑷畾涔夐〉闈唬鐮乗",\"doc.6997986\",\"鑷畾涔夐〉闈唬鐮侊紙 CSS / JS / HTML锛塡",[],\"/custom-css-js-html\",{\"_179\":263,\"_185\":1529,\"_18\":1530,\"_266\":1531,\"_268\":1486,\"_270\":-7,\"_271\":1532,\"_11\":-7,\"_189\":1533,\"_188\":192},5702008,\"鑷畾涔夊煙鍚峔",\"doc.5702008\",[],\"/custom-domain\",{\"_179\":263,\"_185\":1535,\"_18\":1536,\"_266\":1537,\"_268\":1486,\"_270\":-7,\"_271\":1538,\"_11\":-7,\"_189\":1539,\"_188\":192},6464590,\"AI 鐩稿叧鐗规€",\"doc.6464590\",[],\"/ai-features\",{\"_179\":263,\"_185\":1541,\"_18\":1542,\"_266\":1543,\"_268\":1486,\"_270\":-7,\"_271\":1544,\"_11\":-7,\"_189\":1545,\"_188\":192},5702007,\"SEO 璁剧疆\",\"doc.5702007\",[],\"/seo-settings\",{\"_179\":279,\"_280\":269,\"_185\":1547,\"_18\":1548,\"_266\":1549,\"_284\":1550,\"_210\":1551,\"_268\":1486,\"_270\":-7,\"_19\":-7,\"_308\":279,\"_11\":-7,\"_189\":136,\"_188\":192,\"_309\":192},54584254,\"楂樼骇璁剧疆\",\"apiDetailFolder.54584254\",{\"_286\":287,\"_288\":289},[1552,1558,1564,1570,1576,1582],{\"_179\":263,\"_185\":1553,\"_18\":1554,\"_266\":1555,\"_268\":1547,\"_270\":-7,\"_271\":1556,\"_11\":-7,\"_189\":1557,\"_188\":192},5702016,\"鏂囨。绔欐悳绱㈣缃甛",\"doc.5702016\",[],\"/documentation-site-search-settings\",{\"_179\":263,\"_185\":1559,\"_18\":1560,\"_266\":1561,\"_268\":1547,\"_270\":-7,\"_271\":1562,\"_11\":-7,\"_189\":1563,\"_188\":192},5830046,\"璺ㄥ煙浠ｇ悊\",\"doc.5830046\",[],\"/cross-origin-proxy\",{\"_179\":263,\"_185\":1565,\"_18\":1566,\"_266\":1567,\"_268\":1547,\"_270\":-7,\"_271\":1568,\"_11\":-7,\"_189\":1569,\"_188\":192},5735413,\"鏂囨。绔欐帴鍏?Google Analytics\",\"doc.5735413\",[],\"/google-analytics-integration\",{\"_179\":263,\"_185\":1571,\"_18\":1572,\"_266\":1573,\"_268\":1547,\"_270\":-7,\"_271\":1574,\"_11\":-7,\"_189\":1575,\"_188\":192},5701998,\"鏂囨。宸︿晶鐩綍璁剧疆\",\"doc.5701998\",[],\"/documentation-sidebar-settings\",{\"_179\":263,\"_185\":1577,\"_18\":1578,\"_266\":1579,\"_268\":1547,\"_270\":-7,\"_271\":1580,\"_11\":-7,\"_189\":1581,\"_188\":192},5702006,\"鏂囨。鍙鎬ц缃甛",\"doc.5702006\",[],\"/documentation-visibility-settings\",{\"_179\":263,\"_185\":1583,\"_18\":1584,\"_266\":1585,\"_268\":1547,\"_270\":-7,\"_271\":1586,\"_11\":-7,\"_189\":1587,\"_188\":192},5702014,\"鍦ㄧ嚎 URL 閾炬帴瑙勮寖\",\"doc.5702014\",[],\"/url-link-conventions\",{\"_179\":279,\"_280\":269,\"_185\":1589,\"_18\":1590,\"_266\":1591,\"_284\":1592,\"_210\":1593,\"_268\":1486,\"_270\":-7,\"_19\":-7,\"_308\":279,\"_11\":-7,\"_189\":136,\"_188\":192,\"_309\":192},47534682,\"API 鐗堟湰\",\"apiDetailFolder.47534682\",{\"_286\":287,\"_288\":289},[1594,1599,1605,1611],{\"_179\":263,\"_185\":1595,\"_18\":986,\"_266\":1596,\"_268\":1589,\"_270\":1590,\"_271\":1597,\"_11\":-7,\"_189\":1598,\"_188\":192},5705538,\"doc.5705538\",[],\"/api-version\",{\"_179\":263,\"_185\":1600,\"_18\":1601,\"_266\":1602,\"_268\":1589,\"_270\":-7,\"_271\":1603,\"_11\":-7,\"_189\":1604,\"_188\":192},5705539,\"鍒涘缓 API 鐗堟湰\",\"doc.5705539\",[],\"/create-api-version\",{\"_179\":263,\"_185\":1606,\"_18\":1607,\"_266\":1608,\"_268\":1589,\"_270\":-7,\"_271\":1609,\"_11\":-7,\"_189\":1610,\"_188\":192},5705542,\"鍙戝竷 API 鐗堟湰\",\"doc.5705542\",[],\"/publish-api-version\",{\"_179\":263,\"_185\":1612,\"_18\":1613,\"_266\":1614,\"_268\":1589,\"_270\":-7,\"_271\":1615,\"_11\":-7,\"_189\":1616,\"_188\":192},5705545,\"蹇嵎鍒嗕韩 API 鐗堟湰\",\"doc.5705545\",[],\"/quick-share-api-version\",{\"_179\":279,\"_280\":269,\"_185\":1618,\"_18\":1619,\"_266\":1620,\"_284\":1621,\"_210\":1622,\"_268\":269,\"_270\":-7,\"_19\":-7,\"_308\":279,\"_11\":-7,\"_189\":136,\"_188\":192,\"_309\":192},47541700,\"杩唬鍒嗘敮\",\"apiDetailFolder.47541700\",{\"_286\":287,\"_288\":289},[1623,1628,1634,1640,1646,1652],{\"_179\":263,\"_185\":1624,\"_18\":986,\"_266\":1625,\"_268\":1618,\"_270\":1619,\"_271\":1626,\"_11\":-7,\"_189\":1627,\"_188\":192},5706205,\"doc.5706205\",[],\"/sprint-branch\",{\"_179\":263,\"_185\":1629,\"_18\":1630,\"_266\":1631,\"_268\":1618,\"_270\":-7,\"_271\":1632,\"_11\":-7,\"_189\":1633,\"_188\":192},5706206,\"鏂板缓杩唬鍒嗘敮\",\"doc.5706206\",[],\"/create-sprint-branch\",{\"_179\":263,\"_185\":1635,\"_18\":1636,\"_266\":1637,\"_268\":1618,\"_270\":-7,\"_271\":1638,\"_11\":-7,\"_189\":1639,\"_188\":192},5706208,\"鍦ㄨ凯浠ｅ垎鏀腑鏀瑰姩 API\",\"doc.5706208\",[],\"/api-changes-in-sprint-branch\",{\"_179\":263,\"_185\":1641,\"_18\":1642,\"_266\":1643,\"_268\":1618,\"_270\":-7,\"_271\":1644,\"_11\":-7,\"_189\":1645,\"_188\":192},5706211,\"鍦ㄨ凯浠ｅ垎鏀腑娴嬭瘯 API\",\"doc.5706211\",[],\"/test-api-in-sprint-branch\",{\"_179\":263,\"_185\":1647,\"_18\":1648,\"_266\":1649,\"_268\":1618,\"_270\":-7,\"_271\":1650,\"_11\":-7,\"_189\":1651,\"_188\":192},5706212,\"鍚堝苟杩唬鍒嗘敮\",\"doc.5706212\",[],\"/merge-sprint-branch\",{\"_179\":263,\"_185\":1653,\"_18\":1654,\"_266\":1655,\"_268\":1618,\"_270\":-7,\"_271\":1656,\"_11\":-7,\"_189\":1657,\"_188\":192},5706216,\"绠＄悊杩唬鍒嗘敮\",\"doc.5706216\",[],\"/manage-sprint-branch\",{\"_179\":279,\"_280\":269,\"_185\":1659,\"_18\":1660,\"_266\":1661,\"_284\":1662,\"_210\":1663,\"_268\":269,\"_270\":-7,\"_19\":-7,\"_308\":279,\"_11\":-7,\"_189\":136,\"_188\":192,\"_309\":192},49712699,\"绠＄悊涓績\",\"apiDetailFolder.49712699\",{\"_286\":287,\"_288\":289},[1664,1682,1742,1802],{\"_179\":279,\"_280\":269,\"_185\":1665,\"_18\":1666,\"_266\":1667,\"_284\":1668,\"_210\":1669,\"_268\":1659,\"_270\":-7,\"_19\":-7,\"_308\":279,\"_11\":-7,\"_189\":136,\"_188\":192,\"_309\":192},49712854,\"鍏ラ┗娓呭崟\",\"apiDetailFolder.49712854\",{\"_286\":287,\"_288\":289},[1670,1676],{\"_179\":263,\"_185\":1671,\"_18\":1672,\"_266\":1673,\"_268\":1665,\"_270\":-7,\"_271\":1674,\"_11\":-7,\"_189\":1675,\"_188\":192},5712879,\"浜嗚В鍩烘湰姒傚康\",\"doc.5712879\",[],\"/management-center\",{\"_179\":263,\"_185\":1677,\"_18\":1678,\"_266\":1679,\"_268\":1665,\"_270\":-7,\"_271\":1680,\"_11\":-7,\"_189\":1681,\"_188\":192},5712619,\"鍥㈤槦鍏ラ┗\",\"doc.5712619\",[],\"/onboard-team\",{\"_179\":279,\"_280\":269,\"_185\":1683,\"_18\":1684,\"_266\":1685,\"_284\":1686,\"_210\":1687,\"_268\":1659,\"_270\":-7,\"_19\":-7,\"_308\":279,\"_11\":-7,\"_189\":136,\"_188\":192,\"_309\":192},47605996,\"绠＄悊鍥㈤槦\",\"apiDetailFolder.47605996\",{\"_286\":287,\"_288\":289},[1688,1694,1700,1706,1730],{\"_179\":263,\"_185\":1689,\"_18\":1690,\"_266\":1691,\"_268\":1683,\"_270\":-7,\"_271\":1692,\"_11\":-7,\"_189\":1693,\"_188\":192},5713360,\"鎴愬憳瑙掕壊涓庢潈闄愯缃甛",\"doc.5713360\",[],\"/member-roles-and-permissions\",{\"_179\":263,\"_185\":1695,\"_18\":1696,\"_266\":1697,\"_268\":1683,\"_270\":-7,\"_271\":1698,\"_11\":-7,\"_189\":1699,\"_188\":192},5713261,\"鍥㈤槦鍩烘湰鎿嶄綔\",\"doc.5713261\",[],\"/team-basic-operations\",{\"_179\":263,\"_185\":1701,\"_18\":1702,\"_266\":1703,\"_268\":1683,\"_270\":-7,\"_271\":1704,\"_11\":-7,\"_189\":1705,\"_188\":192},5800393,\"鍥㈤槦鎴愬憳绠＄悊\",\"doc.5800393\",[],\"/team-member-management\",{\"_179\":279,\"_280\":269,\"_185\":1707,\"_18\":1708,\"_266\":1709,\"_284\":1710,\"_210\":1711,\"_268\":1683,\"_270\":-7,\"_19\":-7,\"_308\":279,\"_11\":-7,\"_189\":136,\"_188\":192,\"_309\":192},47614814,\"鍥㈤槦璧勬簮\",\"apiDetailFolder.47614814\",{\"_286\":287,\"_288\":289},[1712,1718,1724],{\"_179\":263,\"_185\":1713,\"_18\":1714,\"_266\":1715,\"_268\":1707,\"_270\":-7,\"_271\":1716,\"_11\":-7,\"_189\":1717,\"_188\":192},5714000,\"閫氱敤 Runner\",\"doc.5714000\",[],\"/universal-runner\",{\"_179\":263,\"_185\":1719,\"_18\":1720,\"_266\":1721,\"_268\":1707,\"_270\":-7,\"_271\":1722,\"_11\":-7,\"_189\":1723,\"_188\":192},5830068,\"璇锋眰浠ｇ悊 Agent\",\"doc.5830068\",[],\"/request-proxy-agent\",{\"_179\":263,\"_185\":1725,\"_18\":1726,\"_266\":1727,\"_268\":1707,\"_270\":-7,\"_271\":1728,\"_11\":-7,\"_189\":1729,\"_188\":192},5817756,\"鍥㈤槦鍙橀噺\",\"doc.5817756\",[],\"/team-variables\",{\"_179\":279,\"_280\":269,\"_185\":1731,\"_18\":1732,\"_266\":1733,\"_284\":1734,\"_210\":1735,\"_268\":1683,\"_270\":-7,\"_19\":-7,\"_308\":279,\"_11\":-7,\"_189\":136,\"_188\":192,\"_309\":192},49713952,\"瀹炴椂鍗忎綔\",\"apiDetailFolder.49713952\",{\"_286\":287,\"_288\":289},[1736],{\"_179\":263,\"_185\":1737,\"_18\":1738,\"_266\":1739,\"_268\":1731,\"_270\":-7,\"_271\":1740,\"_11\":-7,\"_189\":1741,\"_188\":192},5713996,\"鍥㈤槦鍗忎綔\",\"doc.5713996\",[],\"/team-collaboration\",{\"_179\":279,\"_280\":269,\"_185\":1743,\"_18\":1744,\"_266\":1745,\"_284\":1746,\"_210\":1747,\"_268\":1659,\"_270\":-7,\"_19\":-7,\"_308\":279,\"_11\":-7,\"_189\":136,\"_188\":192,\"_309\":192},49713028,\"绠＄悊椤圭洰\",\"apiDetailFolder.49713028\",{\"_286\":287,\"_288\":289},[1748,1754,1760,1784],{\"_179\":263,\"_185\":1749,\"_18\":1750,\"_266\":1751,\"_268\":1743,\"_270\":-7,\"_271\":1752,\"_11\":-7,\"_189\":1753,\"_188\":192},5713374,\"椤圭洰鍩烘湰鎿嶄綔\",\"doc.5713374\",[],\"/project-basic-operations\",{\"_179\":263,\"_185\":1755,\"_18\":1756,\"_266\":1757,\"_268\":1743,\"_270\":-7,\"_271\":1758,\"_11\":-7,\"_189\":1759,\"_188\":192},5800993,\"椤圭洰鎴愬憳绠＄悊\",\"doc.5800993\",[],\"/project-member-management\",{\"_179\":279,\"_280\":269,\"_185\":1761,\"_18\":1762,\"_266\":1763,\"_284\":1764,\"_210\":1765,\"_268\":1743,\"_270\":-7,\"_19\":-7,\"_308\":279,\"_11\":-7,\"_189\":136,\"_188\":192,\"_309\":192},47626936,\"閫氱煡璁剧疆\",\"apiDetailFolder.47626936\",{\"_286\":287,\"_288\":289},[1766,1772,1778],{\"_179\":263,\"_185\":1767,\"_18\":986,\"_266\":1768,\"_268\":1761,\"_270\":1769,\"_271\":1770,\"_11\":-7,\"_189\":1771,\"_188\":192},5714005,\"doc.5714005\",\"娑堟伅閫氱煡\",[],\"/message-notifications\",{\"_179\":263,\"_185\":1773,\"_18\":1774,\"_266\":1775,\"_268\":1761,\"_270\":-7,\"_271\":1776,\"_11\":-7,\"_189\":1777,\"_188\":192},5714006,\"閫氱煡瀵硅薄\",\"doc.5714006\",[],\"/notification-targets\",{\"_179\":263,\"_185\":1779,\"_18\":1780,\"_266\":1781,\"_268\":1761,\"_270\":-7,\"_271\":1782,\"_11\":-7,\"_189\":1783,\"_188\":192},5714007,\"閫氱煡浜嬩欢\",\"doc.5714007\",[],\"/notification-events\",{\"_179\":279,\"_280\":269,\"_185\":1785,\"_18\":1786,\"_266\":1787,\"_284\":1788,\"_210\":1789,\"_268\":1743,\"_270\":-7,\"_19\":-7,\"_308\":279,\"_11\":-7,\"_189\":136,\"_188\":192,\"_309\":192},49716045,\"椤圭洰璧勬簮\",\"apiDetailFolder.49716045\",{\"_286\":287,\"_288\":289},[1790,1796],{\"_179\":263,\"_185\":1791,\"_18\":1792,\"_266\":1793,\"_268\":1785,\"_270\":-7,\"_271\":1794,\"_11\":-7,\"_189\":1795,\"_188\":192},5909682,\"鏁版嵁搴撹繛鎺",\"doc.5909682\",[],\"/database-connections\",{\"_179\":263,\"_185\":1797,\"_18\":1798,\"_266\":1799,\"_268\":1785,\"_270\":-7,\"_271\":1800,\"_11\":-7,\"_189\":1801,\"_188\":192},7230090,\"Git 浠撳簱杩炴帴\",\"doc.7230090\",[],\"/7230090m0\",{\"_179\":279,\"_280\":269,\"_185\":1803,\"_18\":1804,\"_266\":1805,\"_284\":1806,\"_210\":1807,\"_268\":1659,\"_270\":-7,\"_19\":-7,\"_308\":279,\"_11\":-7,\"_189\":136,\"_188\":192,\"_309\":192},47632524,\"绠＄悊缁勭粐\",\"apiDetailFolder.47632524\",{\"_286\":287,\"_288\":289},[1808,1843,1859,1871],{\"_179\":279,\"_280\":269,\"_185\":1809,\"_18\":1810,\"_266\":1811,\"_284\":1812,\"_210\":1813,\"_268\":1803,\"_270\":-7,\"_19\":-7,\"_308\":279,\"_11\":-7,\"_189\":136,\"_188\":192,\"_309\":192},47632538,\"鍗曠偣鐧诲綍锛圫SO锛塡",\"apiDetailFolder.47632538\",{\"_286\":287,\"_288\":289},[1814,1819,1825,1831,1837],{\"_179\":263,\"_185\":1815,\"_18\":986,\"_266\":1816,\"_268\":1809,\"_270\":1810,\"_271\":1817,\"_11\":-7,\"_189\":1818,\"_188\":192},5715169,\"doc.5715169\",[],\"/sso\",{\"_179\":263,\"_185\":1820,\"_18\":1821,\"_266\":1822,\"_268\":1809,\"_270\":-7,\"_271\":1823,\"_11\":-7,\"_189\":1824,\"_188\":192},5715170,\"涓虹粍缁囬厤缃崟鐐圭櫥褰昞",\"doc.5715170\",[],\"/configure-sso-for-organization\",{\"_179\":263,\"_185\":1826,\"_18\":1827,\"_266\":1828,\"_268\":1809,\"_270\":-7,\"_271\":1829,\"_11\":-7,\"_189\":1830,\"_188\":192},5715171,\"绠＄悊鐢ㄦ埛璐﹀彿\",\"doc.5715171\",[],\"/manage-user-accounts\",{\"_179\":263,\"_185\":1832,\"_18\":1833,\"_266\":1834,\"_268\":1809,\"_270\":-7,\"_271\":1835,\"_11\":-7,\"_189\":1836,\"_188\":192},5715174,\"灏嗙粍鏄犲皠鍒板洟闃焅",\"doc.5715174\",[],\"/map-groups-to-teams\",{\"_179\":263,\"_185\":1838,\"_18\":1839,\"_266\":1840,\"_268\":1809,\"_270\":-7,\"_271\":1841,\"_11\":-7,\"_189\":1842,\"_188\":192},5715175,\"Microsoft Entra ID\",\"doc.5715175\",[],\"/microsoft-entra-id\",{\"_179\":279,\"_280\":269,\"_185\":1844,\"_18\":1845,\"_266\":1846,\"_284\":1847,\"_210\":1848,\"_268\":1803,\"_270\":-7,\"_19\":-7,\"_308\":279,\"_11\":-7,\"_189\":136,\"_188\":192,\"_309\":192},********,\"SCIM 鐢ㄦ埛绠＄悊\",\"apiDetailFolder.********\",{\"_286\":287,\"_288\":289},[1849,1854],{\"_179\":263,\"_185\":1850,\"_18\":986,\"_266\":1851,\"_268\":1844,\"_270\":1845,\"_271\":1852,\"_11\":-7,\"_189\":1853,\"_188\":192},5715176,\"doc.5715176\",[],\"/scim-user-management\",{\"_179\":263,\"_185\":1855,\"_18\":1839,\"_266\":1856,\"_268\":1844,\"_270\":-7,\"_271\":1857,\"_11\":-7,\"_189\":1858,\"_188\":192},5715178,\"doc.5715178\",[],\"/scmi-microsoft-entra-id\",{\"_179\":279,\"_280\":269,\"_185\":1860,\"_18\":1861,\"_266\":1862,\"_284\":1863,\"_210\":1864,\"_268\":1803,\"_270\":-7,\"_19\":-7,\"_308\":279,\"_11\":-7,\"_189\":136,\"_188\":192,\"_309\":192},47632681,\"缁勭粐璧勬簮\",\"apiDetailFolder.47632681\",{\"_286\":287,\"_288\":289},[1865],{\"_179\":263,\"_185\":1866,\"_18\":1867,\"_266\":1868,\"_268\":1860,\"_270\":-7,\"_271\":1869,\"_11\":-7,\"_189\":1870,\"_188\":192},5715182,\"鑷墭绠?Runner\",\"doc.5715182\",[],\"/self-hosted-runner\",{\"_179\":279,\"_280\":269,\"_185\":1872,\"_18\":1873,\"_266\":1874,\"_284\":1875,\"_210\":1876,\"_268\":1803,\"_270\":-7,\"_19\":-7,\"_308\":279,\"_11\":-7,\"_189\":136,\"_188\":192,\"_309\":192},63568743,\"璁㈠崟绠＄悊\",\"apiDetailFolder.63568743\",{\"_286\":287,\"_288\":289},[1877],{\"_179\":263,\"_185\":1878,\"_18\":1879,\"_266\":1880,\"_268\":1872,\"_270\":-7,\"_271\":1881,\"_11\":-7,\"_189\":1882,\"_188\":192},7191758,\"缁勭粐浠樿垂缁忕悊\",\"doc.7191758\",[],\"/7191758m0\",{\"_179\":279,\"_280\":269,\"_185\":1884,\"_18\":1885,\"_266\":1886,\"_284\":1887,\"_210\":1888,\"_268\":269,\"_270\":-7,\"_19\":-7,\"_308\":279,\"_11\":-7,\"_189\":136,\"_188\":192,\"_309\":192},61655708,\"绂荤嚎绌洪棿\",\"apiDetailFolder.61655708\",{\"_286\":287,\"_288\":289},[1889],{\"_179\":263,\"_185\":1890,\"_18\":986,\"_266\":1891,\"_268\":1884,\"_270\":1885,\"_271\":1892,\"_11\":-7,\"_189\":1893,\"_188\":192},7035411,\"doc.7035411\",[],\"/offline-space\",{\"_179\":279,\"_280\":269,\"_185\":1895,\"_18\":1896,\"_266\":1897,\"_284\":1898,\"_210\":1899,\"_268\":269,\"_270\":-7,\"_19\":-7,\"_308\":279,\"_11\":-7,\"_189\":136,\"_188\":192,\"_309\":192},41772643,\"IDEA 鎻掍欢\",\"apiDetailFolder.41772643\",{\"_286\":287,\"_288\":289},[1900,1905,1911,1917,1953,1971],{\"_179\":263,\"_185\":1901,\"_18\":341,\"_266\":1902,\"_268\":1895,\"_270\":-7,\"_271\":1903,\"_11\":-7,\"_189\":1904,\"_188\":192},5743620,\"doc.5743620\",[],\"/apifox-idea-plugin\",{\"_179\":263,\"_185\":1906,\"_18\":1907,\"_266\":1908,\"_268\":1895,\"_270\":-7,\"_271\":1909,\"_11\":-7,\"_189\":1910,\"_188\":192},5139742,\"鐢熸垚鎺ュ彛鏂囨。\",\"doc.5139742\",[],\"/generate-api-docs-with-idea\",{\"_179\":263,\"_185\":1912,\"_18\":1913,\"_266\":1914,\"_268\":1895,\"_270\":-7,\"_271\":1915,\"_11\":-7,\"_189\":1916,\"_188\":192},5140509,\"鐢熸垚鏁版嵁妯″瀷\",\"doc.5140509\",[],\"/generate-data-schemas-with-idea\",{\"_179\":279,\"_280\":269,\"_185\":1918,\"_18\":1919,\"_266\":1920,\"_284\":1921,\"_210\":1922,\"_268\":1895,\"_270\":-7,\"_19\":-7,\"_308\":279,\"_11\":-7,\"_189\":136,\"_188\":192,\"_309\":192},48584809,\"閰嶇疆\",\"apiDetailFolder.48584809\",{\"_286\":287,\"_288\":289},[1923,1929,1935,1941,1947],{\"_179\":263,\"_185\":1924,\"_18\":1925,\"_266\":1926,\"_268\":1918,\"_270\":-7,\"_271\":1927,\"_11\":-7,\"_189\":1928,\"_188\":192},5801717,\"鍏ㄥ眬閰嶇疆\",\"doc.5801717\",[],\"/5801717m0\",{\"_179\":263,\"_185\":1930,\"_18\":1931,\"_266\":1932,\"_268\":1918,\"_270\":-7,\"_271\":1933,\"_11\":-7,\"_189\":1934,\"_188\":192},5801720,\"椤圭洰鍐呴厤缃甛",\"doc.5801720\",[],\"/5801720m0\",{\"_179\":263,\"_185\":1936,\"_18\":1937,\"_266\":1938,\"_268\":1918,\"_270\":-7,\"_271\":1939,\"_11\":-7,\"_189\":1940,\"_188\":192},5801721,\"鍙厤缃鍒橽",\"doc.5801721\",[],\"/5801721m0\",{\"_179\":263,\"_185\":1942,\"_18\":1943,\"_266\":1944,\"_268\":1918,\"_270\":-7,\"_271\":1945,\"_11\":-7,\"_189\":1946,\"_188\":192},5801722,\"鑴氭湰宸ュ叿\",\"doc.5801722\",[],\"/5801722m0\",{\"_179\":263,\"_185\":1948,\"_18\":1949,\"_266\":1950,\"_268\":1918,\"_270\":-7,\"_271\":1951,\"_11\":-7,\"_189\":1952,\"_188\":192},5801723,\"Groovy 鏈湴鎵╁睍\",\"doc.5801723\",[],\"/5801723m0\",{\"_179\":279,\"_280\":269,\"_185\":1954,\"_18\":1955,\"_266\":1956,\"_284\":1957,\"_210\":1958,\"_268\":1895,\"_270\":-7,\"_19\":-7,\"_308\":279,\"_11\":-7,\"_189\":136,\"_188\":192,\"_309\":192},48584950,\"杩涢樁閰嶇疆\",\"apiDetailFolder.48584950\",{\"_286\":287,\"_288\":289},[1959,1965],{\"_179\":263,\"_185\":1960,\"_18\":1961,\"_266\":1962,\"_268\":1954,\"_270\":-7,\"_271\":1963,\"_11\":-7,\"_189\":1964,\"_188\":192},5801730,\"娉ㄩ噴瑙勮寖璇存槑\",\"doc.5801730\",[],\"/5801730m0\",{\"_179\":263,\"_185\":1966,\"_18\":1967,\"_266\":1968,\"_268\":1954,\"_270\":-7,\"_271\":1969,\"_11\":-7,\"_189\":1970,\"_188\":192},5801731,\"妗嗘灦鏀寔\",\"doc.5801731\",[],\"/5801731m0\",{\"_179\":279,\"_280\":269,\"_185\":1972,\"_18\":195,\"_266\":1973,\"_284\":1974,\"_210\":1975,\"_268\":1895,\"_270\":-7,\"_19\":-7,\"_308\":279,\"_11\":-7,\"_189\":136,\"_188\":192,\"_309\":192},48585251,\"apiDetailFolder.48585251\",{\"_286\":287,\"_288\":289},[1976],{\"_179\":263,\"_185\":1977,\"_18\":195,\"_266\":1978,\"_268\":1972,\"_270\":-7,\"_271\":1979,\"_11\":-7,\"_189\":1980,\"_188\":192},5801734,\"doc.5801734\",[],\"/apifox-idea-plugin-faqs\",{\"_179\":279,\"_280\":269,\"_185\":1982,\"_18\":1983,\"_266\":1984,\"_284\":1985,\"_210\":1986,\"_268\":269,\"_270\":-7,\"_19\":-7,\"_308\":279,\"_11\":-7,\"_189\":136,\"_188\":192,\"_309\":192},48674219,\"娴忚鍣ㄦ墿灞昞",\"apiDetailFolder.48674219\",{\"_286\":287,\"_288\":289},[1987,1994],{\"_179\":263,\"_185\":1988,\"_18\":1989,\"_266\":1990,\"_268\":1982,\"_270\":1991,\"_271\":1992,\"_11\":-7,\"_189\":1993,\"_188\":192},5807583,\"Chrome\",\"doc.5807583\",\"Chrome 娴忚鍣ㄦ墿灞曞畨瑁匼",[],\"/chrome-extension\",{\"_179\":263,\"_185\":1995,\"_18\":1996,\"_266\":1997,\"_268\":1982,\"_270\":1998,\"_271\":1999,\"_11\":-7,\"_189\":2000,\"_188\":192},5807584,\"Microsoft Edge\",\"doc.5807584\",\"Microsoft Edge 娴忚鍣ㄦ墿灞曞畨瑁匼",[],\"/microsoft-edge-extension\",{\"_179\":279,\"_280\":269,\"_185\":2002,\"_18\":2003,\"_266\":2004,\"_284\":2005,\"_210\":2006,\"_268\":269,\"_270\":-7,\"_19\":-7,\"_308\":279,\"_11\":-7,\"_189\":136,\"_188\":192,\"_309\":192},59225815,\"Apifox AI 鍔熻兘\",\"apiDetailFolder.59225815\",{\"_286\":287,\"_288\":289},[2007,2013,2019,2025,2031,2037],{\"_179\":263,\"_185\":2008,\"_18\":2009,\"_266\":2010,\"_268\":2002,\"_270\":-7,\"_271\":2011,\"_11\":-7,\"_189\":2012,\"_188\":192},6875653,\"鎬昏\",\"doc.6875653\",[],\"/apifox-ai\",{\"_179\":263,\"_185\":2014,\"_18\":2015,\"_266\":2016,\"_268\":2002,\"_270\":-7,\"_271\":2017,\"_11\":-7,\"_189\":2018,\"_188\":192},6875656,\"鍚敤 AI 鍔熻兘\",\"doc.6875656\",[],\"/enable-ai-features\",{\"_179\":263,\"_185\":2020,\"_18\":2021,\"_266\":2022,\"_268\":2002,\"_270\":-7,\"_271\":2023,\"_11\":-7,\"_189\":2024,\"_188\":192},6875667,\"淇敼鏁版嵁妯″瀷\",\"doc.6875667\",[],\"/generate-data-schemas-with-ai\",{\"_179\":263,\"_185\":2026,\"_18\":2027,\"_266\":2028,\"_268\":2002,\"_270\":-7,\"_271\":2029,\"_11\":-7,\"_189\":2030,\"_188\":192},7150435,\"鎺ュ彛瑙勮寖鎬ф娴媆",\"doc.7150435\",[],\"/api-compliance-check\",{\"_179\":263,\"_185\":2032,\"_18\":2033,\"_266\":2034,\"_268\":2002,\"_270\":-7,\"_271\":2035,\"_11\":-7,\"_189\":2036,\"_188\":192},7150436,\"瀛楁鍛藉悕\",\"doc.7150436\",[],\"/field-naming\",{\"_179\":263,\"_185\":2038,\"_18\":195,\"_266\":2039,\"_268\":2002,\"_270\":-7,\"_271\":2040,\"_11\":-7,\"_189\":2041,\"_188\":192},6876907,\"doc.6876907\",[],\"/apifox-ai-faqs\",{\"_179\":279,\"_280\":269,\"_185\":2043,\"_18\":2044,\"_266\":2045,\"_284\":2046,\"_210\":2047,\"_268\":269,\"_270\":-7,\"_19\":-7,\"_308\":279,\"_11\":-7,\"_189\":136,\"_188\":192,\"_309\":192},52502933,\"Apifox MCP Server\",\"apiDetailFolder.52502933\",{\"_286\":287,\"_288\":289},[2048,2053,2059,2065],{\"_179\":263,\"_185\":2049,\"_18\":347,\"_266\":2050,\"_268\":2043,\"_270\":2044,\"_271\":2051,\"_11\":-7,\"_189\":2052,\"_188\":192},6264407,\"doc.6264407\",[],\"/apifox-mcp-server\",{\"_179\":263,\"_185\":2054,\"_18\":2055,\"_266\":2056,\"_268\":2043,\"_270\":-7,\"_271\":2057,\"_11\":-7,\"_189\":2058,\"_188\":192},6327888,\"閫氳繃 MCP 浣跨敤 Apifox 椤圭洰鍐呯殑 API 鏂囨。\",\"doc.6327888\",[],\"/6327888m0\",{\"_179\":263,\"_185\":2060,\"_18\":2061,\"_266\":2062,\"_268\":2043,\"_270\":-7,\"_271\":2063,\"_11\":-7,\"_189\":2064,\"_188\":192},6327890,\"閫氳繃 MCP 浣跨敤鍏紑鍙戝竷鐨?API 鏂囨。\",\"doc.6327890\",[],\"/6327890m0\",{\"_179\":263,\"_185\":2066,\"_18\":2067,\"_266\":2068,\"_268\":2043,\"_270\":2069,\"_271\":2070,\"_11\":-7,\"_189\":2071,\"_188\":192},6327891,\"閫氳繃 MCP 浣跨敤 OpenAPI/Swagger鏂囨。\",\"doc.6327891\",\"閫氳繃 MCP 浣跨敤 OpenAPI/Swagger 鏂囨。\",[],\"/6327891m0\",{\"_179\":279,\"_280\":269,\"_185\":2073,\"_18\":2074,\"_266\":2075,\"_284\":2076,\"_210\":2077,\"_268\":269,\"_270\":-7,\"_19\":-7,\"_308\":279,\"_11\":-7,\"_189\":136,\"_188\":192,\"_309\":192},47551540,\"鏈€浣冲疄璺礬",\"apiDetailFolder.47551540\",{\"_286\":287,\"_288\":289},[2078,2083,2089,2095,2101,2107,2113,2119,2125,2131,2137],{\"_179\":263,\"_185\":2079,\"_18\":347,\"_266\":2080,\"_268\":2073,\"_270\":2074,\"_271\":2081,\"_11\":-7,\"_189\":2082,\"_188\":192},5743521,\"doc.5743521\",[],\"/5743521m0\",{\"_179\":263,\"_185\":2084,\"_18\":2085,\"_266\":2086,\"_268\":2073,\"_270\":-7,\"_271\":2087,\"_11\":-7,\"_189\":2088,\"_188\":192},5793498,\"鎺ュ彛涔嬮棿濡備綍浼犻€掓暟鎹甛",\"doc.5793498\",[],\"/5793498m0\",{\"_179\":263,\"_185\":2090,\"_18\":2091,\"_266\":2092,\"_268\":2073,\"_270\":-7,\"_271\":2093,\"_11\":-7,\"_189\":2094,\"_188\":192},5802184,\"鐧诲綍鎬侊紙Auth锛夊浣曞鐞哱",\"doc.5802184\",[],\"/5802184m0\",{\"_179\":263,\"_185\":2096,\"_18\":2097,\"_266\":2098,\"_268\":2073,\"_270\":-7,\"_271\":2099,\"_11\":-7,\"_189\":2100,\"_188\":192},5802226,\"鎺ュ彛绛惧悕濡備綍澶勭悊\",\"doc.5802226\",[],\"/5802226m0\",{\"_179\":263,\"_185\":2102,\"_18\":2103,\"_266\":2104,\"_268\":2073,\"_270\":-7,\"_271\":2105,\"_11\":-7,\"_189\":2106,\"_188\":192},5802338,\"濡備綍鍔犲瘑/瑙ｅ瘑鎺ュ彛鏁版嵁\",\"doc.5802338\",[],\"/5802338m0\",{\"_179\":263,\"_185\":2108,\"_18\":2109,\"_266\":2110,\"_268\":2073,\"_270\":-7,\"_271\":2111,\"_11\":-7,\"_189\":2112,\"_188\":192},5802381,\"Jenkins 瀹氭椂瑙﹀彂浠诲姟\",\"doc.5802381\",[],\"/5802381m0\",{\"_179\":263,\"_185\":2114,\"_18\":2115,\"_266\":2116,\"_268\":2073,\"_270\":-7,\"_271\":2117,\"_11\":-7,\"_189\":2118,\"_188\":192},5835636,\"濡備綍璁＄畻 AI 闂瓟鎴愭湰\",\"doc.5835636\",[],\"/5835636m0\",{\"_179\":263,\"_185\":2120,\"_18\":2121,\"_266\":2122,\"_268\":2073,\"_270\":-7,\"_271\":2123,\"_11\":-7,\"_189\":2124,\"_188\":192},6378711,\"涓庡叾浠栨垚鍛樺叡鐢ㄦ暟鎹簱杩炴帴閰嶇疆\",\"doc.6378711\",[],\"/6378711m0\",{\"_179\":263,\"_185\":2126,\"_18\":2127,\"_266\":2128,\"_268\":2073,\"_270\":-7,\"_271\":2129,\"_11\":-7,\"_189\":2130,\"_188\":192},6378956,\"閫氳繃 CLI 杩愯鍖呭惈浜戠鏁版嵁搴撹繛鎺ラ厤缃殑娴嬭瘯鍦烘櫙\",\"doc.6378956\",[],\"/6378956m0\",{\"_179\":263,\"_185\":2132,\"_18\":2133,\"_266\":2134,\"_268\":2073,\"_270\":-7,\"_271\":2135,\"_11\":-7,\"_189\":2136,\"_188\":192},6379146,\"閫氳繃 Runner 杩愯鍖呭惈浜戠鏁版嵁搴撹繛鎺ラ厤缃殑娴嬭瘯鍦烘櫙\",\"doc.6379146\",[],\"/6379146m0\",{\"_179\":263,\"_185\":2138,\"_18\":2139,\"_266\":2140,\"_268\":2073,\"_270\":-7,\"_271\":2141,\"_11\":-7,\"_189\":2142,\"_188\":192},6822783,\"Apifox 娴嬭瘯姝ラ涔嬮棿鎬庝箞浼犻€掓暟鎹紵\",\"doc.6822783\",[],\"/6822783m0\",{\"_179\":279,\"_280\":269,\"_185\":2144,\"_18\":2145,\"_266\":2146,\"_284\":2147,\"_210\":2148,\"_268\":269,\"_270\":-7,\"_19\":-7,\"_308\":279,\"_11\":-7,\"_189\":136,\"_188\":192,\"_309\":192},********,\"璐﹀彿\u0026搴旂敤璁剧疆\",\"apiDetailFolder.********\",{\"_286\":287,\"_288\":289},[2149,2155,2161,2167,2173,2179,2185,2191,2197],{\"_179\":263,\"_185\":2150,\"_18\":2151,\"_266\":2152,\"_268\":2144,\"_270\":-7,\"_271\":2153,\"_11\":-7,\"_189\":2154,\"_188\":192},5722982,\"璐﹀彿璁剧疆\",\"doc.5722982\",[],\"/account-settings\",{\"_179\":263,\"_185\":2156,\"_18\":2157,\"_266\":2158,\"_268\":2144,\"_270\":-7,\"_271\":2159,\"_11\":-7,\"_189\":2160,\"_188\":192},5723694,\"API 璁块棶浠ょ墝\",\"doc.5723694\",[],\"/api-access-token\",{\"_179\":263,\"_185\":2162,\"_18\":2163,\"_266\":2164,\"_268\":2144,\"_270\":-7,\"_271\":2165,\"_11\":-7,\"_189\":2166,\"_188\":192},7381499,\"閫氱煡\",\"doc.7381499\",[],\"/notifications\",{\"_179\":263,\"_185\":2168,\"_18\":2169,\"_266\":2170,\"_268\":2144,\"_270\":-7,\"_271\":2171,\"_11\":-7,\"_189\":2172,\"_188\":192},5722984,\"璇█璁剧疆\",\"doc.5722984\",[],\"/language-settings\",{\"_179\":263,\"_185\":2174,\"_18\":2175,\"_266\":2176,\"_268\":2144,\"_270\":-7,\"_271\":2177,\"_11\":-7,\"_189\":2178,\"_188\":192},5722988,\"蹇嵎閿甛",\"doc.5722988\",[],\"/keyboard-shortcuts\",{\"_179\":263,\"_185\":2180,\"_18\":2181,\"_266\":2182,\"_268\":2144,\"_270\":-7,\"_271\":2183,\"_11\":-7,\"_189\":2184,\"_188\":192},5722987,\"缃戠粶浠ｇ悊\",\"doc.5722987\",[],\"/network-proxy\",{\"_179\":263,\"_185\":2186,\"_18\":2187,\"_266\":2188,\"_268\":2144,\"_270\":-7,\"_271\":2189,\"_11\":-7,\"_189\":2190,\"_188\":192},5722985,\"鏁版嵁澶囦唤涓庢仮澶峔",\"doc.5722985\",[],\"/data-backup-restore\",{\"_179\":263,\"_185\":2192,\"_18\":2193,\"_266\":2194,\"_268\":2144,\"_270\":-7,\"_271\":2195,\"_11\":-7,\"_189\":2196,\"_188\":192},5722990,\"鏇存柊 Apifox\",\"doc.5722990\",[],\"/update-apifox\",{\"_179\":263,\"_185\":2198,\"_18\":2199,\"_266\":2200,\"_268\":2144,\"_270\":-7,\"_271\":2201,\"_11\":-7,\"_189\":2202,\"_188\":192},6758815,\"瀹為獙鎬у姛鑳絓",\"doc.6758815\",[],\"/experimental-features\",{\"_179\":279,\"_280\":269,\"_185\":2204,\"_18\":2205,\"_266\":2206,\"_284\":2207,\"_210\":2208,\"_268\":269,\"_270\":-7,\"_19\":-7,\"_308\":279,\"_11\":-7,\"_189\":136,\"_188\":192,\"_309\":192},47820313,\"韬唤楠岃瘉 \u0026 Auth 閴存潈鎸囧崡\",\"apiDetailFolder.47820313\",{\"_286\":287,\"_288\":289},[2209,2215,2221,2227,2233,2239,2245],{\"_179\":263,\"_185\":2210,\"_18\":2211,\"_266\":2212,\"_268\":2204,\"_270\":-7,\"_271\":2213,\"_11\":-7,\"_189\":2214,\"_188\":192},5734556,\"浠€涔堟槸 API Key\",\"doc.5734556\",[],\"/what-is-api-key\",{\"_179\":263,\"_185\":2216,\"_18\":2217,\"_266\":2218,\"_268\":2204,\"_270\":-7,\"_271\":2219,\"_11\":-7,\"_189\":2220,\"_188\":192},5734558,\"浠€涔堟槸 Bearer Token\",\"doc.5734558\",[],\"/what-is-bearer-token\",{\"_179\":263,\"_185\":2222,\"_18\":2223,\"_266\":2224,\"_268\":2204,\"_270\":-7,\"_271\":2225,\"_11\":-7,\"_189\":2226,\"_188\":192},5734559,\"浠€涔堟槸 JWT\",\"doc.5734559\",[],\"/what-is-jwt\",{\"_179\":263,\"_185\":2228,\"_18\":2229,\"_266\":2230,\"_268\":2204,\"_270\":-7,\"_271\":2231,\"_11\":-7,\"_189\":2232,\"_188\":192},5734560,\"浠€涔堟槸 Basic Auth\",\"doc.5734560\",[],\"/what-is-basic-auth\",{\"_179\":263,\"_185\":2234,\"_18\":2235,\"_266\":2236,\"_268\":2204,\"_270\":-7,\"_271\":2237,\"_11\":-7,\"_189\":2238,\"_188\":192},5734561,\"浠€涔堟槸 Digest Auth\",\"doc.5734561\",[],\"/what-is-digest-auth\",{\"_179\":263,\"_185\":2240,\"_18\":2241,\"_266\":2242,\"_268\":2204,\"_270\":-7,\"_271\":2243,\"_11\":-7,\"_189\":2244,\"_188\":192},5734563,\"浠€涔堟槸 OAuth 1.0\",\"doc.5734563\",[],\"/what-is-oauth-1\",{\"_179\":279,\"_280\":269,\"_185\":2246,\"_18\":2247,\"_266\":2248,\"_284\":2249,\"_210\":2250,\"_268\":2204,\"_270\":-7,\"_19\":-7,\"_308\":279,\"_11\":-7,\"_189\":136,\"_188\":192,\"_309\":192},47820756,\"浠€涔堟槸 OAuth 2.0\",\"apiDetailFolder.47820756\",{\"_286\":287,\"_288\":289},[2251,2256,2262,2268,2274,2280],{\"_179\":263,\"_185\":2252,\"_18\":2247,\"_266\":2253,\"_268\":2246,\"_270\":-7,\"_271\":2254,\"_11\":-7,\"_189\":2255,\"_188\":192},5734564,\"doc.5734564\",[],\"/what-is-oauth-2\",{\"_179\":263,\"_185\":2257,\"_18\":2258,\"_266\":2259,\"_268\":2246,\"_270\":-7,\"_271\":2260,\"_11\":-7,\"_189\":2261,\"_188\":192},5734566,\"鎺堟潈鐮佹巿鏉冪被鍨媆",\"doc.5734566\",[],\"/authorization-code-grant\",{\"_179\":263,\"_185\":2263,\"_18\":2264,\"_266\":2265,\"_268\":2246,\"_270\":-7,\"_271\":2266,\"_11\":-7,\"_189\":2267,\"_188\":192},5734568,\"鎺堟潈鐮佹巿鏉冪被鍨嬶紝甯︽湁 PKCE\",\"doc.5734568\",[],\"/authorization-code-grant-with-pkce\",{\"_179\":263,\"_185\":2269,\"_18\":2270,\"_266\":2271,\"_268\":2246,\"_270\":-7,\"_271\":2272,\"_11\":-7,\"_189\":2273,\"_188\":192},5734569,\"闅愬紡鎺堟潈绫诲瀷\",\"doc.5734569\",[],\"/implicit-grant\",{\"_179\":263,\"_185\":2275,\"_18\":2276,\"_266\":2277,\"_268\":2246,\"_270\":-7,\"_271\":2278,\"_11\":-7,\"_189\":2279,\"_188\":192},5734572,\"瀵嗙爜鍑瘉鎺堟潈绫诲瀷\",\"doc.5734572\",[],\"/password-credentials-grant\",{\"_179\":263,\"_185\":2281,\"_18\":2282,\"_266\":2283,\"_268\":2246,\"_270\":-7,\"_271\":2284,\"_11\":-7,\"_189\":2285,\"_188\":192},5734576,\"瀹㈡埛绔嚟璇佹巿鏉冪被鍨媆",\"doc.5734576\",[],\"/client-credentials-grant\",{\"_179\":279,\"_280\":269,\"_185\":2287,\"_18\":2288,\"_266\":2289,\"_284\":2290,\"_210\":2291,\"_268\":269,\"_270\":-7,\"_19\":-7,\"_308\":279,\"_11\":-7,\"_189\":136,\"_188\":192,\"_309\":192},47817194,\"鏈嶅姟涓庨殣绉佸崗璁甛",\"apiDetailFolder.47817194\",{\"_286\":287,\"_288\":289},[2292,2298,2304],{\"_179\":263,\"_185\":2293,\"_18\":2294,\"_266\":2295,\"_268\":2287,\"_270\":-7,\"_271\":2296,\"_11\":-7,\"_189\":2297,\"_188\":192},5734439,\"鏈嶅姟鍗忚\",\"doc.5734439\",[],\"/service-agreement\",{\"_179\":263,\"_185\":2299,\"_18\":2300,\"_266\":2301,\"_268\":2287,\"_270\":-7,\"_271\":2302,\"_11\":-7,\"_189\":2303,\"_188\":192},5734501,\"闅愮鍗忚\",\"doc.5734501\",[],\"/privacy-policy\",{\"_179\":263,\"_185\":2305,\"_18\":2306,\"_266\":2307,\"_268\":2287,\"_270\":-7,\"_271\":2308,\"_11\":-7,\"_189\":2309,\"_188\":192},5734506,\"鏈嶅姟绛夌骇鍗忚\",\"doc.5734506\",[],\"/sla\",{\"_179\":279,\"_280\":269,\"_185\":2311,\"_18\":2312,\"_266\":2313,\"_284\":2314,\"_210\":2315,\"_268\":269,\"_270\":-7,\"_19\":-7,\"_308\":279,\"_11\":-7,\"_189\":136,\"_188\":192,\"_309\":192},47720892,\"鍙傝€冭祫鏂橽",\"apiDetailFolder.47720892\",{\"_286\":287,\"_288\":289},[2316,2322,2328,2334,2340,2346,2352,2358,2364,2370,2376,2382,2388,2423,2452],{\"_179\":263,\"_185\":2317,\"_18\":2318,\"_266\":2319,\"_268\":2311,\"_270\":-7,\"_271\":2320,\"_11\":-7,\"_189\":2321,\"_188\":192},5723921,\"API 璁捐浼樺厛鐞嗗康\",\"doc.5723921\",[],\"/api-first-design\",{\"_179\":263,\"_185\":2323,\"_18\":2324,\"_266\":2325,\"_268\":2311,\"_270\":-7,\"_271\":2326,\"_11\":-7,\"_189\":2327,\"_188\":192},5723977,\"JSON Schema 浠嬬粛\",\"doc.5723977\",[],\"/json-schema\",{\"_179\":263,\"_185\":2329,\"_18\":2330,\"_266\":2331,\"_268\":2311,\"_270\":-7,\"_271\":2332,\"_11\":-7,\"_189\":2333,\"_188\":192},5725287,\"JSONPath 浠嬬粛\",\"doc.5725287\",[],\"/jsonpath\",{\"_179\":263,\"_185\":2335,\"_18\":2336,\"_266\":2337,\"_268\":2311,\"_270\":-7,\"_271\":2338,\"_11\":-7,\"_189\":2339,\"_188\":192},5725289,\"XPath 浠嬬粛\",\"doc.5725289\",[],\"/xpath\",{\"_179\":263,\"_185\":2341,\"_18\":2342,\"_266\":2343,\"_268\":2311,\"_270\":-7,\"_271\":2344,\"_11\":-7,\"_189\":2345,\"_188\":192},5725364,\"Apifox Markdown 璇硶\",\"doc.5725364\",[],\"/apifox-markdown\",{\"_179\":263,\"_185\":2347,\"_18\":2348,\"_266\":2349,\"_268\":2311,\"_270\":-7,\"_271\":2350,\"_11\":-7,\"_189\":2351,\"_188\":192},5725296,\"CSV 鏍煎紡瑙勮寖\",\"doc.5725296\",[],\"/csv\",{\"_179\":263,\"_185\":2353,\"_18\":2354,\"_266\":2355,\"_268\":2311,\"_270\":-7,\"_271\":2356,\"_11\":-7,\"_189\":2357,\"_188\":192},5725291,\"姝ｅ垯琛ㄨ揪寮廫",\"doc.5725291\",[],\"/regex\",{\"_179\":263,\"_185\":2359,\"_18\":2360,\"_266\":2361,\"_268\":2311,\"_270\":-7,\"_271\":2362,\"_11\":-7,\"_189\":2363,\"_188\":192},5725302,\"瀹夎 Java 鐜\",\"doc.5725302\",[],\"/install-java\",{\"_179\":263,\"_185\":2365,\"_18\":2366,\"_266\":2367,\"_268\":2311,\"_270\":-7,\"_271\":2368,\"_11\":-7,\"_189\":2369,\"_188\":192},5725306,\"Runner 杩愯鐜\",\"doc.5725306\",[],\"/runner-environment\",{\"_179\":263,\"_185\":2371,\"_18\":2372,\"_266\":2373,\"_268\":2311,\"_270\":-7,\"_271\":2374,\"_11\":-7,\"_189\":2375,\"_188\":192},5725305,\"甯歌缂栫▼璇█瀵瑰簲鐨勬暟鎹被鍨媆",\"doc.5725305\",[],\"/programming-data-types\",{\"_179\":263,\"_185\":2377,\"_18\":2378,\"_266\":2379,\"_268\":2311,\"_270\":-7,\"_271\":2380,\"_11\":-7,\"_189\":2381,\"_188\":192},5725299,\"Socket 绮樺寘鍜屽垎鍖呴棶棰榎",\"doc.5725299\",[],\"/socket-packet-fragmentation-and-merging\",{\"_179\":263,\"_185\":2383,\"_18\":2384,\"_266\":2385,\"_268\":2311,\"_270\":-7,\"_271\":2386,\"_11\":-7,\"_189\":2387,\"_188\":192},5835748,\"璇嶆眹琛╘",\"doc.5835748\",[],\"/glossary\",{\"_179\":279,\"_280\":269,\"_185\":2389,\"_18\":2390,\"_266\":2391,\"_284\":2392,\"_210\":2393,\"_268\":2311,\"_270\":-7,\"_19\":-7,\"_308\":279,\"_11\":-7,\"_189\":136,\"_188\":192,\"_309\":192},47721665,\"Apifox Swagger 鎵╁睍\",\"apiDetailFolder.47721665\",{\"_286\":287,\"_288\":289},[2394,2399,2405,2411,2417],{\"_179\":263,\"_185\":2395,\"_18\":347,\"_266\":2396,\"_268\":2389,\"_270\":-7,\"_271\":2397,\"_11\":-7,\"_189\":2398,\"_188\":192},5723948,\"doc.5723948\",[],\"/apifox-openapi-swagger-extension\",{\"_179\":263,\"_185\":2400,\"_18\":2401,\"_266\":2402,\"_268\":2389,\"_270\":-7,\"_271\":2403,\"_11\":-7,\"_189\":2404,\"_188\":192},5723950,\"x-apifox-folder\",\"doc.5723950\",[],\"/x-apifox-folder\",{\"_179\":263,\"_185\":2406,\"_18\":2407,\"_266\":2408,\"_268\":2389,\"_270\":-7,\"_271\":2409,\"_11\":-7,\"_189\":2410,\"_188\":192},5723952,\"x-apifox-status\",\"doc.5723952\",[],\"/x-apifox-status\",{\"_179\":263,\"_185\":2412,\"_18\":2413,\"_266\":2414,\"_268\":2389,\"_270\":-7,\"_271\":2415,\"_11\":-7,\"_189\":2416,\"_188\":192},5723955,\"x-apifox-name\",\"doc.5723955\",[],\"/x-apifox-name\",{\"_179\":263,\"_185\":2418,\"_18\":2419,\"_266\":2420,\"_268\":2389,\"_270\":-7,\"_271\":2421,\"_11\":-7,\"_189\":2422,\"_188\":192},5723957,\"x-apifox-maintainer\",\"doc.5723957\",[],\"/x-apifox-maintainer\",{\"_179\":279,\"_280\":269,\"_185\":2424,\"_18\":2425,\"_266\":2426,\"_284\":2427,\"_210\":2428,\"_268\":2311,\"_270\":-7,\"_19\":-7,\"_308\":279,\"_11\":-7,\"_189\":136,\"_188\":192,\"_309\":192},47721766,\"Apifox JSON Schema 鎵╁睍\",\"apiDetailFolder.47721766\",{\"_286\":287,\"_288\":289},[2429,2434,2440,2446],{\"_179\":263,\"_185\":2430,\"_18\":347,\"_266\":2431,\"_268\":2424,\"_270\":-7,\"_271\":2432,\"_11\":-7,\"_189\":2433,\"_188\":192},5723963,\"doc.5723963\",[],\"/apifox-json-schema-extension\",{\"_179\":263,\"_185\":2435,\"_18\":2436,\"_266\":2437,\"_268\":2424,\"_270\":-7,\"_271\":2438,\"_11\":-7,\"_189\":2439,\"_188\":192},5723969,\"x-apifox-mock\",\"doc.5723969\",[],\"/x-apifox-mock\",{\"_179\":263,\"_185\":2441,\"_18\":2442,\"_266\":2443,\"_268\":2424,\"_270\":-7,\"_271\":2444,\"_11\":-7,\"_189\":2445,\"_188\":192},5723972,\"x-apifox-orders\",\"doc.5723972\",[],\"/x-apifox-orders\",{\"_179\":263,\"_185\":2447,\"_18\":2448,\"_266\":2449,\"_268\":2424,\"_270\":-7,\"_271\":2450,\"_11\":-7,\"_189\":2451,\"_188\":192},5723964,\"x-apifox-enum\",\"doc.5723964\",[],\"/x-apifox-enum\",{\"_179\":279,\"_280\":2453,\"_185\":2454,\"_18\":2455,\"_266\":2456,\"_284\":2457,\"_210\":2460,\"_268\":2311,\"_270\":-7,\"_19\":-7,\"_308\":279,\"_11\":-7,\"_189\":2461,\"_188\":192,\"_309\":192},5589656,46262793,\"鍔ㄦ€佸€艰〃杈惧紡\",\"apiDetailFolder.46262793\",{\"_286\":2458,\"_288\":2459},\"GO_TO_THE_SUBFOLDER\",\"CUSTOM_CONTENT\",[],\"/46262793f0\",{\"_179\":279,\"_280\":269,\"_185\":2463,\"_18\":195,\"_266\":2464,\"_284\":2465,\"_210\":2466,\"_268\":269,\"_270\":-7,\"_19\":-7,\"_308\":279,\"_11\":-7,\"_189\":2467,\"_188\":192,\"_309\":192},49049400,\"apiDetailFolder.49049400\",{\"_286\":2458,\"_288\":289},[],\"/5835927m0\",\"selectedSidebarTreeNode\",{\"_179\":263,\"_185\":264,\"_18\":265,\"_266\":267,\"_268\":269,\"_270\":-7,\"_271\":272,\"_11\":-7,\"_189\":190,\"_188\":47},\"parentSidebarTreeApiFolderNodes\",[],\"previousSidebarTreeNode\",\"nextSidebarTreeNode\",{\"_179\":263,\"_185\":274,\"_18\":218,\"_266\":275,\"_268\":269,\"_270\":-7,\"_271\":276,\"_11\":-7,\"_189\":277,\"_188\":192},\"docsBaseConfig\",{\"_26\":2477,\"_2478\":2477,\"_19\":2479,\"_2480\":2481,\"_2485\":13,\"_2486\":2487,\"_2498\":30,\"_2499\":2500,\"_2501\":47,\"_2502\":192,\"_2503\":192,\"_2504\":192,\"_2505\":192,\"_2506\":192,\"_2507\":2508,\"_2509\":192,\"_2510\":192,\"_2511\":192,\"_2512\":47,\"_2513\":192,\"_2514\":192,\"_2515\":192,\"_2516\":192,\"_2517\":2518,\"_2520\":2521,\"_2523\":161,\"_2524\":2525,\"_2529\":2530,\"_2532\":192,\"_2533\":47,\"_2534\":47,\"_2535\":2536,\"_2537\":2538,\"_2541\":47,\"_2542\":47,\"_2543\":192,\"_2544\":192,\"_2545\":192,\"_2546\":-7},\"Apifox 甯姪鏂囨。\",\"siteName\",\"鏂扮増甯姪鏂囨。\",\"logoSettings\",{\"_2482\":2483,\"_2484\":2483},\"light\",\"https://cdn.apifox.com/app/project-icon/custom/20240903/5960e2f3-e538-486f-9c67-a2710de3b7b0.png\",\"dark\",\"favicon\",\"themePrimarySettings\",{\"_2482\":2488,\"_2484\":2496},{\"_2489\":2490,\"_2491\":2492,\"_2493\":2494,\"_2495\":2490},\"primaryColor\",\"purple\",\"textColor\",\"WHITE\",\"backgroundColor\",\"#ffffff\",\"accentColor\",{\"_2489\":2490,\"_2491\":2492,\"_2493\":2497,\"_2495\":2490},\"#1a1922\",\"language\",\"theme\",\"system\",\"displayThemeToggle\",\"isShowPrefixUrl\",\"isShowMaintainer\",\"isShowTags\",\"isShowUpdatedAt\",\"isShowLastModifiedAt\",\"displayFieldIds\",[],\"hidePoweredBy\",\"hideBuiltWith\",\"hideRunInAppButton\",\"hideTryItOutButton\",\"exportable\",\"hideCloneButton\",\"hideMyAppButton\",\"hideAppFormatExportOption\",\"googleAnalytics\",{\"_2519\":136},\"measurementId\",\"visibilitySettings\",{\"_179\":2522},\"PUBLIC\",\"apiDocLayout\",\"mockRule\",{\"_2526\":2527,\"_2528\":47},\"rules\",[],\"enableSystemRule\",\"corsProxySettings\",{\"_185\":2531},4,\"enableVibeCodingViaMCP\",\"enableCopyPage\",\"enableLLMs\",\"globalMetadata\",\"[\\n  {\\\"name\\\": \\\"description\\\", \\\"content\\\": \\\"{{DESCRIPTION}}\\\"},\\n  {\\\"name\\\": \\\"keywords\\\", \\\"content\\\": \\\"{{KEYWORDS}}\\\"}\\n]\",\"robotsFile\",{\"_2539\":47,\"_20\":2540},\"enable\",\"User-Agent: *\\nAllow: /\\n\\nSitemap: {{SITEMAP_URL}}\",\"enableSitemapFile\",\"persistEnvVariables\",\"showDataSchemas\",\"showDataSchemasExample\",\"showDataSchemasAutoGenerateExample\",\"landingPageType\",\"versionList\",[2549],{\"_185\":2550,\"_18\":2551,\"_188\":47,\"_189\":259,\"_191\":192},\"4759753\",\"latest\",\"resourceData\",{\"_179\":263,\"_2554\":2555,\"_2573\":2574},\"data\",{\"_185\":264,\"_18\":265,\"_2556\":136,\"_20\":2557,\"_2558\":269,\"_2559\":2560,\"_2561\":2562,\"_2563\":2564,\"_2565\":2566,\"_2567\":2568,\"_2569\":2568,\"_179\":136,\"_271\":2570,\"_2571\":2572},\"sidebarTitle\",\"娆㈣繋鏉ュ埌 [Apifox](https://apifox.com/) 甯姪鏂囨。锛佽繖浠芥寚鍗楁棬鍦ㄥ府鍔╀綘楂樻晥鎺屾彙鍜屼娇鐢?Apifox锛屼綘鍙互閫氳繃浠ヤ笅涓婚蹇€熷畾浣嶆墍闇€淇℃伅銆俓\n\\n## 鍏ラ棬\\n\\n\u003cCardGroup cols={3}\u003e\\n    \u003cCard\u003e\\n      \u003cimg src=\\\"https://api.apifox.com/api/v1/projects/5097254/resources/505412/image-preview\\\" style=\\\"background-color: transparent; width: 64px\\\"/\u003e\\n\u003cp style=\\\"font-size: 24px; font-weight: 600; display: block; margin: 20px 0; line-height: 1.2;\\\"\u003e浠嬬粛\u003c/p\u003e\\n\\n    瀛︿範 Apifox 鐨勫熀鏈敤娉曞拰鍩虹鐭ヨ瘑銆俓\n        \\n    - [浠嬬粛 Apifox](/introduction)\\n    - [椤甸潰甯冨眬](/page-layout)\\n    - [鍩烘湰姒傚康](/apifox-basic-concepts)\\n    \u003c/Card\u003e\\n    \\n    \u003cCard\u003e\\n      \u003cimg src=\\\"https://api.apifox.com/api/v1/projects/5097254/resources/488414/image-preview\\\" style=\\\"background-color: transparent; width: 64px\\\"/\u003e\\n        \\n\u003cp style=\\\"font-size: 24px; font-weight: 600; display: block; margin: 20px 0; line-height: 1.2;\\\"\u003e蹇€熶笂鎵媆u003c/p\u003e\\n\\n    浣跨敤 Apifox 璁捐鍜屾祴璇曚綘鐨勭涓€涓帴鍙ｃ€俓\n\\n    - [鏂板缓鎺ュ彛](/create-api)\\n        \\n    - [鍙戦€佹帴鍙ｈ姹俔(/send-api-request)\\n    - [鏂板缓娴嬭瘯鍦烘櫙](/create-test-scenario)\\n\u003c/Card\u003e\\n  \u003c!--  \\n    \u003cCard\u003e\\n                  \u003cimg src=\\\"https://api.apifox.com/api/v1/projects/5097254/resources/488415/image-preview\\\" style=\\\"background-color: transparent; width: 64px\\\"/\u003e\\n\\n\\n    \u003cp style=\\\"font-size: 24px; font-weight: 600; display: block; margin: 20px 0; line-height: 1.2;\\\"\u003e绠€鏄庢墜鍐孿u003c/p\u003e\\n\\n            鍚勮鑹插浣曢珮鏁堜娇鐢?Apifox銆俓\n\\n                - [API 璁捐鑰咃細璁捐 API](/5093626m0)\\n                - [鍚庣寮€鍙戯細璋冭瘯 API](/5093741m0)\\n                - [鍓嶇寮€鍙戯細Mock API](/5093821m0)\\n        - [娴嬭瘯宸ョ▼甯堬細娴嬭瘯 API](/5094372m0)\\n\\n    \u003c/Card\u003e\\n--\u003e\\n    \u003cCard\u003e\\n      \u003cimg src=\\\"https://api.apifox.com/api/v1/projects/5097254/resources/488416/image-preview\\\" style=\\\"background-color: transparent; width: 64px\\\"/\u003e\\n\u003cp style=\\\"font-size: 24px; font-weight: 600; display: block; margin: 20px 0; line-height: 1.2;\\\"\u003e鍥㈤槦鍏ラ┗鎸囧崡\u003c/p\u003e\\n\\n    浠庡叾浠栧伐鍏疯縼绉绘暟鎹埌 Apifox锛屼互鍙婂洟闃熺鐞嗐€俓\n\\n    - [浠?Postman 杩佺Щ](/import-postman)\\n    - [瀵煎叆 OAS/Swagger](/import-openapi-swagger)\\n    - [鍥㈤槦鍏ラ┗涓庡崗浣淽(/onboard-team)\\n\u003c/Card\u003e\\n\u003c/CardGroup\u003e\\n\\n## 鎺㈢储 Apifox\\n\\n\u003cCardGroup cols={3}\u003e    \\n    \u003cCard\u003e\\n      \u003cimg src=\\\"https://api.apifox.com/api/v1/projects/5097254/resources/488424/image-preview\\\" style=\\\"background-color: transparent; width: 64px\\\"/\u003e\\n        \\n\u003cp style=\\\"font-size: 24px; font-weight: 600; display: block; margin: 20px 0; line-height: 1.2;\\\"\u003e鍙戦€佽姹俓u003c/p\u003e\\n\\n    鍙戦€?HTTP銆乬RPC銆丏ubbo銆乄ebSocket 绛夎姹傘€俓\n\\n    - [HTTP 璇锋眰](/quick-request)\\n    - [GraphQL](/graphql)\\n    - [gRPC](/grpc)\\n\u003c/Card\u003e\\n\\n    \u003cCard\u003e\\n\\n      \u003cimg src=\\\"https://api.apifox.com/api/v1/projects/5097254/resources/488425/image-preview\\\" style=\\\"background-color: transparent; width: 64px\\\"/\u003e\\n        \\n        \\n\u003cp style=\\\"font-size: 24px; font-weight: 600; display: block; margin: 20px 0; line-height: 1.2;\\\"\u003e璁捐 API\u003c/p\u003e\\n    \\n    \\n    鍦ㄦ敮鎸?JSON Schema 鐨勫己澶у彲瑙嗗寲缁勪欢涓璁?API銆俓\n    - [鍙鍖?OAS 璁捐](/create-an-api)\\n    - [浣跨敤鏁版嵁妯″瀷](/data-schemas)\\n    - [鎺ュ彛鍘嗗彶璁板綍](/api-history)\\n\u003c/Card\u003e\\n\\n    \u003cCard\u003e\\n\\n      \u003cimg src=\\\"https://api.apifox.com/api/v1/projects/5097254/resources/488426/image-preview\\\" style=\\\"background-color: transparent; width: 64px\\\"/\u003e\\n        \\n\u003cp style=\\\"font-size: 24px; font-weight: 600; display: block; margin: 20px 0; line-height: 1.2;\\\"\u003e璋冭瘯 API\u003c/p\u003e\\n\\n    鍙戦€?API 璇锋眰锛岃嚜鍔ㄦ牎楠岃繑鍥炲搷搴斻€俓\n    - [鏍￠獙鍝嶅簲](/validate-response)\\n    - [鍓嶅悗缃搷浣淽(/pre-post-processors)\\n    - [鏁版嵁搴撴搷浣淽(/database)\\n\u003c/Card\u003e\\n\\n    \u003cCard\u003e\\n\\n      \u003cimg src=\\\"https://api.apifox.com/api/v1/projects/5097254/resources/488427/image-preview\\\" style=\\\"background-color: transparent; width: 64px\\\"/\u003e\\n\\n\u003cp style=\\\"font-size: 24px; font-weight: 600; display: block; margin: 20px 0; line-height: 1.2;\\\"\u003e娴嬭瘯 API\u003c/p\u003e\\n\\n    鏋勫缓鏀寔寰幆銆佹潯浠跺垎鏀瓑鍔熻兘鐨勬祴璇曞満鏅€俓\n\\n    - [鍙鍖栫紪鎺抅(/new-test-scenario)\\n    - [瀹氭椂浠诲姟](/scheduled-tasks)\\n    - [鎬ц兘娴嬭瘯](/performance-testing)\\n\u003c/Card\u003e\\n\\n    \u003cCard\u003e\\n      \u003cimg src=\\\"https://api.apifox.com/api/v1/projects/5097254/resources/488428/image-preview\\\" style=\\\"background-color: transparent; width: 64px\\\"/\u003e\\n\\n\u003cp style=\\\"font-size: 24px; font-weight: 600; display: block; margin: 20px 0; line-height: 1.2;\\\"\u003eMock API\u003c/p\u003e\\n\\n鐢ㄧ湡瀹?Mock 鏁版嵁鍦ㄦ帴鍙ｆ湭瀹屾垚鍓嶅紑鍙戝墠绔姛鑳姐€俓\n\\n    - [鏅鸿兘 Mock](/smart-mock)\\n    - [浜戠 Mock](/cloud-mock)\\n    - [Mock 鏈熸湜](/custom-mock)\\n\u003c/Card\u003e\\n\\n    \u003cCard\u003e\\n\\n      \u003cimg src=\\\"https://api.apifox.com/api/v1/projects/5097254/resources/488429/image-preview\\\" style=\\\"background-color: transparent; width: 64px\\\"/\u003e\\n        \\n\u003cp style=\\\"font-size: 24px; font-weight: 600; display: block; margin: 20px 0; line-height: 1.2;\\\"\u003e鍙戝竷 API 鏂囨。\u003c/p\u003e\\n\\n    涓€閿垎浜?鍙戝竷缇庤鐨?API 鏂囨。銆俓\n    \\n    - [鍒嗕韩 API 鏂囨。](/quick-share)\\n    - [鍙戝竷鏂囨。绔橾(/publish-documentation-site)\\n    - [鑷畾涔夐〉闈㈠竷灞€](/page-layout-settings)\\n\u003c/Card\u003e\\n    \u003cCard\u003e\\n\\n      \u003cimg src=\\\"https://api.apifox.com/api/v1/projects/5097254/resources/488430/image-preview\\\" style=\\\"background-color: transparent; width: 64px\\\"/\u003e\\n        \\n\u003cp style=\\\"font-size: 24px; font-weight: 600; display: block; margin: 20px 0; line-height: 1.2;\\\"\u003e杩唬鍒嗘敮\u003c/p\u003e\\n\\n    杩唬鍒嗘敮鍔熻兘鍔╁姏鍥㈤槦杩涜鍒嗘敮鍗忎綔銆俓\n\\n    - [鏂板缓杩唬鍒嗘敮](/create-sprint-branch)\\n    - [鍦ㄥ垎鏀腑娴嬭瘯鎺ュ彛](/test-api-in-sprint-branch)\\n    - [鍚堝苟杩唬鍒嗘敮](/merge-sprint-branch)\\n\u003c/Card\u003e\\n    \u003cCard\u003e\\n      \u003cimg src=\\\"https://api.apifox.com/api/v1/projects/5097254/resources/488431/image-preview\\\" style=\\\"background-color: transparent; width: 64px\\\"/\u003e\\n        \\n\u003cp style=\\\"font-size: 24px; font-weight: 600; display: block; margin: 20px 0; line-height: 1.2;\\\"\u003e鍥㈤槦涓庨」鐩甛u003c/p\u003e\\n\\n    鏀寔缁勭粐銆佸洟闃熴€侀」鐩殑绠＄悊锛屽苟鎻愪緵鐏垫椿鐨勬潈闄愰厤缃€俓\n    \\n    - [鍥㈤槦涓庨」鐩甝(/management-center)\\n    - [鎴愬憳鏉冮檺](/member-roles-and-permissions)\\n    - [缁勭粐鍜屽崟鐐圭櫥褰昡(/sso)\\n    \\n\u003c/Card\u003e\\n    \u003cCard\u003e\\n\\n      \u003cimg src=\\\"https://api.apifox.com/api/v1/projects/5097254/resources/488432/image-preview\\\" style=\\\"background-color: transparent; width: 64px\\\"/\u003e\\n        \\n\u003cp style=\\\"font-size: 24px; font-weight: 600; display: block; margin: 20px 0; line-height: 1.2;\\\"\u003eIDEA 鎻掍欢\u003c/p\u003e\\n\\n    閫氳繃 IDEA 鎻掍欢鑷姩鐢熸垚 API 鏂囨。骞朵竴閿悓姝ュ埌 Apifox銆俓\n    \\n    - [瀹夎 IDEA 鎻掍欢](/apifox-idea-plugin)\\n    - [鐢熸垚鎺ュ彛鏂囨。](/generate-api-docs-with-idea)\\n    - [鐢熸垚鏁版嵁妯″瀷](/generate-data-schemas-with-idea)\\n    \u003c/Card\u003e\\n\u003c/CardGroup\u003e\\n\\n## 鏇村\\n\\n\\n\u003cCardGroup cols={3}\u003e\\n    \u003cCard href=\\\"https://apifox.com/siyouhua\\\"\u003e\\n      \u003cimg src=\\\"https://api.apifox.com/api/v1/projects/5097254/resources/488433/image-preview\\\" style=\\\"background-color: transparent; width: 64px\\\"/\u003e\\n\\n        **Apifox 绉佹湁鍖栭儴缃?*\\n\\n\\n\u003c/Card\u003e\\n    \u003cCard href=\\\"https://apifox-openapi.apifox.cn/\\\"\u003e\\n              \u003cimg src=\\\"https://api.apifox.com/api/v1/projects/5097254/resources/488434/image-preview\\\" style=\\\"background-color: transparent; width: 64px;border-radius: 14px; \\\"/\u003e\\n\\n**Apifox 寮€鏀?API**\\n\\n\\n\u003c/Card\u003e\\n    \u003cCard href=\\\"https://markdown.apifox.cn/\\\"\u003e\\n      \u003cimg src=\\\"https://api.apifox.com/api/v1/projects/5097254/resources/488435/image-preview\\\" style=\\\"background-color: transparent; width: 64px\\\"/\u003e\\n        \\n**Apifox Markdown**\\n\\n\u003c/Card\u003e\\n\u003c/CardGroup\u003e\",\"folderId\",\"projectId\",5097254,\"moduleId\",5128887,\"createdAt\",\"2025-02-21T06:01:29.000Z\",\"updatedAt\",\"2025-04-09T09:19:13.000Z\",\"creatorId\",1558083,\"editorId\",[],\"visibility\",\"INHERITED\",\"extraData\",{\"_2575\":2576,\"_2584\":2585},\"apiFieldList\",[2577],{\"_179\":2565,\"_266\":2578,\"_18\":2579,\"_2580\":2566,\"_2581\":2582,\"_2583\":47},\"updateAt\",\"鏈€鍚庝慨鏀规椂闂达細\",\"value\",\"format\",\"2025-04-09 09:19:13\",\"hidden\",\"dataSchemaDefinitions\",{},\"docsIdTypeData\",{\"_2588\":2589,\"_2590\":2591,\"_2559\":2560,\"_2592\":2593,\"_2594\":2595,\"_2596\":2597,\"_2598\":136,\"_2599\":-7},\"onlineType\",\"APIDOC\",\"onlineId\",5431806,\"teamId\",407061,\"branchId\",4759753,\"visitType\",\"customDomain\",\"subdirectory\",\"specialFileType\",\"notification\",[],\"footerBanner\",[],\"projectSetting\",{\"_2606\":2607,\"_2608\":2609,\"_2629\":2630,\"_2631\":2632,\"_2633\":2634,\"_2635\":2636},\"auth\",{},\"advancedSettings\",{\"_2610\":192,\"_2611\":192,\"_2612\":47,\"_2613\":2614,\"_2615\":192,\"_2616\":192,\"_2617\":2618,\"_2623\":2624},\"enableJsonc\",\"enableBigint\",\"responseValidate\",\"isDefaultUrlEncoding\",2,\"enableTestScenarioSetting\",\"enableYAPICompatScript\",\"publishedDocUrlRules\",{\"_2619\":2620,\"_2621\":2622},\"defaultRule\",\"RESOURCE_KEY_ONLY\",\"resourceKeyStandard\",\"NEW\",\"folderShareExpandModeSettings\",{\"_2625\":2626,\"_2627\":2628},\"expandId\",[],\"mode\",\"AUTO\",\"securityScheme\",{},\"mockSettings\",{},\"preProcessors\",[],\"postProcessors\",[],\"environments\",{\"_2639\":-7,\"_2640\":2641,\"_2642\":2643},\"selectedEnvironment\",\"environmentList\",[],\"servers\",[2644],{\"_185\":2645,\"_18\":2646},\"default\",\"榛樿鏈嶅姟\",\"searchSettings\",{\"_179\":2649,\"_2650\":2651,\"_2652\":2653,\"_2654\":2655,\"_2656\":2657,\"_2658\":2659,\"_2660\":2659,\"_2661\":2662,\"_2663\":2664,\"_2665\":2666},\"ALGOLIA\",\"appId\",\"ERNX5G1RJT\",\"searchApiKey\",\"********************************\",\"indexName\",\"xmt2ucqlpg-apifox\",\"algoliaDocSearchRebuiltAt\",\"2025-09-19T06:56:49.461Z\",\"algoliaRebuildStatus\",\"REBUILT\",\"localSearchRebuildStatus\",\"localSearchRebuiltAt\",\"2025-01-13T03:12:02.187Z\",\"localSearchIndexDataURL\",\"/api/v1/projects/5097254/docs-sites/5431806/search-index-data/preview?path=%2Fapp%2Fprojects%2F5097254%2Fdocs-sites%2F502c5bf32c8f87813ee6e03287ef331c%2Flocal-search-indexes%2Fwn50ds108y-apifox%2Fdata.json\",\"localSearchIndexSettingsURL\",\"/api/v1/projects/5097254/docs-sites/5431806/search-index-data/preview?path=%2Fapp%2Fprojects%2F5097254%2Fdocs-sites%2F502c5bf32c8f87813ee6e03287ef331c%2Flocal-search-indexes%2Fwn50ds108y-apifox%2Fsettings.json\",\"versionSettings\",[2669],{\"_2594\":2595,\"_2670\":47,\"_18\":2551,\"_2671\":2551,\"_2637\":2672},\"isDefaultVersion\",\"slug\",{\"_2673\":2674,\"_2675\":269},\"environmentIds\",[],\"defaultEnvironmentId\",\"seoInfos\",[\"M\",2678,2679,2689,2690,2694,2695,2700,2701,2704,2705,2709,2710,2714,2715,2719,2720,2726,2727,2731,2732,2736,2737,2741,2742,2746,2747,2751,2752,2756,2757,2761,2762,2766,2767,2771,2772,2776,2777,2781,2782,2786,2787,2791,2792,2796,2797,2801,2802,2806,2807,2811,2812,2816,2817,2821,2822,2826,2827,2831,2832,2836,2837,2841,2842,2846,2847,2851,2852,2856,2857,2861,2862,2866,2867,2871,2872,2876,2877,2881,2882,2886,2887,2891,2892,2896,2897,2901,2902,2906,2907,2911,2912,2916,2917,2921,2922,2926,2927,2931,2932,2936,2937,2941,2942,2946,2947,2951,2952,2956,2957,2961,2962,2966,2967,2971,2972,2976,2977,2981,2982,2986,2987,2991,2992,2996,2997,3001,3002,3006,3007,3011,3012,3016,3017,3021,3022,3026,3027,3031,3032,3036,3037,3041,3042,3046,3047,3051,3052,3056,3057,3061,3062,3066,3067,3071,3072,3076,3077,3081,3082,3086,3087,3091,3092,3096,3097,3101,3102,3106,3107,3111,3112,3116,3117,3121,3122,3126,3127,3131,3132,3136,3137,3141,3142,3146,3147,3151,3152,3156,3157,3161,3162,3166,3167,3171,3172,3176,3177,3181,3182,3186,3187,3191,3192,3196,3197,3201,3202,3206,3207,3211,3212,3216,3217,3221,3222,3226,3227,3231,3232,3236,3237,3241,3242,3246,3247,3251,3252,3256,3257,3261,3262,3266,3267,3271,3272,3276,3277,3281,3282,3286,3287,3291,3292,3296,3297,3301,3302,3306,3307,3311,3312,3316,3317,3321,3322,3325,3326,3330,3331,3335,3336,3340,3341,3345,3346,3350,3351,3355,3356,3360,3361,3364,3365,3368,3369,3372,3373,3376,3377,3381,3382,3385,3386,3389,3390,3393,3394,3399,3400,3404,3405,3409,3410,3414,3415,3419,3420,3424,3425,3429,3430,3434,3435,3439,3440,3444,3445,3449,3450,3454,3455,3459,3460,3464,3465,3469,3470,3474,3475,3479,3480,3484,3485,3489,3490,3494,3495,3499,3500,3504,3505,3509,3510,3514,3515,3519,3520,3524,3525,3529,3530,3534,3535,3539,3540,3544,3545,3549,3550,3554,3555,3559,3560,3564,3565,3569,3570,3574,3575,3579,3580,3584,3585,3589,3590,3594,3595,3599,3600,3604,3605,3609,3610,3614,3615,3619,3620,3624,3625,3629,3630,3634,3635,3639,3640,3644,3645,3649,3650,3654,3655,3659,3660,3664,3665,3669,3670,3674,3675,3679,3680,3684,3685,3689,3690,3694,3695,3699,3700,3704,3705,3709,3710,3714,3715,3719,3720,3724,3725,3729,3730,3734,3735,3739,3740,3744,3745,3749,3750,3754,3755,3759,3760,3764,3765,3769,3770,3774,3775,3779,3780,3784,3785,3789,3790,3794,3795,3799,3800,3804,3805,3809,3810,3814,3815,3819,3820,3824,3825,3829,3830,3834,3835,3839,3840,3844,3845,3849,3850,3854,3855,3859,3860,3864,3865,3869,3870,3874,3875,3879,3880,3884,3885,3889,3890,3894,3895,3899,3900,3904,3905,3909,3910,3914,3915,3919,3920,3924,3925,3929,3930,3934,3935,3939,3940,3944,3945,3949,3950,3954,3955,3959,3960,3964,3965,3969,3970,3974,3975,3979,3980,3984,3985,3989,3990,3994,3995,3999,4000,4004,4005,4009,4010,4014,4015,4019,4020,4024,4025,4029,4030,4034,4035,4039,4040,4044,4045,4049,4050],\"5092525:DOC\",{\"_185\":2680,\"_2681\":292,\"_2682\":2683,\"_2684\":2685,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":2687,\"_2559\":2560,\"_2688\":-5},596,\"relatedId\",\"relatedType\",\"DOC\",\"path\",\"introduction\",\"metaSocial\",{},\"customMeta\",\"6264407:DOC\",{\"_185\":2691,\"_2681\":2049,\"_2682\":2683,\"_2684\":2692,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":2693,\"_2559\":2560,\"_2688\":-5},3836,\"apifox-mcp-server\",{},\"5903141:DOC\",{\"_185\":2696,\"_2681\":2697,\"_2682\":2683,\"_2684\":2698,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":2699,\"_2559\":2560,\"_2688\":-5},4618,5903141,\"download-alpha\",{},\"5802858:DOC\",{\"_185\":2702,\"_2681\":264,\"_2682\":2683,\"_2684\":119,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":2703,\"_2559\":2560,\"_2688\":-5},4620,{},\"5807637:DOC\",{\"_185\":2706,\"_2681\":274,\"_2682\":2683,\"_2684\":2707,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":2708,\"_2559\":2560,\"_2688\":-5},4621,\"changelog\",{},\"5751209:DOC\",{\"_185\":2711,\"_2681\":298,\"_2682\":2683,\"_2684\":2712,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":2713,\"_2559\":2560,\"_2688\":-5},4622,\"contact-us\",{},\"5751301:DOC\",{\"_185\":2716,\"_2681\":304,\"_2682\":2683,\"_2684\":2717,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":2718,\"_2559\":2560,\"_2688\":-5},4623,\"siyouhua\",{},\"5135961:DOC\",{\"_185\":2721,\"_2681\":317,\"_2682\":2683,\"_2684\":2722,\"_26\":2723,\"_19\":2724,\"_23\":136,\"_2686\":2725,\"_2559\":2560,\"_2688\":-5},4624,\"download\",\"涓嬭浇 Apifox - Windows macOS Linux 妗岄潰鐗堜笅杞絓",\"Apifox Windows 妗岄潰鐗堜笅杞姐€乵acOS 妗岄潰鐗堜笅杞姐€丩inux 妗岄潰鐗堜笅杞姐€備綘鍙互鏍规嵁鎿嶄綔绯荤粺閫夋嫨鍚堥€傜殑瀹夎鍖咃紝骞跺湪瀹樼綉棣栭〉鎴栦笅鏂硅〃鏍间腑鐐瑰嚮涓嬭浇銆俓",{},\"5136040:DOC\",{\"_185\":2728,\"_2681\":322,\"_2682\":2683,\"_2684\":2729,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":2730,\"_2559\":2560,\"_2688\":136},6221,\"sign-up-and-login\",{},\"5146285:DOC\",{\"_185\":2733,\"_2681\":328,\"_2682\":2683,\"_2684\":2734,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":2735,\"_2559\":2560,\"_2688\":136},6222,\"page-layout\",{},\"5146286:DOC\",{\"_185\":2738,\"_2681\":334,\"_2682\":2683,\"_2684\":2739,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":2740,\"_2559\":2560,\"_2688\":136},6223,\"apifox-basic-concepts\",{},\"5135833:DOC\",{\"_185\":2743,\"_2681\":346,\"_2682\":2683,\"_2684\":2744,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":2745,\"_2559\":2560,\"_2688\":136},6225,\"getting-started\",{},\"5220199:DOC\",{\"_185\":2748,\"_2681\":407,\"_2682\":2683,\"_2684\":2749,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":2750,\"_2559\":2560,\"_2688\":136},6226,\"request-url-and-method\",{},\"5220200:DOC\",{\"_185\":2753,\"_2681\":413,\"_2682\":2683,\"_2684\":2754,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":2755,\"_2559\":2560,\"_2688\":136},6227,\"request-params-and-body\",{},\"5220201:DOC\",{\"_185\":2758,\"_2681\":419,\"_2682\":2683,\"_2684\":2759,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":2760,\"_2559\":2560,\"_2688\":136},6228,\"request-headers\",{},\"5220202:DOC\",{\"_185\":2763,\"_2681\":425,\"_2682\":2683,\"_2684\":2764,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":2765,\"_2559\":2560,\"_2688\":136},6229,\"request-param-encoding-decoding\",{},\"5220203:DOC\",{\"_185\":2768,\"_2681\":431,\"_2682\":2683,\"_2684\":2769,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":2770,\"_2559\":2560,\"_2688\":136},6230,\"http2\",{},\"5220207:DOC\",{\"_185\":2773,\"_2681\":443,\"_2682\":2683,\"_2684\":2774,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":2775,\"_2559\":2560,\"_2688\":136},6231,\"authentication-and-authorization\",{},\"5220209:DOC\",{\"_185\":2778,\"_2681\":448,\"_2682\":2683,\"_2684\":2779,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":2780,\"_2559\":2560,\"_2688\":136},6232,\"authorization-types\",{},\"5352149:DOC\",{\"_185\":2783,\"_2681\":454,\"_2682\":2683,\"_2684\":2784,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":2785,\"_2559\":2560,\"_2688\":136},6233,\"digest-auth\",{},\"5352166:DOC\",{\"_185\":2788,\"_2681\":460,\"_2682\":2683,\"_2684\":2789,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":2790,\"_2559\":2560,\"_2688\":136},6234,\"oauth1\",{},\"5352170:DOC\",{\"_185\":2793,\"_2681\":466,\"_2682\":2683,\"_2684\":2794,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":2795,\"_2559\":2560,\"_2688\":136},6235,\"oauth2\",{},\"5352172:DOC\",{\"_185\":2798,\"_2681\":472,\"_2682\":2683,\"_2684\":2799,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":2800,\"_2559\":2560,\"_2688\":136},6236,\"hawk-auth\",{},\"5352174:DOC\",{\"_185\":2803,\"_2681\":478,\"_2682\":2683,\"_2684\":2804,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":2805,\"_2559\":2560,\"_2688\":136},6237,\"kerberos\",{},\"5352176:DOC\",{\"_185\":2808,\"_2681\":484,\"_2682\":2683,\"_2684\":2809,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":2810,\"_2559\":2560,\"_2688\":136},6238,\"ntlm\",{},\"5352177:DOC\",{\"_185\":2813,\"_2681\":490,\"_2682\":2683,\"_2684\":2814,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":2815,\"_2559\":2560,\"_2688\":136},6239,\"akamai-edgegrid\",{},\"5220208:DOC\",{\"_185\":2818,\"_2681\":496,\"_2682\":2683,\"_2684\":2819,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":2820,\"_2559\":2560,\"_2688\":136},6240,\"ca-and-client-certificates\",{},\"5220211:DOC\",{\"_185\":2823,\"_2681\":508,\"_2682\":2683,\"_2684\":2824,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":2825,\"_2559\":2560,\"_2688\":136},6241,\"response-and-cookie\",{},\"5220212:DOC\",{\"_185\":2828,\"_2681\":513,\"_2682\":2683,\"_2684\":2829,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":2830,\"_2559\":2560,\"_2688\":136},6242,\"api-response\",{},\"5220214:DOC\",{\"_185\":2833,\"_2681\":519,\"_2682\":2683,\"_2684\":2834,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":2835,\"_2559\":2560,\"_2688\":136},6244,\"create-and-send-cookie\",{},\"5220215:DOC\",{\"_185\":2838,\"_2681\":525,\"_2682\":2683,\"_2684\":2839,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":2840,\"_2559\":2560,\"_2688\":136},6245,\"actual-request\",{},\"5220219:DOC\",{\"_185\":2843,\"_2681\":531,\"_2682\":2683,\"_2684\":2844,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":2845,\"_2559\":2560,\"_2688\":136},6246,\"extract-response-example\",{},\"5146296:DOC\",{\"_185\":2848,\"_2681\":567,\"_2682\":2683,\"_2684\":2849,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":2850,\"_2559\":2560,\"_2688\":136},6247,\"apihub\",{},\"5173624:DOC\",{\"_185\":2853,\"_2681\":578,\"_2682\":2683,\"_2684\":2854,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":2855,\"_2559\":2560,\"_2688\":136},6248,\"import-and-export\",{},\"5173626:DOC\",{\"_185\":2858,\"_2681\":583,\"_2682\":2683,\"_2684\":2859,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":2860,\"_2559\":2560,\"_2688\":136},6249,\"manual-import\",{},\"5173630:DOC\",{\"_185\":2863,\"_2681\":589,\"_2682\":2683,\"_2684\":2864,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":2865,\"_2559\":2560,\"_2688\":136},6250,\"scheduled-import\",{},\"5173637:DOC\",{\"_185\":2868,\"_2681\":595,\"_2682\":2683,\"_2684\":2869,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":2870,\"_2559\":2560,\"_2688\":136},6251,\"import-settings\",{},\"5173639:DOC\",{\"_185\":2873,\"_2681\":601,\"_2682\":2683,\"_2684\":2874,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":2875,\"_2559\":2560,\"_2688\":136},6252,\"export-data\",{},\"5174534:DOC\",{\"_185\":2878,\"_2681\":613,\"_2682\":2683,\"_2684\":2879,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":2880,\"_2559\":2560,\"_2688\":136},6253,\"import-openapi-swagger\",{},\"5174528:DOC\",{\"_185\":2883,\"_2681\":619,\"_2682\":2683,\"_2684\":2884,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":2885,\"_2559\":2560,\"_2688\":136},6254,\"import-postman\",{},\"5174546:DOC\",{\"_185\":2888,\"_2681\":625,\"_2682\":2683,\"_2684\":2889,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":2890,\"_2559\":2560,\"_2688\":136},6255,\"import-apipost\",{},\"5174555:DOC\",{\"_185\":2893,\"_2681\":631,\"_2682\":2683,\"_2684\":2894,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":2895,\"_2559\":2560,\"_2688\":136},6256,\"import-eolink\",{},\"5174539:DOC\",{\"_185\":2898,\"_2681\":637,\"_2682\":2683,\"_2684\":2899,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":2900,\"_2559\":2560,\"_2688\":136},6257,\"import-curl\",{},\"5174540:DOC\",{\"_185\":2903,\"_2681\":643,\"_2682\":2683,\"_2684\":2904,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":2905,\"_2559\":2560,\"_2688\":136},6258,\"import-markdown\",{},\"5174542:DOC\",{\"_185\":2908,\"_2681\":649,\"_2682\":2683,\"_2684\":2909,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":2910,\"_2559\":2560,\"_2688\":136},6259,\"import-insomnia\",{},\"5174544:DOC\",{\"_185\":2913,\"_2681\":655,\"_2682\":2683,\"_2684\":2914,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":2915,\"_2559\":2560,\"_2688\":136},6260,\"import-apidoc\",{},\"5174545:DOC\",{\"_185\":2918,\"_2681\":661,\"_2682\":2683,\"_2684\":2919,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":2920,\"_2559\":2560,\"_2688\":136},6261,\"import-har\",{},\"5811273:DOC\",{\"_185\":2923,\"_2681\":667,\"_2682\":2683,\"_2684\":2924,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":2925,\"_2559\":2560,\"_2688\":136},6262,\"import-knife4j\",{},\"5811290:DOC\",{\"_185\":2928,\"_2681\":673,\"_2682\":2683,\"_2684\":2929,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":2930,\"_2559\":2560,\"_2688\":136},6263,\"import-nei\",{},\"5811306:DOC\",{\"_185\":2933,\"_2681\":679,\"_2682\":2683,\"_2684\":2934,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":2935,\"_2559\":2560,\"_2688\":136},6264,\"import-docway\",{},\"5811315:DOC\",{\"_185\":2938,\"_2681\":685,\"_2682\":2683,\"_2684\":2939,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":2940,\"_2559\":2560,\"_2688\":136},6265,\"import-apizza\",{},\"5811330:DOC\",{\"_185\":2943,\"_2681\":691,\"_2682\":2683,\"_2684\":2944,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":2945,\"_2559\":2560,\"_2688\":136},6266,\"import-wsdl\",{},\"5220221:DOC\",{\"_185\":2948,\"_2681\":703,\"_2682\":2683,\"_2684\":2949,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":2950,\"_2559\":2560,\"_2688\":136},6267,\"design-api\",{},\"5220261:DOC\",{\"_185\":2953,\"_2681\":893,\"_2682\":2683,\"_2684\":2954,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":2955,\"_2559\":2560,\"_2688\":136},6268,\"develop-and-debug-api\",{},\"5589818:DOC\",{\"_185\":2958,\"_2681\":1252,\"_2682\":2683,\"_2684\":2959,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":2960,\"_2559\":2560,\"_2688\":136},6269,\"mock\",{},\"5615148:DOC\",{\"_185\":2963,\"_2681\":1299,\"_2682\":2683,\"_2684\":2964,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":2965,\"_2559\":2560,\"_2688\":136},6270,\"automated-testing\",{},\"5651467:DOC\",{\"_185\":2968,\"_2681\":1492,\"_2682\":2683,\"_2684\":2969,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":2970,\"_2559\":2560,\"_2688\":136},6271,\"api-documentation\",{},\"5218920:DOC\",{\"_185\":2973,\"_2681\":1156,\"_2682\":2683,\"_2684\":2974,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":2975,\"_2559\":2560,\"_2688\":136},6273,\"graphql\",{},\"5220190:DOC\",{\"_185\":2978,\"_2681\":1162,\"_2682\":2683,\"_2684\":2979,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":2980,\"_2559\":2560,\"_2688\":136},6274,\"websocket\",{},\"6107994:DOC\",{\"_185\":2983,\"_2681\":1168,\"_2682\":2683,\"_2684\":2984,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":2985,\"_2559\":2560,\"_2688\":136},6275,\"socketio\",{},\"5219195:DOC\",{\"_185\":2988,\"_2681\":1174,\"_2682\":2683,\"_2684\":2989,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":2990,\"_2559\":2560,\"_2688\":136},6276,\"sse\",{},\"5219198:DOC\",{\"_185\":2993,\"_2681\":1180,\"_2682\":2683,\"_2684\":2994,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":2995,\"_2559\":2560,\"_2688\":136},6277,\"soap\",{},\"5219191:DOC\",{\"_185\":2998,\"_2681\":1186,\"_2682\":2683,\"_2684\":2999,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3000,\"_2559\":2560,\"_2688\":136},6278,\"grpc\",{},\"5830401:DOC\",{\"_185\":3003,\"_2681\":1198,\"_2682\":2683,\"_2684\":3004,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3005,\"_2559\":2560,\"_2688\":136},6279,\"request-proxy-debugging\",{},\"5725400:DOC\",{\"_185\":3008,\"_2681\":1210,\"_2682\":2683,\"_2684\":3009,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3010,\"_2559\":2560,\"_2688\":136},6280,\"create-dubbo-api\",{},\"5725405:DOC\",{\"_185\":3013,\"_2681\":1216,\"_2682\":2683,\"_2684\":3014,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3015,\"_2559\":2560,\"_2688\":136},6281,\"debug-dubbo-api\",{},\"5725407:DOC\",{\"_185\":3018,\"_2681\":1222,\"_2682\":2683,\"_2684\":3019,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3020,\"_2559\":2560,\"_2688\":136},6282,\"dubbo-api-documentation\",{},\"5838988:DOC\",{\"_185\":3023,\"_2681\":1234,\"_2682\":2683,\"_2684\":3024,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3025,\"_2559\":2560,\"_2688\":136},6283,\"tcp-socket\",{},\"5838989:DOC\",{\"_185\":3028,\"_2681\":1240,\"_2682\":2683,\"_2684\":3029,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3030,\"_2559\":2560,\"_2688\":136},6284,\"message-data-processor\",{},\"5651112:DOC\",{\"_185\":3033,\"_2681\":1450,\"_2682\":2683,\"_2684\":3034,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3035,\"_2559\":2560,\"_2688\":136},6285,\"cicd\",{},\"5637749:DOC\",{\"_185\":3038,\"_2681\":1427,\"_2682\":2683,\"_2684\":3039,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3040,\"_2559\":2560,\"_2688\":136},6287,\"apifox-cli\",{},\"5637752:DOC\",{\"_185\":3043,\"_2681\":1432,\"_2682\":2683,\"_2684\":3044,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3045,\"_2559\":2560,\"_2688\":136},6288,\"install-and-run-cli\",{},\"5637756:DOC\",{\"_185\":3048,\"_2681\":1438,\"_2682\":2683,\"_2684\":3049,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3050,\"_2559\":2560,\"_2688\":136},6289,\"cli-command-options\",{},\"5651150:DOC\",{\"_185\":3053,\"_2681\":1456,\"_2682\":2683,\"_2684\":3054,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3055,\"_2559\":2560,\"_2688\":136},6290,\"integration-with-jenkins\",{},\"5651151:DOC\",{\"_185\":3058,\"_2681\":1462,\"_2682\":2683,\"_2684\":3059,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3060,\"_2559\":2560,\"_2688\":136},6291,\"integration-with-gitlab\",{},\"5651156:DOC\",{\"_185\":3063,\"_2681\":1468,\"_2682\":2683,\"_2684\":3064,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3065,\"_2559\":2560,\"_2688\":136},6292,\"integration-with-github-actions\",{},\"5651160:DOC\",{\"_185\":3068,\"_2681\":1474,\"_2682\":2683,\"_2684\":3069,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3070,\"_2559\":2560,\"_2688\":136},6293,\"integration-with-other-ci-cd-platforms\",{},\"6917191:DOC\",{\"_185\":3073,\"_2681\":1480,\"_2682\":2683,\"_2684\":3074,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3075,\"_2559\":2560,\"_2688\":136},6294,\"git-commit-triggered-testing\",{},\"5705538:DOC\",{\"_185\":3078,\"_2681\":1595,\"_2682\":2683,\"_2684\":3079,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3080,\"_2559\":2560,\"_2688\":136},6295,\"api-version\",{},\"5652692:DOC\",{\"_185\":3083,\"_2681\":1498,\"_2682\":2683,\"_2684\":3084,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3085,\"_2559\":2560,\"_2688\":136},6296,\"quick-share\",{},\"5701987:DOC\",{\"_185\":3088,\"_2681\":1504,\"_2682\":2683,\"_2684\":3089,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3090,\"_2559\":2560,\"_2688\":136},6297,\"view-api-documentation\",{},\"5701992:DOC\",{\"_185\":3093,\"_2681\":1510,\"_2682\":2683,\"_2684\":3094,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3095,\"_2559\":2560,\"_2688\":136},6298,\"publish-documentation-site\",{},\"5702004:DOC\",{\"_185\":3098,\"_2681\":1516,\"_2682\":2683,\"_2684\":3099,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3100,\"_2559\":2560,\"_2688\":136},6299,\"page-layout-settings\",{},\"5702008:DOC\",{\"_185\":3103,\"_2681\":1529,\"_2682\":2683,\"_2684\":3104,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3105,\"_2559\":2560,\"_2688\":136},6300,\"custom-domain\",{},\"6464590:DOC\",{\"_185\":3108,\"_2681\":1535,\"_2682\":2683,\"_2684\":3109,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3110,\"_2559\":2560,\"_2688\":136},6301,\"ai-features\",{},\"5702007:DOC\",{\"_185\":3113,\"_2681\":1541,\"_2682\":2683,\"_2684\":3114,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3115,\"_2559\":2560,\"_2688\":136},6303,\"seo-settings\",{},\"5702016:DOC\",{\"_185\":3118,\"_2681\":1553,\"_2682\":2683,\"_2684\":3119,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3120,\"_2559\":2560,\"_2688\":136},6304,\"documentation-site-search-settings\",{},\"5830046:DOC\",{\"_185\":3123,\"_2681\":1559,\"_2682\":2683,\"_2684\":3124,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3125,\"_2559\":2560,\"_2688\":136},6305,\"cross-origin-proxy\",{},\"5735413:DOC\",{\"_185\":3128,\"_2681\":1565,\"_2682\":2683,\"_2684\":3129,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3130,\"_2559\":2560,\"_2688\":136},6306,\"google-analytics-integration\",{},\"5701998:DOC\",{\"_185\":3133,\"_2681\":1571,\"_2682\":2683,\"_2684\":3134,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3135,\"_2559\":2560,\"_2688\":136},6307,\"documentation-sidebar-settings\",{},\"5702006:DOC\",{\"_185\":3138,\"_2681\":1577,\"_2682\":2683,\"_2684\":3139,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3140,\"_2559\":2560,\"_2688\":136},6308,\"documentation-visibility-settings\",{},\"5702014:DOC\",{\"_185\":3143,\"_2681\":1583,\"_2682\":2683,\"_2684\":3144,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3145,\"_2559\":2560,\"_2688\":136},6309,\"url-link-conventions\",{},\"5705539:DOC\",{\"_185\":3148,\"_2681\":1600,\"_2682\":2683,\"_2684\":3149,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3150,\"_2559\":2560,\"_2688\":136},6310,\"create-api-version\",{},\"5705542:DOC\",{\"_185\":3153,\"_2681\":1606,\"_2682\":2683,\"_2684\":3154,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3155,\"_2559\":2560,\"_2688\":136},6311,\"publish-api-version\",{},\"5705545:DOC\",{\"_185\":3158,\"_2681\":1612,\"_2682\":2683,\"_2684\":3159,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3160,\"_2559\":2560,\"_2688\":136},6312,\"quick-share-api-version\",{},\"5706205:DOC\",{\"_185\":3163,\"_2681\":1624,\"_2682\":2683,\"_2684\":3164,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3165,\"_2559\":2560,\"_2688\":136},6313,\"sprint-branch\",{},\"5706206:DOC\",{\"_185\":3168,\"_2681\":1629,\"_2682\":2683,\"_2684\":3169,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3170,\"_2559\":2560,\"_2688\":136},6314,\"create-sprint-branch\",{},\"5706208:DOC\",{\"_185\":3173,\"_2681\":1635,\"_2682\":2683,\"_2684\":3174,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3175,\"_2559\":2560,\"_2688\":136},6315,\"api-changes-in-sprint-branch\",{},\"5706211:DOC\",{\"_185\":3178,\"_2681\":1641,\"_2682\":2683,\"_2684\":3179,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3180,\"_2559\":2560,\"_2688\":136},6316,\"test-api-in-sprint-branch\",{},\"5706212:DOC\",{\"_185\":3183,\"_2681\":1647,\"_2682\":2683,\"_2684\":3184,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3185,\"_2559\":2560,\"_2688\":136},6317,\"merge-sprint-branch\",{},\"5706216:DOC\",{\"_185\":3188,\"_2681\":1653,\"_2682\":2683,\"_2684\":3189,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3190,\"_2559\":2560,\"_2688\":136},6318,\"manage-sprint-branch\",{},\"5743620:DOC\",{\"_185\":3193,\"_2681\":1901,\"_2682\":2683,\"_2684\":3194,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3195,\"_2559\":2560,\"_2688\":136},6319,\"apifox-idea-plugin\",{},\"5139742:DOC\",{\"_185\":3198,\"_2681\":1906,\"_2682\":2683,\"_2684\":3199,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3200,\"_2559\":2560,\"_2688\":136},6320,\"generate-api-docs-with-idea\",{},\"5140509:DOC\",{\"_185\":3203,\"_2681\":1912,\"_2682\":2683,\"_2684\":3204,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3205,\"_2559\":2560,\"_2688\":136},6321,\"generate-data-schemas-with-idea\",{},\"5801734:DOC\",{\"_185\":3208,\"_2681\":1977,\"_2682\":2683,\"_2684\":3209,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3210,\"_2559\":2560,\"_2688\":136},6322,\"apifox-idea-plugin-faqs\",{},\"5807583:DOC\",{\"_185\":3213,\"_2681\":1988,\"_2682\":2683,\"_2684\":3214,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3215,\"_2559\":2560,\"_2688\":136},6323,\"chrome-extension\",{},\"5807584:DOC\",{\"_185\":3218,\"_2681\":1995,\"_2682\":2683,\"_2684\":3219,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3220,\"_2559\":2560,\"_2688\":136},6324,\"microsoft-edge-extension\",{},\"6875653:DOC\",{\"_185\":3223,\"_2681\":2008,\"_2682\":2683,\"_2684\":3224,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3225,\"_2559\":2560,\"_2688\":136},6325,\"apifox-ai\",{},\"6875656:DOC\",{\"_185\":3228,\"_2681\":2014,\"_2682\":2683,\"_2684\":3229,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3230,\"_2559\":2560,\"_2688\":136},6326,\"enable-ai-features\",{},\"6875667:DOC\",{\"_185\":3233,\"_2681\":2020,\"_2682\":2683,\"_2684\":3234,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3235,\"_2559\":2560,\"_2688\":136},6327,\"generate-data-schemas-with-ai\",{},\"6876907:DOC\",{\"_185\":3238,\"_2681\":2038,\"_2682\":2683,\"_2684\":3239,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3240,\"_2559\":2560,\"_2688\":136},6328,\"apifox-ai-faqs\",{},\"5722982:DOC\",{\"_185\":3243,\"_2681\":2150,\"_2682\":2683,\"_2684\":3244,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3245,\"_2559\":2560,\"_2688\":136},6329,\"account-settings\",{},\"5723694:DOC\",{\"_185\":3248,\"_2681\":2156,\"_2682\":2683,\"_2684\":3249,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3250,\"_2559\":2560,\"_2688\":136},6330,\"api-access-token\",{},\"5722984:DOC\",{\"_185\":3253,\"_2681\":2168,\"_2682\":2683,\"_2684\":3254,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3255,\"_2559\":2560,\"_2688\":136},6331,\"language-settings\",{},\"5722988:DOC\",{\"_185\":3258,\"_2681\":2174,\"_2682\":2683,\"_2684\":3259,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3260,\"_2559\":2560,\"_2688\":136},6332,\"keyboard-shortcuts\",{},\"5722987:DOC\",{\"_185\":3263,\"_2681\":2180,\"_2682\":2683,\"_2684\":3264,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3265,\"_2559\":2560,\"_2688\":136},6333,\"network-proxy\",{},\"5722985:DOC\",{\"_185\":3268,\"_2681\":2186,\"_2682\":2683,\"_2684\":3269,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3270,\"_2559\":2560,\"_2688\":136},6334,\"data-backup-restore\",{},\"5722990:DOC\",{\"_185\":3273,\"_2681\":2192,\"_2682\":2683,\"_2684\":3274,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3275,\"_2559\":2560,\"_2688\":136},6335,\"update-apifox\",{},\"6758815:DOC\",{\"_185\":3278,\"_2681\":2198,\"_2682\":2683,\"_2684\":3279,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3280,\"_2559\":2560,\"_2688\":136},6336,\"experimental-features\",{},\"5734439:DOC\",{\"_185\":3283,\"_2681\":2293,\"_2682\":2683,\"_2684\":3284,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3285,\"_2559\":2560,\"_2688\":136},6337,\"service-agreement\",{},\"5734501:DOC\",{\"_185\":3288,\"_2681\":2299,\"_2682\":2683,\"_2684\":3289,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3290,\"_2559\":2560,\"_2688\":136},6338,\"privacy-policy\",{},\"5734506:DOC\",{\"_185\":3293,\"_2681\":2305,\"_2682\":2683,\"_2684\":3294,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3295,\"_2559\":2560,\"_2688\":136},6339,\"sla\",{},\"5723921:DOC\",{\"_185\":3298,\"_2681\":2317,\"_2682\":2683,\"_2684\":3299,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3300,\"_2559\":2560,\"_2688\":136},6340,\"api-first-design\",{},\"5723977:DOC\",{\"_185\":3303,\"_2681\":2323,\"_2682\":2683,\"_2684\":3304,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3305,\"_2559\":2560,\"_2688\":136},6341,\"json-schema\",{},\"5725287:DOC\",{\"_185\":3308,\"_2681\":2329,\"_2682\":2683,\"_2684\":3309,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3310,\"_2559\":2560,\"_2688\":136},6342,\"jsonpath\",{},\"5725289:DOC\",{\"_185\":3313,\"_2681\":2335,\"_2682\":2683,\"_2684\":3314,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3315,\"_2559\":2560,\"_2688\":136},6343,\"xpath\",{},\"5725364:DOC\",{\"_185\":3318,\"_2681\":2341,\"_2682\":2683,\"_2684\":3319,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3320,\"_2559\":2560,\"_2688\":136},6344,\"apifox-markdown\",{},\"5725296:DOC\",{\"_185\":3323,\"_2681\":2347,\"_2682\":2683,\"_2684\":124,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3324,\"_2559\":2560,\"_2688\":136},6345,{},\"5725291:DOC\",{\"_185\":3327,\"_2681\":2353,\"_2682\":2683,\"_2684\":3328,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3329,\"_2559\":2560,\"_2688\":136},6346,\"regex\",{},\"5725302:DOC\",{\"_185\":3332,\"_2681\":2359,\"_2682\":2683,\"_2684\":3333,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3334,\"_2559\":2560,\"_2688\":136},6347,\"install-java\",{},\"5725306:DOC\",{\"_185\":3337,\"_2681\":2365,\"_2682\":2683,\"_2684\":3338,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3339,\"_2559\":2560,\"_2688\":136},6348,\"runner-environment\",{},\"5725305:DOC\",{\"_185\":3342,\"_2681\":2371,\"_2682\":2683,\"_2684\":3343,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3344,\"_2559\":2560,\"_2688\":136},6349,\"programming-data-types\",{},\"5725299:DOC\",{\"_185\":3347,\"_2681\":2377,\"_2682\":2683,\"_2684\":3348,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3349,\"_2559\":2560,\"_2688\":136},6350,\"socket-packet-fragmentation-and-merging\",{},\"5835748:DOC\",{\"_185\":3352,\"_2681\":2383,\"_2682\":2683,\"_2684\":3353,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3354,\"_2559\":2560,\"_2688\":136},6351,\"glossary\",{},\"5723948:DOC\",{\"_185\":3357,\"_2681\":2395,\"_2682\":2683,\"_2684\":3358,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3359,\"_2559\":2560,\"_2688\":136},6353,\"apifox-openapi-swagger-extension\",{},\"5723950:DOC\",{\"_185\":3362,\"_2681\":2400,\"_2682\":2683,\"_2684\":2401,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3363,\"_2559\":2560,\"_2688\":136},6354,{},\"5723952:DOC\",{\"_185\":3366,\"_2681\":2406,\"_2682\":2683,\"_2684\":2407,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3367,\"_2559\":2560,\"_2688\":136},6355,{},\"5723955:DOC\",{\"_185\":3370,\"_2681\":2412,\"_2682\":2683,\"_2684\":2413,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3371,\"_2559\":2560,\"_2688\":136},6356,{},\"5723957:DOC\",{\"_185\":3374,\"_2681\":2418,\"_2682\":2683,\"_2684\":2419,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3375,\"_2559\":2560,\"_2688\":136},6357,{},\"5723963:DOC\",{\"_185\":3378,\"_2681\":2430,\"_2682\":2683,\"_2684\":3379,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3380,\"_2559\":2560,\"_2688\":136},6358,\"apifox-json-schema-extension\",{},\"5723969:DOC\",{\"_185\":3383,\"_2681\":2435,\"_2682\":2683,\"_2684\":2436,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3384,\"_2559\":2560,\"_2688\":136},6359,{},\"5723972:DOC\",{\"_185\":3387,\"_2681\":2441,\"_2682\":2683,\"_2684\":2442,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3388,\"_2559\":2560,\"_2688\":136},6360,{},\"5723964:DOC\",{\"_185\":3391,\"_2681\":2447,\"_2682\":2683,\"_2684\":2448,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3392,\"_2559\":2560,\"_2688\":136},6361,{},\"5778140:DOC\",{\"_185\":3395,\"_2681\":3396,\"_2682\":2683,\"_2684\":3397,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3398,\"_2559\":2560,\"_2688\":136},6362,5778140,\"dynamic-value-expressions\",{},\"5139642:DOC\",{\"_185\":3401,\"_2681\":352,\"_2682\":2683,\"_2684\":3402,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3403,\"_2559\":2560,\"_2688\":136},6367,\"create-api\",{},\"5140339:DOC\",{\"_185\":3406,\"_2681\":358,\"_2682\":2683,\"_2684\":3407,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3408,\"_2559\":2560,\"_2688\":136},6368,\"send-api-request\",{},\"5218722:DOC\",{\"_185\":3411,\"_2681\":364,\"_2682\":2683,\"_2684\":3412,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3413,\"_2559\":2560,\"_2688\":136},6369,\"quick-request\",{},\"5140346:DOC\",{\"_185\":3416,\"_2681\":371,\"_2682\":2683,\"_2684\":3417,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3418,\"_2559\":2560,\"_2688\":136},6370,\"add-assertions\",{},\"5140350:DOC\",{\"_185\":3421,\"_2681\":377,\"_2682\":2683,\"_2684\":3422,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3423,\"_2559\":2560,\"_2688\":136},6371,\"create-test-scenario\",{},\"5140353:DOC\",{\"_185\":3426,\"_2681\":383,\"_2682\":2683,\"_2684\":3427,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3428,\"_2559\":2560,\"_2688\":136},6372,\"share-api-documentation\",{},\"5140360:DOC\",{\"_185\":3431,\"_2681\":389,\"_2682\":2683,\"_2684\":3432,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3433,\"_2559\":2560,\"_2688\":136},6373,\"learn-more\",{},\"5146289:DOC\",{\"_185\":3436,\"_2681\":543,\"_2682\":2683,\"_2684\":3437,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3438,\"_2559\":2560,\"_2688\":136},6374,\"request-proxy-in-web\",{},\"5903157:DOC\",{\"_185\":3441,\"_2681\":549,\"_2682\":2683,\"_2684\":3442,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3443,\"_2559\":2560,\"_2688\":136},6375,\"share-document-proxy\",{},\"5903163:DOC\",{\"_185\":3446,\"_2681\":555,\"_2682\":2683,\"_2684\":3447,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3448,\"_2559\":2560,\"_2688\":136},6376,\"client-side-request-proxy\",{},\"5220222:DOC\",{\"_185\":3451,\"_2681\":708,\"_2682\":2683,\"_2684\":3452,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3453,\"_2559\":2560,\"_2688\":136},6377,\"create-api-project\",{},\"5220223:DOC\",{\"_185\":3456,\"_2681\":714,\"_2682\":2683,\"_2684\":3457,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3458,\"_2559\":2560,\"_2688\":136},6378,\"create-an-api\",{},\"6121960:DOC\",{\"_185\":3461,\"_2681\":732,\"_2682\":2683,\"_2684\":3462,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3463,\"_2559\":2560,\"_2688\":136},6379,\"multi-example-request-body\",{},\"5220225:DOC\",{\"_185\":3466,\"_2681\":738,\"_2682\":2683,\"_2684\":3467,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3468,\"_2559\":2560,\"_2688\":136},6380,\"response-components\",{},\"5220226:DOC\",{\"_185\":3471,\"_2681\":744,\"_2682\":2683,\"_2684\":3472,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3473,\"_2559\":2560,\"_2688\":136},6381,\"common-fields\",{},\"5802626:DOC\",{\"_185\":3476,\"_2681\":750,\"_2682\":2683,\"_2684\":3477,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3478,\"_2559\":2560,\"_2688\":136},6382,\"global-parameters\",{},\"5220227:DOC\",{\"_185\":3481,\"_2681\":756,\"_2682\":2683,\"_2684\":3482,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3483,\"_2559\":2560,\"_2688\":136},6383,\"api-history\",{},\"5220228:DOC\",{\"_185\":3486,\"_2681\":768,\"_2682\":2683,\"_2684\":3487,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3488,\"_2559\":2560,\"_2688\":136},6384,\"bulk-operations\",{},\"5220231:DOC\",{\"_185\":3491,\"_2681\":780,\"_2682\":2683,\"_2684\":3492,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3493,\"_2559\":2560,\"_2688\":136},6385,\"data-schemas\",{},\"5220234:DOC\",{\"_185\":3496,\"_2681\":786,\"_2682\":2683,\"_2684\":3497,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3498,\"_2559\":2560,\"_2688\":136},6386,\"create-data-schema\",{},\"5220237:DOC\",{\"_185\":3501,\"_2681\":792,\"_2682\":2683,\"_2684\":3502,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3503,\"_2559\":2560,\"_2688\":136},6387,\"build-data-schemas\",{},\"5220243:DOC\",{\"_185\":3506,\"_2681\":798,\"_2682\":2683,\"_2684\":3507,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3508,\"_2559\":2560,\"_2688\":136},6388,\"generate-from-json\",{},\"5808428:DOC\",{\"_185\":3511,\"_2681\":804,\"_2682\":2683,\"_2684\":3512,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3513,\"_2559\":2560,\"_2688\":136},6389,\"advanced-data-types\",{},\"6367782:DOC\",{\"_185\":3516,\"_2681\":828,\"_2682\":2683,\"_2684\":3517,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3518,\"_2559\":2560,\"_2688\":136},6390,\"security-schemes\",{},\"6367853:DOC\",{\"_185\":3521,\"_2681\":833,\"_2682\":2683,\"_2684\":3522,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3523,\"_2559\":2560,\"_2688\":136},6391,\"create-security-scheme\",{},\"6368405:DOC\",{\"_185\":3526,\"_2681\":839,\"_2682\":2683,\"_2684\":3527,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3528,\"_2559\":2560,\"_2688\":136},6392,\"use-security-schemes\",{},\"6368428:DOC\",{\"_185\":3531,\"_2681\":845,\"_2682\":2683,\"_2684\":3532,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3533,\"_2559\":2560,\"_2688\":136},6393,\"security-schemes-in-docs\",{},\"5220249:DOC\",{\"_185\":3536,\"_2681\":857,\"_2682\":2683,\"_2684\":3537,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3538,\"_2559\":2560,\"_2688\":136},6394,\"api-fields\",{},\"5220252:DOC\",{\"_185\":3541,\"_2681\":863,\"_2682\":2683,\"_2684\":3542,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3543,\"_2559\":2560,\"_2688\":136},6395,\"api-status\",{},\"5830326:DOC\",{\"_185\":3546,\"_2681\":869,\"_2682\":2683,\"_2684\":3547,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3548,\"_2559\":2560,\"_2688\":136},6396,\"linked-test-scenarios\",{},\"5220254:DOC\",{\"_185\":3551,\"_2681\":875,\"_2682\":2683,\"_2684\":3552,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3553,\"_2559\":2560,\"_2688\":136},6397,\"parameter-list-appearance\",{},\"5220255:DOC\",{\"_185\":3556,\"_2681\":881,\"_2682\":2683,\"_2684\":3557,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3558,\"_2559\":2560,\"_2688\":136},6398,\"api-unique-identifier\",{},\"5220264:DOC\",{\"_185\":3561,\"_2681\":898,\"_2682\":2683,\"_2684\":3562,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3563,\"_2559\":2560,\"_2688\":136},6399,\"generate-request\",{},\"5220266:DOC\",{\"_185\":3566,\"_2681\":904,\"_2682\":2683,\"_2684\":3567,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3568,\"_2559\":2560,\"_2688\":136},6400,\"send-request\",{},\"5830564:DOC\",{\"_185\":3571,\"_2681\":910,\"_2682\":2683,\"_2684\":3572,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3573,\"_2559\":2560,\"_2688\":136},6401,\"request-history\",{},\"5220267:DOC\",{\"_185\":3576,\"_2681\":916,\"_2682\":2683,\"_2684\":3577,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3578,\"_2559\":2560,\"_2688\":136},6402,\"api-test-cases\",{},\"5220268:DOC\",{\"_185\":3581,\"_2681\":928,\"_2682\":2683,\"_2684\":3582,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3583,\"_2559\":2560,\"_2688\":136},6403,\"dynamic-values\",{},\"5220269:DOC\",{\"_185\":3586,\"_2681\":934,\"_2682\":2683,\"_2684\":3587,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3588,\"_2559\":2560,\"_2688\":136},6404,\"validate-response\",{},\"5220270:DOC\",{\"_185\":3591,\"_2681\":940,\"_2682\":2683,\"_2684\":3592,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3593,\"_2559\":2560,\"_2688\":136},6405,\"design-and-request-mode\",{},\"5220271:DOC\",{\"_185\":3596,\"_2681\":946,\"_2682\":2683,\"_2684\":3597,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3598,\"_2559\":2560,\"_2688\":136},6406,\"generate-code\",{},\"5537408:DOC\",{\"_185\":3601,\"_2681\":958,\"_2682\":2683,\"_2684\":3602,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3603,\"_2559\":2560,\"_2688\":136},6407,\"environments-and-variables\",{},\"5537409:DOC\",{\"_185\":3606,\"_2681\":969,\"_2682\":2683,\"_2684\":3607,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3608,\"_2559\":2560,\"_2688\":136},6408,\"global-environment-session-variables\",{},\"5537410:DOC\",{\"_185\":3611,\"_2681\":963,\"_2682\":2683,\"_2684\":3612,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3613,\"_2559\":2560,\"_2688\":136},6409,\"environments-and-services\",{},\"5831220:DOC\",{\"_185\":3616,\"_2681\":985,\"_2682\":2683,\"_2684\":3617,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3618,\"_2559\":2560,\"_2688\":136},6410,\"vault-secrets\",{},\"5580531:DOC\",{\"_185\":3621,\"_2681\":997,\"_2682\":2683,\"_2684\":3622,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3623,\"_2559\":2560,\"_2688\":136},6411,\"pre-post-processors\",{},\"5580821:DOC\",{\"_185\":3626,\"_2681\":1003,\"_2682\":2683,\"_2684\":3627,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3628,\"_2559\":2560,\"_2688\":136},6412,\"assertions\",{},\"5580967:DOC\",{\"_185\":3631,\"_2681\":1009,\"_2682\":2683,\"_2684\":3632,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3633,\"_2559\":2560,\"_2688\":136},6413,\"extract-variables\",{},\"5580968:DOC\",{\"_185\":3636,\"_2681\":1015,\"_2682\":2683,\"_2684\":3637,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3638,\"_2559\":2560,\"_2688\":136},6414,\"wait-time\",{},\"5580972:DOC\",{\"_185\":3641,\"_2681\":1027,\"_2682\":2683,\"_2684\":3642,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3643,\"_2559\":2560,\"_2688\":136},6415,\"database\",{},\"6797991:DOC\",{\"_185\":3646,\"_2681\":1032,\"_2682\":2683,\"_2684\":3647,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3648,\"_2559\":2560,\"_2688\":136},6416,\"mysql\",{},\"5580974:DOC\",{\"_185\":3651,\"_2681\":1038,\"_2682\":2683,\"_2684\":3652,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3653,\"_2559\":2560,\"_2688\":136},6417,\"mongodb\",{},\"5580976:DOC\",{\"_185\":3656,\"_2681\":1044,\"_2682\":2683,\"_2684\":3657,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3658,\"_2559\":2560,\"_2688\":136},6418,\"redis\",{},\"5580981:DOC\",{\"_185\":3661,\"_2681\":1050,\"_2682\":2683,\"_2684\":3662,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3663,\"_2559\":2560,\"_2688\":136},6419,\"oracle\",{},\"5580993:DOC\",{\"_185\":3666,\"_2681\":1062,\"_2682\":2683,\"_2684\":3667,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3668,\"_2559\":2560,\"_2688\":136},6420,\"scripts\",{},\"5580999:DOC\",{\"_185\":3671,\"_2681\":1067,\"_2682\":2683,\"_2684\":3672,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3673,\"_2559\":2560,\"_2688\":136},6421,\"pre-request-scripts\",{},\"5581000:DOC\",{\"_185\":3676,\"_2681\":1073,\"_2682\":2683,\"_2684\":3677,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3678,\"_2559\":2560,\"_2688\":136},6422,\"post-request-scripts\",{},\"5581002:DOC\",{\"_185\":3681,\"_2681\":1079,\"_2682\":2683,\"_2684\":3682,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3683,\"_2559\":2560,\"_2688\":136},6423,\"common-scripts\",{},\"5580997:DOC\",{\"_185\":3686,\"_2681\":1085,\"_2682\":2683,\"_2684\":3687,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3688,\"_2559\":2560,\"_2688\":136},6424,\"postman-script-api\",{},\"5586307:DOC\",{\"_185\":3691,\"_2681\":1091,\"_2682\":2683,\"_2684\":3692,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3693,\"_2559\":2560,\"_2688\":136},6425,\"js-libraries\",{},\"5586317:DOC\",{\"_185\":3696,\"_2681\":1097,\"_2682\":2683,\"_2684\":3697,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3698,\"_2559\":2560,\"_2688\":136},6426,\"response-data-visualization\",{},\"5586304:DOC\",{\"_185\":3701,\"_2681\":1103,\"_2682\":2683,\"_2684\":3702,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3703,\"_2559\":2560,\"_2688\":136},6427,\"call-external-programs\",{},\"5589198:DOC\",{\"_185\":3706,\"_2681\":1115,\"_2682\":2683,\"_2684\":3707,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3708,\"_2559\":2560,\"_2688\":136},6428,\"assertion-examples\",{},\"5589203:DOC\",{\"_185\":3711,\"_2681\":1121,\"_2682\":2683,\"_2684\":3712,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3713,\"_2559\":2560,\"_2688\":136},6429,\"use-variables-in-scripts\",{},\"5589215:DOC\",{\"_185\":3716,\"_2681\":1127,\"_2682\":2683,\"_2684\":3717,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3718,\"_2559\":2560,\"_2688\":136},6430,\"access-modify-request-data\",{},\"5589821:DOC\",{\"_185\":3721,\"_2681\":1257,\"_2682\":2683,\"_2684\":3722,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3723,\"_2559\":2560,\"_2688\":136},6431,\"smart-mock\",{},\"5589823:DOC\",{\"_185\":3726,\"_2681\":1263,\"_2682\":2683,\"_2684\":3727,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3728,\"_2559\":2560,\"_2688\":136},6432,\"custom-mock\",{},\"5589825:DOC\",{\"_185\":3731,\"_2681\":1269,\"_2682\":2683,\"_2684\":3732,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3733,\"_2559\":2560,\"_2688\":136},6433,\"mock-priority-rules\",{},\"5589827:DOC\",{\"_185\":3736,\"_2681\":1275,\"_2682\":2683,\"_2684\":3737,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3738,\"_2559\":2560,\"_2688\":136},6434,\"mock-scripts\",{},\"5589829:DOC\",{\"_185\":3741,\"_2681\":1281,\"_2682\":2683,\"_2684\":3742,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3743,\"_2559\":2560,\"_2688\":136},6436,\"cloud-mock\",{},\"5589830:DOC\",{\"_185\":3746,\"_2681\":1287,\"_2682\":2683,\"_2684\":3747,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3748,\"_2559\":2560,\"_2688\":136},6437,\"runner-mock\",{},\"5629565:DOC\",{\"_185\":3751,\"_2681\":1310,\"_2682\":2683,\"_2684\":3752,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3753,\"_2559\":2560,\"_2688\":136},6438,\"new-test-scenario\",{},\"5629567:DOC\",{\"_185\":3756,\"_2681\":1315,\"_2682\":2683,\"_2684\":3757,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3758,\"_2559\":2560,\"_2688\":136},6439,\"pass-data-between-test-steps\",{},\"5629569:DOC\",{\"_185\":3761,\"_2681\":1321,\"_2682\":2683,\"_2684\":3762,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3763,\"_2559\":2560,\"_2688\":136},6440,\"flow-control-conditions\",{},\"5629571:DOC\",{\"_185\":3766,\"_2681\":1327,\"_2682\":2683,\"_2684\":3767,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3768,\"_2559\":2560,\"_2688\":136},6441,\"sync-from-endpoint-or-test-case\",{},\"5629575:DOC\",{\"_185\":3771,\"_2681\":1333,\"_2682\":2683,\"_2684\":3772,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3773,\"_2559\":2560,\"_2688\":136},6442,\"import-apis-or-cases-cross-projects\",{},\"5629572:DOC\",{\"_185\":3776,\"_2681\":1339,\"_2682\":2683,\"_2684\":3777,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3778,\"_2559\":2560,\"_2688\":136},6443,\"export-test-scenario-data\",{},\"5632037:DOC\",{\"_185\":3781,\"_2681\":1351,\"_2682\":2683,\"_2684\":3782,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3783,\"_2559\":2560,\"_2688\":136},6444,\"run-test-scenarios\",{},\"5635578:DOC\",{\"_185\":3786,\"_2681\":1356,\"_2682\":2683,\"_2684\":3787,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3788,\"_2559\":2560,\"_2688\":136},6445,\"batch-run-test-scenarios\",{},\"5635110:DOC\",{\"_185\":3791,\"_2681\":1362,\"_2682\":2683,\"_2684\":3792,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3793,\"_2559\":2560,\"_2688\":136},6446,\"data-driven-testing\",{},\"5635614:DOC\",{\"_185\":3796,\"_2681\":1368,\"_2682\":2683,\"_2684\":3797,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3798,\"_2559\":2560,\"_2688\":136},6447,\"scheduled-tasks\",{},\"5635652:DOC\",{\"_185\":3801,\"_2681\":1374,\"_2682\":2683,\"_2684\":3802,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3803,\"_2559\":2560,\"_2688\":136},6448,\"manage-cross-project-environments\",{},\"5615180:DOC\",{\"_185\":3806,\"_2681\":1386,\"_2682\":2683,\"_2684\":3807,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3808,\"_2559\":2560,\"_2688\":136},6449,\"test-reports\",{},\"5637211:DOC\",{\"_185\":3811,\"_2681\":1403,\"_2682\":2683,\"_2684\":3812,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3813,\"_2559\":2560,\"_2688\":136},6450,\"integration-testing\",{},\"5637213:DOC\",{\"_185\":3816,\"_2681\":1397,\"_2682\":2683,\"_2684\":3817,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3818,\"_2559\":2560,\"_2688\":136},6452,\"performance-testing\",{},\"5637215:DOC\",{\"_185\":3821,\"_2681\":1409,\"_2682\":2683,\"_2684\":3822,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3823,\"_2559\":2560,\"_2688\":136},6453,\"end-to-end-testing\",{},\"5637217:DOC\",{\"_185\":3826,\"_2681\":1415,\"_2682\":2683,\"_2684\":3827,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3828,\"_2559\":2560,\"_2688\":136},6454,\"regression-testing\",{},\"5712879:DOC\",{\"_185\":3831,\"_2681\":1671,\"_2682\":2683,\"_2684\":3832,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3833,\"_2559\":2560,\"_2688\":136},6455,\"management-center\",{},\"5712619:DOC\",{\"_185\":3836,\"_2681\":1677,\"_2682\":2683,\"_2684\":3837,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3838,\"_2559\":2560,\"_2688\":136},6456,\"onboard-team\",{},\"5713360:DOC\",{\"_185\":3841,\"_2681\":1689,\"_2682\":2683,\"_2684\":3842,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3843,\"_2559\":2560,\"_2688\":136},6457,\"member-roles-and-permissions\",{},\"5713261:DOC\",{\"_185\":3846,\"_2681\":1695,\"_2682\":2683,\"_2684\":3847,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3848,\"_2559\":2560,\"_2688\":136},6458,\"team-basic-operations\",{},\"5800393:DOC\",{\"_185\":3851,\"_2681\":1701,\"_2682\":2683,\"_2684\":3852,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3853,\"_2559\":2560,\"_2688\":136},6459,\"team-member-management\",{},\"5714000:DOC\",{\"_185\":3856,\"_2681\":1713,\"_2682\":2683,\"_2684\":3857,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3858,\"_2559\":2560,\"_2688\":136},6460,\"universal-runner\",{},\"5830068:DOC\",{\"_185\":3861,\"_2681\":1719,\"_2682\":2683,\"_2684\":3862,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3863,\"_2559\":2560,\"_2688\":136},6461,\"request-proxy-agent\",{},\"5817756:DOC\",{\"_185\":3866,\"_2681\":1725,\"_2682\":2683,\"_2684\":3867,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3868,\"_2559\":2560,\"_2688\":136},6462,\"team-variables\",{},\"5713996:DOC\",{\"_185\":3871,\"_2681\":1737,\"_2682\":2683,\"_2684\":3872,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3873,\"_2559\":2560,\"_2688\":136},6463,\"team-collaboration\",{},\"5713374:DOC\",{\"_185\":3876,\"_2681\":1749,\"_2682\":2683,\"_2684\":3877,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3878,\"_2559\":2560,\"_2688\":136},6464,\"project-basic-operations\",{},\"5800993:DOC\",{\"_185\":3881,\"_2681\":1755,\"_2682\":2683,\"_2684\":3882,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3883,\"_2559\":2560,\"_2688\":136},6465,\"project-member-management\",{},\"5714005:DOC\",{\"_185\":3886,\"_2681\":1767,\"_2682\":2683,\"_2684\":3887,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3888,\"_2559\":2560,\"_2688\":136},6466,\"message-notifications\",{},\"5714006:DOC\",{\"_185\":3891,\"_2681\":1773,\"_2682\":2683,\"_2684\":3892,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3893,\"_2559\":2560,\"_2688\":136},6467,\"notification-targets\",{},\"5714007:DOC\",{\"_185\":3896,\"_2681\":1779,\"_2682\":2683,\"_2684\":3897,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3898,\"_2559\":2560,\"_2688\":136},6468,\"notification-events\",{},\"5909682:DOC\",{\"_185\":3901,\"_2681\":1791,\"_2682\":2683,\"_2684\":3902,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3903,\"_2559\":2560,\"_2688\":136},6469,\"database-connections\",{},\"5715169:DOC\",{\"_185\":3906,\"_2681\":1815,\"_2682\":2683,\"_2684\":3907,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3908,\"_2559\":2560,\"_2688\":136},6470,\"sso\",{},\"5715170:DOC\",{\"_185\":3911,\"_2681\":1820,\"_2682\":2683,\"_2684\":3912,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3913,\"_2559\":2560,\"_2688\":136},6471,\"configure-sso-for-organization\",{},\"5715171:DOC\",{\"_185\":3916,\"_2681\":1826,\"_2682\":2683,\"_2684\":3917,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3918,\"_2559\":2560,\"_2688\":136},6472,\"manage-user-accounts\",{},\"5715174:DOC\",{\"_185\":3921,\"_2681\":1832,\"_2682\":2683,\"_2684\":3922,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3923,\"_2559\":2560,\"_2688\":136},6473,\"map-groups-to-teams\",{},\"5715175:DOC\",{\"_185\":3926,\"_2681\":1838,\"_2682\":2683,\"_2684\":3927,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3928,\"_2559\":2560,\"_2688\":136},6474,\"microsoft-entra-id\",{},\"5715176:DOC\",{\"_185\":3931,\"_2681\":1850,\"_2682\":2683,\"_2684\":3932,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3933,\"_2559\":2560,\"_2688\":136},6475,\"scim-user-management\",{},\"5715178:DOC\",{\"_185\":3936,\"_2681\":1855,\"_2682\":2683,\"_2684\":3937,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3938,\"_2559\":2560,\"_2688\":136},6476,\"scmi-microsoft-entra-id\",{},\"5715182:DOC\",{\"_185\":3941,\"_2681\":1866,\"_2682\":2683,\"_2684\":3942,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3943,\"_2559\":2560,\"_2688\":136},6477,\"self-hosted-runner\",{},\"5734556:DOC\",{\"_185\":3946,\"_2681\":2210,\"_2682\":2683,\"_2684\":3947,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3948,\"_2559\":2560,\"_2688\":136},6478,\"what-is-api-key\",{},\"5734558:DOC\",{\"_185\":3951,\"_2681\":2216,\"_2682\":2683,\"_2684\":3952,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3953,\"_2559\":2560,\"_2688\":136},6479,\"what-is-bearer-token\",{},\"5734559:DOC\",{\"_185\":3956,\"_2681\":2222,\"_2682\":2683,\"_2684\":3957,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3958,\"_2559\":2560,\"_2688\":136},6480,\"what-is-jwt\",{},\"5734560:DOC\",{\"_185\":3961,\"_2681\":2228,\"_2682\":2683,\"_2684\":3962,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3963,\"_2559\":2560,\"_2688\":136},6481,\"what-is-basic-auth\",{},\"5734561:DOC\",{\"_185\":3966,\"_2681\":2234,\"_2682\":2683,\"_2684\":3967,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3968,\"_2559\":2560,\"_2688\":136},6482,\"what-is-digest-auth\",{},\"5734563:DOC\",{\"_185\":3971,\"_2681\":2240,\"_2682\":2683,\"_2684\":3972,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3973,\"_2559\":2560,\"_2688\":136},6483,\"what-is-oauth-1\",{},\"5734564:DOC\",{\"_185\":3976,\"_2681\":2252,\"_2682\":2683,\"_2684\":3977,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3978,\"_2559\":2560,\"_2688\":136},6484,\"what-is-oauth-2\",{},\"5734566:DOC\",{\"_185\":3981,\"_2681\":2257,\"_2682\":2683,\"_2684\":3982,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3983,\"_2559\":2560,\"_2688\":136},6485,\"authorization-code-grant\",{},\"5734568:DOC\",{\"_185\":3986,\"_2681\":2263,\"_2682\":2683,\"_2684\":3987,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3988,\"_2559\":2560,\"_2688\":136},6486,\"authorization-code-grant-with-pkce\",{},\"5734569:DOC\",{\"_185\":3991,\"_2681\":2269,\"_2682\":2683,\"_2684\":3992,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3993,\"_2559\":2560,\"_2688\":136},6487,\"implicit-grant\",{},\"5734572:DOC\",{\"_185\":3996,\"_2681\":2275,\"_2682\":2683,\"_2684\":3997,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":3998,\"_2559\":2560,\"_2688\":136},6488,\"password-credentials-grant\",{},\"5734576:DOC\",{\"_185\":4001,\"_2681\":2281,\"_2682\":2683,\"_2684\":4002,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":4003,\"_2559\":2560,\"_2688\":136},6489,\"client-credentials-grant\",{},\"6970194:DOC\",{\"_185\":4006,\"_2681\":726,\"_2682\":2683,\"_2684\":4007,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":4008,\"_2559\":2560,\"_2688\":136},6499,\"module\",{},\"6997986:DOC\",{\"_185\":4011,\"_2681\":1522,\"_2682\":2683,\"_2684\":4012,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":4013,\"_2559\":2560,\"_2688\":136},6709,\"custom-css-js-html\",{},\"7035411:DOC\",{\"_185\":4016,\"_2681\":1890,\"_2682\":2683,\"_2684\":4017,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":4018,\"_2559\":2560,\"_2688\":136},6955,\"offline-space\",{},\"7036471:DOC\",{\"_185\":4021,\"_2681\":1192,\"_2682\":2683,\"_2684\":4022,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":4023,\"_2559\":2560,\"_2688\":136},6956,\"webhook\",{},\"7151314:DOC\",{\"_185\":4026,\"_2681\":720,\"_2682\":2683,\"_2684\":4027,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":4028,\"_2559\":2560,\"_2688\":136},7245,\"api-design-guidelines\",{},\"7150435:DOC\",{\"_185\":4031,\"_2681\":2026,\"_2682\":2683,\"_2684\":4032,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":4033,\"_2559\":2560,\"_2688\":136},7246,\"api-compliance-check\",{},\"7150436:DOC\",{\"_185\":4036,\"_2681\":2032,\"_2682\":2683,\"_2684\":4037,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":4038,\"_2559\":2560,\"_2688\":136},7247,\"field-naming\",{},\"7158896:DOC\",{\"_185\":4041,\"_2681\":816,\"_2682\":2683,\"_2684\":4042,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":4043,\"_2559\":2560,\"_2688\":136},7266,\"oneof-anyof-allof\",{},\"7380975:DOC\",{\"_185\":4046,\"_2681\":762,\"_2682\":2683,\"_2684\":4047,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":4048,\"_2559\":2560,\"_2688\":136},7918,\"api-comments\",{},\"7381499:DOC\",{\"_185\":4051,\"_2681\":2162,\"_2682\":2683,\"_2684\":4052,\"_26\":136,\"_19\":136,\"_23\":136,\"_2686\":4053,\"_2559\":2560,\"_2688\":136},7921,\"notifications\",{},\"itemPathPrefix\",{\"_4056\":136,\"_4057\":136,\"_2598\":136,\"_4058\":192},\"baseUrl\",\"versionSlug\",\"isSharedDocIndependentDomain\",\"appEnvMap\",{\"_4061\":148},\"appBrowserExtensionAdapterKey\",\"customCodes\",{\"_4064\":-7},\"javascript\",\"primaryColorVariable\",{\"_2482\":4067,\"_2484\":4068},\"--ui-primary-color-hover: var(--ui-primary-5);--ui-primary-color-active: var(--ui-primary-7);--ui-picker-basic-cell-hover-with-range-color: #36139c;--ui-picker-date-hover-range-border-color: #4e1be0;--ui-primary-base: #9373EE;--ui-primary-base-p: 147, 115, 238;--ui-primary-color-p: 147, 115, 238;--ui-primary-color: #9373EE;--ui-primary-10: #362b50;--ui-primary-9: #51407e;--ui-primary-8: #634e9b;--ui-primary-7: #7a60c4;--ui-primary-7-p: 122, 96, 196;--ui-primary-6: #9373EE;--ui-primary-6-p: 147, 115, 238;--ui-primary-5: #b499f4;--ui-primary-5-p: 180, 153, 244;--ui-primary-4: #cebaf8;--ui-primary-3: #dfd1fb;--ui-primary-2: #ebe2fc;--ui-primary-1: #f3eefe;--ui-primary-1-p: 243, 238, 254;--ui-primary-a: #f7f3fe;--ui-primary-b: #fbf9ff;--g-color-primary: #9373EE;--ui-btn-primary-color: #fff;--ui-lime-6-p: 160,217,17;--ui-orange-6-p: 250,140,22;--ui-yellow-6-p: 250,219,20;--ui-pink-6-p: 235,47,150;--ui-body-background: #ffffff;--theme-tars-item-active-bg: var(--ui-tree-node-selected-bg);--ifm-menu-color: var(--ui-text-color-sub);--ifm-menu-color-active: var(--ui-text-color-sub);--ifm-menu-color-background-hover: var(--ui-item-hover-bg);--theme-tars-border-color-split: var(--ui-border-color-split);\",\"--ui-primary-color-hover: var(--ui-primary-7);--ui-primary-color-active: var(--ui-primary-5);--ui-picker-basic-cell-hover-with-range-color: #312865;--ui-picker-date-hover-range-border-color: #4b3d9c;--ui-primary-base: #8276C9;--ui-primary-base-p: 130, 118, 201;--ui-primary-color-p: 130, 118, 201;--ui-primary-color: #8276C9;--ui-primary-10: #dcd7f0;--ui-primary-9: #a69bd9;--ui-primary-8: #b0a5dd;--ui-primary-7: #9c90d4;--ui-primary-7-p: 156, 144, 212;--ui-primary-6: #8276C9;--ui-primary-6-p: 130, 118, 201;--ui-primary-5: #635a96;--ui-primary-5-p: 99, 90, 150;--ui-primary-4: #49436c;--ui-primary-3: #393452;--ui-primary-2: #312d46;--ui-primary-1: #252333;--ui-primary-1-p: 37, 35, 51;--ui-primary-a: #21202d;--ui-primary-b: #1e1c28;--g-color-primary: #8276C9;--ui-btn-primary-color: #fff;--ui-lime-6-p: 160,217,17;--ui-orange-6-p: 250,140,22;--ui-yellow-6-p: 250,219,20;--ui-pink-6-p: 235,47,150;--ui-body-background: #1a1922;--theme-tars-item-active-bg: var(--ui-tree-node-selected-bg);--ifm-menu-color: var(--ui-text-color-sub);--ifm-menu-color-active: var(--ui-text-color-sub);--ifm-menu-color-background-hover: var(--ui-item-hover-bg);--theme-tars-border-color-split: var(--ui-border-color-split);\",\"routes/_index/route\",\"actionData\",\"errors\"]\n");</script><!--$?--><template id="B:1"></template><!--/$--></div><script>function $RC(a,b){a=document.getElementById(a);b=document.getElementById(b);b.parentNode.removeChild(b);if(a){a=a.previousSibling;var f=a.parentNode,c=a.nextSibling,e=0;do{if(c&&8===c.nodeType){var d=c.data;if("/$"===d)if(0===e)break;else e--;else"$"!==d&&"$?"!==d&&"$!"!==d||e++}d=c.nextSibling;f.removeChild(c);c=d}while(c);for(;b.firstChild;)f.insertBefore(b.firstChild,c);a.data="$";a._reactRetry&&a._reactRetry()}};$RC("B:0","S:0")</script><div hidden id="S:1"><script>window.__remixContext.streamController.close();</script></div><script>$RC("B:1","S:1")</script>
